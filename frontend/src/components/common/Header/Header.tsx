import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { logout } from '../../../store/slices/authSlice';
import { useAppDispatch, useAppSelector } from '../../../hooks/redux';
import { ShoppingCartIcon, HeartIcon, UserIcon, MagnifyingGlassIcon, Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';

const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showUserMenu, setShowUserMenu] = useState(false);
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const { isAuthenticated, user } = useAppSelector((state) => state.auth);
  const { totalItems } = useAppSelector((state) => state.cart);

  const handleLogout = () => {
    dispatch(logout());
    navigate('/');
    setShowUserMenu(false);
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/products?search=${encodeURIComponent(searchQuery.trim())}`);
      setSearchQuery('');
    }
  };

  const categories = [
    {
      name: 'MEN',
      link: '/products?category=men',
      submenu: ['Shirts', 'T-Shirts', 'Jeans', 'Formal Wear', 'Casual Wear', 'Accessories']
    },
    {
      name: 'WOMEN',
      link: '/products?category=women',
      submenu: ['Dresses', 'Tops', 'Ethnic Wear', 'Western Wear', 'Lingerie', 'Accessories']
    },
    {
      name: 'KIDS',
      link: '/products?category=kids',
      submenu: ['Boys', 'Girls', 'Baby', 'Toys', 'School Supplies']
    },
    {
      name: 'FOOTWEAR',
      link: '/products?category=footwear',
      submenu: ['Sneakers', 'Formal Shoes', 'Sandals', 'Boots', 'Sports Shoes']
    },
    {
      name: 'ACCESSORIES',
      link: '/products?category=accessories',
      submenu: ['Bags', 'Watches', 'Jewelry', 'Sunglasses', 'Belts']
    },
    {
      name: 'BEAUTY',
      link: '/products?category=beauty',
      submenu: ['Skincare', 'Makeup', 'Haircare', 'Fragrances', 'Personal Care']
    }
  ];

  return (
    <header className="bg-white shadow-sm sticky top-0 z-50">
      {/* Main Header */}
      <div className="border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            {/* Logo */}
            <div className="flex-shrink-0">
              <Link to="/" className="flex items-center group">
                <div className="relative">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-600 to-pink-600 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300">
                    <span className="text-white font-bold text-xl">MM</span>
                  </div>
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-yellow-400 rounded-full animate-pulse"></div>
                </div>
                <div className="ml-3">
                  <span className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                    Modern Metro
                  </span>
                  <div className="text-xs text-gray-500 font-medium">Fashion Forward</div>
                </div>
              </Link>
            </div>

            {/* Main Navigation - Desktop */}
            <nav className="hidden lg:flex items-center space-x-1">
              {categories.map((category) => (
                <div key={category.name} className="relative group">
                  <Link
                    to={category.link}
                    className="px-4 py-2 text-sm font-bold text-gray-800 hover:text-purple-600 transition-all duration-200 uppercase tracking-wide rounded-lg hover:bg-purple-50 flex items-center"
                  >
                    {category.name}
                    <svg className="w-4 h-4 ml-1 transform group-hover:rotate-180 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </Link>

                  {/* Mega Menu Dropdown */}
                  <div className="absolute top-full left-0 w-64 bg-white rounded-xl shadow-2xl border border-gray-100 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform translate-y-2 group-hover:translate-y-0 z-50">
                    <div className="p-6">
                      <h3 className="text-lg font-bold text-gray-900 mb-4 border-b border-gray-100 pb-2">
                        {category.name} COLLECTION
                      </h3>
                      <div className="grid grid-cols-1 gap-2">
                        {category.submenu.map((item, index) => (
                          <Link
                            key={index}
                            to={`${category.link}&subcategory=${item.toLowerCase()}`}
                            className="text-sm text-gray-600 hover:text-purple-600 hover:bg-purple-50 px-3 py-2 rounded-lg transition-all duration-200 flex items-center justify-between group/item"
                          >
                            {item}
                            <svg className="w-4 h-4 opacity-0 group-hover/item:opacity-100 transition-opacity duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                            </svg>
                          </Link>
                        ))}
                      </div>
                      <div className="mt-4 pt-4 border-t border-gray-100">
                        <Link
                          to={category.link}
                          className="text-sm font-semibold text-purple-600 hover:text-purple-700 flex items-center"
                        >
                          View All {category.name}
                          <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                          </svg>
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </nav>

            {/* Advanced Search Bar */}
            <div className="hidden md:flex flex-1 max-w-2xl mx-8">
              <form onSubmit={handleSearch} className="w-full">
                <div className="relative group">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 group-focus-within:text-purple-500 transition-colors duration-200" />
                  </div>
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="block w-full pl-12 pr-12 py-4 border-2 border-gray-200 rounded-xl leading-5 bg-gray-50 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 focus:bg-white text-sm font-medium shadow-sm hover:shadow-md transition-all duration-200"
                    placeholder="Search for products, brands, categories..."
                  />
                  <div className="absolute inset-y-0 right-0 pr-4 flex items-center">
                    <div className="flex items-center space-x-2">
                      <kbd className="hidden sm:inline-block px-2 py-1 text-xs font-semibold text-gray-500 bg-gray-100 border border-gray-200 rounded">
                        ⌘K
                      </kbd>
                      <button
                        type="submit"
                        className="p-1 text-gray-400 hover:text-purple-500 transition-colors duration-200"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </form>
            </div>

            {/* Right Side Icons */}
            <div className="flex items-center space-x-2">
              {/* Profile */}
              <div className="relative">
                <button
                  onClick={() => setShowUserMenu(!showUserMenu)}
                  className="flex flex-col items-center text-gray-700 hover:text-purple-600 transition-all duration-200 p-3 rounded-xl hover:bg-purple-50 group"
                >
                  <div className="relative">
                    <UserIcon className="h-6 w-6 group-hover:scale-110 transition-transform duration-200" />
                    {isAuthenticated && (
                      <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                    )}
                  </div>
                  <span className="text-xs font-medium mt-1 group-hover:text-purple-600">
                    {isAuthenticated ? 'Account' : 'Sign In'}
                  </span>
                </button>

                {/* Enhanced User Dropdown */}
                {showUserMenu && (
                  <div className="absolute right-0 mt-2 w-72 bg-white rounded-xl shadow-2xl border border-gray-100 py-2 z-50 transform opacity-0 scale-95 animate-in">
                    {isAuthenticated ? (
                      <>
                        <div className="px-6 py-4 border-b border-gray-100">
                          <div className="flex items-center space-x-3">
                            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                              <span className="text-white font-bold text-lg">
                                {user?.firstName?.charAt(0) || 'U'}
                              </span>
                            </div>
                            <div>
                              <p className="font-semibold text-gray-900">Hello, {user?.firstName}!</p>
                              <p className="text-sm text-gray-500">{user?.email}</p>
                            </div>
                          </div>
                        </div>

                        <div className="py-2">
                          <Link
                            to="/profile"
                            className="flex items-center px-6 py-3 text-sm text-gray-700 hover:bg-purple-50 hover:text-purple-600 transition-all duration-200"
                            onClick={() => setShowUserMenu(false)}
                          >
                            <UserIcon className="h-5 w-5 mr-3" />
                            My Profile
                          </Link>
                          <Link
                            to="/orders"
                            className="flex items-center px-6 py-3 text-sm text-gray-700 hover:bg-purple-50 hover:text-purple-600 transition-all duration-200"
                            onClick={() => setShowUserMenu(false)}
                          >
                            <ShoppingCartIcon className="h-5 w-5 mr-3" />
                            My Orders
                          </Link>
                          <Link
                            to="/wishlist"
                            className="flex items-center px-6 py-3 text-sm text-gray-700 hover:bg-purple-50 hover:text-purple-600 transition-all duration-200"
                            onClick={() => setShowUserMenu(false)}
                          >
                            <HeartIcon className="h-5 w-5 mr-3" />
                            My Wishlist
                          </Link>
                        </div>

                        <div className="border-t border-gray-100 py-2">
                          <button
                            onClick={handleLogout}
                            className="flex items-center w-full px-6 py-3 text-sm text-red-600 hover:bg-red-50 transition-all duration-200"
                          >
                            <svg className="h-5 w-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                            </svg>
                            Sign Out
                          </button>
                        </div>
                      </>
                    ) : (
                      <>
                        <div className="px-6 py-4 border-b border-gray-100">
                          <h3 className="font-semibold text-gray-900">Welcome to Modern Metro</h3>
                          <p className="text-sm text-gray-500">Sign in to access your account</p>
                        </div>
                        <div className="py-2">
                          <Link
                            to="/login"
                            className="flex items-center px-6 py-3 text-sm text-gray-700 hover:bg-purple-50 hover:text-purple-600 transition-all duration-200"
                            onClick={() => setShowUserMenu(false)}
                          >
                            <svg className="h-5 w-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m0 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                            </svg>
                            Sign In
                          </Link>
                          <Link
                            to="/register"
                            className="flex items-center px-6 py-3 text-sm text-purple-600 hover:bg-purple-50 font-medium transition-all duration-200"
                            onClick={() => setShowUserMenu(false)}
                          >
                            <svg className="h-5 w-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                            </svg>
                            Create Account
                          </Link>
                        </div>
                      </>
                    )}
                  </div>
                )}
              </div>

              {/* Wishlist */}
              <Link
                to="/wishlist"
                className="flex flex-col items-center text-gray-700 hover:text-purple-600 transition-all duration-200 p-3 rounded-xl hover:bg-purple-50 group relative"
              >
                <div className="relative">
                  <HeartIcon className="h-6 w-6 group-hover:scale-110 transition-transform duration-200" />
                  <div className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                </div>
                <span className="text-xs font-medium mt-1 group-hover:text-purple-600">Wishlist</span>
              </Link>

              {/* Cart */}
              <Link
                to="/cart"
                className="flex flex-col items-center text-gray-700 hover:text-purple-600 transition-all duration-200 p-3 rounded-xl hover:bg-purple-50 group relative"
              >
                <div className="relative">
                  <ShoppingCartIcon className="h-6 w-6 group-hover:scale-110 transition-transform duration-200" />
                  {totalItems > 0 && (
                    <span className="absolute -top-2 -right-2 bg-gradient-to-r from-pink-500 to-purple-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold shadow-lg animate-pulse">
                      {totalItems}
                    </span>
                  )}
                </div>
                <span className="text-xs font-medium mt-1 group-hover:text-purple-600">
                  Cart {totalItems > 0 && `(${totalItems})`}
                </span>
              </Link>

              {/* Mobile Menu Button */}
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="lg:hidden p-2 rounded-md text-gray-700 hover:text-pink-600 hover:bg-gray-100"
              >
                {isMenuOpen ? (
                  <XMarkIcon className="h-6 w-6" />
                ) : (
                  <Bars3Icon className="h-6 w-6" />
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="lg:hidden">
          {/* Search Bar - Mobile */}
          <div className="px-4 py-3 border-b border-gray-200">
            <form onSubmit={handleSearch}>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-pink-500 focus:border-pink-500 text-sm"
                  placeholder="Search for products..."
                />
              </div>
            </form>
          </div>

          {/* Mobile Navigation */}
          <div className="px-4 py-3 space-y-1">
            {categories.map((category) => (
              <Link
                key={category.name}
                to={category.link}
                className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-pink-600 hover:bg-gray-50"
                onClick={() => setIsMenuOpen(false)}
              >
                {category.name}
              </Link>
            ))}

            {/* Mobile User Actions */}
            <div className="pt-4 border-t border-gray-200">
              {isAuthenticated ? (
                <>
                  <div className="px-3 py-2 text-sm text-gray-500">
                    Signed in as {user?.firstName}
                  </div>
                  <Link
                    to="/profile"
                    className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-pink-600 hover:bg-gray-50"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Profile
                  </Link>
                  <Link
                    to="/orders"
                    className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-pink-600 hover:bg-gray-50"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Orders
                  </Link>
                  <button
                    onClick={handleLogout}
                    className="block w-full text-left px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-pink-600 hover:bg-gray-50"
                  >
                    Sign out
                  </button>
                </>
              ) : (
                <>
                  <Link
                    to="/login"
                    className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-pink-600 hover:bg-gray-50"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Sign in
                  </Link>
                  <Link
                    to="/register"
                    className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-pink-600 hover:bg-gray-50"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Create account
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;

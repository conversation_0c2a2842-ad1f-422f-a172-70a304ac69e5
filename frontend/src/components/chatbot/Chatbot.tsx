import React, { useState, useRef, useEffect } from 'react';
import { ChatBubbleLeftRightIcon, XMarkIcon, PaperAirplaneIcon } from '@heroicons/react/24/outline';

interface Message {
  id: string;
  text: string;
  sender: 'user' | 'bot';
  timestamp: Date;
}

const Chatbot: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: "Hi! I'm your fashion assistant. How can I help you today? 👗",
      sender: 'bot',
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const quickReplies = [
    "Show me trending items",
    "Help with size guide",
    "Track my order",
    "Return policy",
    "Customer support"
  ];

  const botResponses: { [key: string]: string } = {
    "show me trending items": "Here are our trending items! Check out our featured collection on the homepage. You can also browse by categories like Women's Fashion, Men's Fashion, and Accessories. 🔥",
    "help with size guide": "I'd be happy to help with sizing! Our size guide varies by brand. Generally: XS (32-34), S (34-36), M (36-38), L (38-40), XL (40-42). For specific items, check the product page for detailed measurements. 📏",
    "track my order": "To track your order, please go to 'My Orders' in your profile section. You'll need your order number. If you're having trouble, I can connect you with customer support! 📦",
    "return policy": "We offer a 30-day return policy! Items must be unworn with tags attached. Free returns for orders over ₹999. You can initiate returns from your order history. 🔄",
    "customer support": "I'm here to help! For complex issues, you can reach our human support <NAME_EMAIL> or call 1800-123-4567 (9 AM - 9 PM). 📞",
    "hello": "Hello! Welcome to FashionStore! I'm here to help you find the perfect outfit. What are you looking for today? 👋",
    "hi": "Hi there! How can I assist you with your fashion needs today? 😊",
    "help": "I can help you with:\n• Finding products\n• Size guidance\n• Order tracking\n• Return information\n• General shopping questions\n\nWhat would you like to know? 💡",
    "price": "Our prices range from ₹999 to ₹15,999 depending on the item and brand. We often have sales and discounts! Check our featured products for current deals. 💰",
    "shipping": "We offer free shipping on orders over ₹999! Standard delivery takes 3-5 business days, and express delivery takes 1-2 days. We deliver pan-India! 🚚",
    "payment": "We accept all major payment methods: Credit/Debit cards, UPI, Net Banking, Wallets, and Cash on Delivery. All payments are 100% secure! 💳"
  };

  const generateBotResponse = (userMessage: string): string => {
    const lowerMessage = userMessage.toLowerCase();
    
    // Check for exact matches first
    for (const [key, response] of Object.entries(botResponses)) {
      if (lowerMessage.includes(key)) {
        return response;
      }
    }

    // Check for keywords
    if (lowerMessage.includes('product') || lowerMessage.includes('item') || lowerMessage.includes('clothes')) {
      return "You can browse our products by visiting the Products page! We have categories for Women's Fashion, Men's Fashion, Accessories, and Footwear. Use filters to find exactly what you're looking for! 🛍️";
    }
    
    if (lowerMessage.includes('discount') || lowerMessage.includes('sale') || lowerMessage.includes('offer')) {
      return "Great news! We have ongoing discounts up to 33% off on selected items. Check our homepage for featured deals and sign up for our newsletter to get exclusive offers! 🎉";
    }

    if (lowerMessage.includes('brand')) {
      return "We feature top brands like Zara, H&M, Nike, Levi's, Mango, Coach, and many more! You can filter products by brand on our Products page. 👔";
    }

    // Default response
    return "I'm not sure about that, but I'm here to help! You can ask me about products, sizing, orders, returns, or anything else related to shopping. You can also contact our customer support for detailed assistance! 🤔";
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: inputMessage,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsTyping(true);

    // Simulate bot typing delay
    setTimeout(() => {
      const botResponse: Message = {
        id: (Date.now() + 1).toString(),
        text: generateBotResponse(inputMessage),
        sender: 'bot',
        timestamp: new Date()
      };

      setMessages(prev => [...prev, botResponse]);
      setIsTyping(false);
    }, 1000 + Math.random() * 1000);
  };

  const handleQuickReply = (reply: string) => {
    setInputMessage(reply);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <>
      {/* Chat Toggle Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="fixed bottom-6 right-6 z-50 bg-purple-600 text-white p-4 rounded-full shadow-lg hover:bg-purple-700 transition-all duration-300 transform hover:scale-110"
      >
        {isOpen ? (
          <XMarkIcon className="h-6 w-6" />
        ) : (
          <ChatBubbleLeftRightIcon className="h-6 w-6" />
        )}
      </button>

      {/* Chat Window */}
      {isOpen && (
        <div className="fixed bottom-24 right-6 z-50 w-80 h-96 bg-white rounded-lg shadow-2xl border border-gray-200 flex flex-col">
          {/* Header */}
          <div className="bg-purple-600 text-white p-4 rounded-t-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center mr-3">
                  <span className="text-sm font-bold">FS</span>
                </div>
                <div>
                  <h3 className="font-semibold">Fashion Assistant</h3>
                  <p className="text-xs text-purple-200">Online now</p>
                </div>
              </div>
              <button
                onClick={() => setIsOpen(false)}
                className="text-purple-200 hover:text-white"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>
          </div>

          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-xs px-3 py-2 rounded-lg text-sm ${
                    message.sender === 'user'
                      ? 'bg-purple-600 text-white'
                      : 'bg-gray-100 text-gray-900'
                  }`}
                >
                  <p className="whitespace-pre-wrap">{message.text}</p>
                  <p className={`text-xs mt-1 ${
                    message.sender === 'user' ? 'text-purple-200' : 'text-gray-500'
                  }`}>
                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </p>
                </div>
              </div>
            ))}
            
            {isTyping && (
              <div className="flex justify-start">
                <div className="bg-gray-100 text-gray-900 px-3 py-2 rounded-lg text-sm">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Quick Replies */}
          {messages.length <= 2 && (
            <div className="px-4 pb-2">
              <div className="flex flex-wrap gap-2">
                {quickReplies.slice(0, 3).map((reply, index) => (
                  <button
                    key={index}
                    onClick={() => handleQuickReply(reply)}
                    className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-full hover:bg-gray-200 transition-colors"
                  >
                    {reply}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Input */}
          <div className="p-4 border-t border-gray-200">
            <div className="flex space-x-2">
              <input
                type="text"
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Type your message..."
                className="flex-1 border border-gray-300 rounded-full px-4 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
              <button
                onClick={handleSendMessage}
                disabled={!inputMessage.trim()}
                className="bg-purple-600 text-white p-2 rounded-full hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <PaperAirplaneIcon className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Chatbot;

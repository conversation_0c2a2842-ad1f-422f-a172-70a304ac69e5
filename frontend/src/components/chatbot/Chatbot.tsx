import React, { useState, useRef, useEffect } from 'react';
import { ChatBubbleLeftRightIcon, XMarkIcon, PaperAirplaneIcon } from '@heroicons/react/24/outline';

interface Message {
  id: string;
  text: string;
  sender: 'user' | 'bot';
  timestamp: Date;
}

const Chatbot: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: "Hi! I'm your fashion assistant. How can I help you today? 👗",
      sender: 'bot',
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const quickReplies = [
    "Show me trending items",
    "Help with size guide",
    "Track my order",
    "Return policy",
    "Customer support"
  ];

  const botResponses: { [key: string]: string } = {
    "show me trending items": "Here are our trending items! Check out our featured collection on the homepage. You can also browse by categories like Women's Fashion, Men's Fashion, and Accessories. 🔥",
    "help with size guide": "I'd be happy to help with sizing! Our size guide varies by brand. Generally: XS (32-34), S (34-36), M (36-38), L (38-40), XL (40-42). For specific items, check the product page for detailed measurements. 📏",
    "track my order": "To track your order, please go to 'My Orders' in your profile section. You'll need your order number. If you're having trouble, I can connect you with customer support! 📦",
    "return policy": "We offer a 30-day return policy! Items must be unworn with tags attached. Free returns for orders over ₹999. You can initiate returns from your order history. 🔄",
    "customer support": "I'm here to help! For complex issues, you can reach our human support <NAME_EMAIL> or call 1800-123-4567 (9 AM - 9 PM). 📞",
    "hello": "Hello! Welcome to FashionStore! I'm here to help you find the perfect outfit. What are you looking for today? 👋",
    "hi": "Hi there! How can I assist you with your fashion needs today? 😊",
    "help": "I can help you with:\n• Finding products\n• Size guidance\n• Order tracking\n• Return information\n• General shopping questions\n\nWhat would you like to know? 💡",
    "price": "Our prices range from ₹999 to ₹15,999 depending on the item and brand. We often have sales and discounts! Check our featured products for current deals. 💰",
    "shipping": "We offer free shipping on orders over ₹999! Standard delivery takes 3-5 business days, and express delivery takes 1-2 days. We deliver pan-India! 🚚",
    "payment": "We accept all major payment methods: Credit/Debit cards, UPI, Net Banking, Wallets, and Cash on Delivery. All payments are 100% secure! 💳"
  };

  const generateBotResponse = (userMessage: string): string => {
    const lowerMessage = userMessage.toLowerCase();
    
    // Check for exact matches first
    for (const [key, response] of Object.entries(botResponses)) {
      if (lowerMessage.includes(key)) {
        return response;
      }
    }

    // Check for keywords
    if (lowerMessage.includes('product') || lowerMessage.includes('item') || lowerMessage.includes('clothes')) {
      return "You can browse our products by visiting the Products page! We have categories for Women's Fashion, Men's Fashion, Accessories, and Footwear. Use filters to find exactly what you're looking for! 🛍️";
    }
    
    if (lowerMessage.includes('discount') || lowerMessage.includes('sale') || lowerMessage.includes('offer')) {
      return "Great news! We have ongoing discounts up to 33% off on selected items. Check our homepage for featured deals and sign up for our newsletter to get exclusive offers! 🎉";
    }

    if (lowerMessage.includes('brand')) {
      return "We feature top brands like Zara, H&M, Nike, Levi's, Mango, Coach, and many more! You can filter products by brand on our Products page. 👔";
    }

    // Default response
    return "I'm not sure about that, but I'm here to help! You can ask me about products, sizing, orders, returns, or anything else related to shopping. You can also contact our customer support for detailed assistance! 🤔";
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: inputMessage,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsTyping(true);

    // Simulate bot typing delay
    setTimeout(() => {
      const botResponse: Message = {
        id: (Date.now() + 1).toString(),
        text: generateBotResponse(inputMessage),
        sender: 'bot',
        timestamp: new Date()
      };

      setMessages(prev => [...prev, botResponse]);
      setIsTyping(false);
    }, 1000 + Math.random() * 1000);
  };

  const handleQuickReply = (reply: string) => {
    setInputMessage(reply);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <>
      {/* Professional Chat Toggle Button */}
      <div className="fixed bottom-6 right-6 z-50">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="group relative bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 text-white p-4 rounded-2xl shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-105 hover:-translate-y-1"
        >
          <div className="relative">
            {isOpen ? (
              <XMarkIcon className="h-7 w-7 transition-transform duration-300" />
            ) : (
              <ChatBubbleLeftRightIcon className="h-7 w-7 transition-transform duration-300 group-hover:scale-110" />
            )}
          </div>

          {/* Notification Badge */}
          {!isOpen && (
            <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center animate-pulse">
              <span className="text-xs font-bold text-black">AI</span>
            </div>
          )}

          {/* Ripple Effect */}
          <div className="absolute inset-0 rounded-2xl bg-white opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
        </button>

        {/* Tooltip */}
        {!isOpen && (
          <div className="absolute bottom-full right-0 mb-2 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">
            Need help? Chat with our AI assistant
            <div className="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
          </div>
        )}
      </div>

      {/* Professional Chat Window */}
      {isOpen && (
        <div className="fixed bottom-32 right-6 z-50 w-96 h-[32rem] bg-white rounded-3xl shadow-2xl border border-gray-100 flex flex-col overflow-hidden transform transition-all duration-300 animate-in slide-in-from-bottom-4">
          {/* Professional Header */}
          <div className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 text-white p-6 relative overflow-hidden">
            <div className="absolute inset-0 bg-black opacity-10"></div>
            <div className="relative flex items-center justify-between">
              <div className="flex items-center">
                <div className="relative">
                  <div className="w-12 h-12 bg-white bg-opacity-20 rounded-2xl flex items-center justify-center mr-4 backdrop-blur-sm">
                    <div className="w-8 h-8 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center">
                      <span className="text-sm font-black text-black">AI</span>
                    </div>
                  </div>
                  <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white animate-pulse"></div>
                </div>
                <div>
                  <h3 className="font-bold text-lg">Modern Metro Assistant</h3>
                  <div className="flex items-center text-sm text-purple-100">
                    <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                    Online • Powered by AI
                  </div>
                </div>
              </div>
              <button
                onClick={() => setIsOpen(false)}
                className="p-2 hover:bg-white hover:bg-opacity-20 rounded-xl transition-all duration-200"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>

            {/* Decorative Elements */}
            <div className="absolute -top-4 -right-4 w-16 h-16 bg-white opacity-5 rounded-full"></div>
            <div className="absolute -bottom-2 -left-2 w-12 h-12 bg-pink-400 opacity-10 rounded-full"></div>
          </div>

          {/* Professional Messages Area */}
          <div className="flex-1 overflow-y-auto p-6 space-y-4 bg-gradient-to-b from-gray-50 to-white">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'} animate-in slide-in-from-bottom-2`}
              >
                <div className={`flex items-end space-x-2 max-w-xs ${message.sender === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                  {message.sender === 'bot' && (
                    <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-full flex items-center justify-center flex-shrink-0">
                      <span className="text-xs font-bold text-white">AI</span>
                    </div>
                  )}
                  <div
                    className={`px-4 py-3 rounded-2xl text-sm shadow-sm ${
                      message.sender === 'user'
                        ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-br-md'
                        : 'bg-white text-gray-800 border border-gray-200 rounded-bl-md'
                    }`}
                  >
                    <p className="whitespace-pre-wrap leading-relaxed">{message.text}</p>
                    <p className={`text-xs mt-2 ${
                      message.sender === 'user' ? 'text-indigo-200' : 'text-gray-500'
                    }`}>
                      {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </p>
                  </div>
                  {message.sender === 'user' && (
                    <div className="w-8 h-8 bg-gradient-to-br from-gray-400 to-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
                      <span className="text-xs font-bold text-white">U</span>
                    </div>
                  )}
                </div>
              </div>
            ))}

            {isTyping && (
              <div className="flex justify-start animate-in slide-in-from-bottom-2">
                <div className="flex items-end space-x-2">
                  <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-full flex items-center justify-center">
                    <span className="text-xs font-bold text-white">AI</span>
                  </div>
                  <div className="bg-white text-gray-800 px-4 py-3 rounded-2xl rounded-bl-md border border-gray-200 shadow-sm">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-indigo-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-pink-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Professional Quick Replies */}
          {messages.length <= 2 && (
            <div className="px-6 pb-4">
              <div className="mb-3">
                <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide">Quick Actions</p>
              </div>
              <div className="flex flex-wrap gap-2">
                {quickReplies.map((reply, index) => (
                  <button
                    key={index}
                    onClick={() => handleQuickReply(reply)}
                    className="text-xs bg-gradient-to-r from-indigo-50 to-purple-50 text-indigo-700 px-3 py-2 rounded-xl hover:from-indigo-100 hover:to-purple-100 transition-all duration-200 border border-indigo-200 hover:border-indigo-300 font-medium"
                  >
                    {reply}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Professional Input Area */}
          <div className="p-6 bg-gray-50 border-t border-gray-100">
            <div className="flex space-x-3">
              <div className="flex-1 relative">
                <input
                  type="text"
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Ask me anything about fashion..."
                  className="w-full border-2 border-gray-200 rounded-2xl px-4 py-3 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 bg-white shadow-sm transition-all duration-200 pr-12"
                />
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <kbd className="px-2 py-1 text-xs font-semibold text-gray-500 bg-gray-100 border border-gray-200 rounded">
                    ↵
                  </kbd>
                </div>
              </div>
              <button
                onClick={handleSendMessage}
                disabled={!inputMessage.trim()}
                className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white p-3 rounded-2xl hover:from-indigo-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 disabled:transform-none"
              >
                <PaperAirplaneIcon className="h-5 w-5" />
              </button>
            </div>

            {/* Footer */}
            <div className="mt-3 text-center">
              <p className="text-xs text-gray-500">
                Powered by Modern Metro AI • Always here to help
              </p>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Chatbot;

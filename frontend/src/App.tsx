import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Provider } from 'react-redux';
import { Toaster } from 'react-hot-toast';

// Import store
import { store } from './store';

// Simple test component
const TestPage = () => (
  <div className="min-h-screen bg-gray-50 flex items-center justify-center">
    <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6">
      <h1 className="text-3xl font-bold text-center mb-4 text-gray-900">
        🎉 Fashion E-Commerce Platform
      </h1>
      <p className="text-gray-600 text-center mb-6">
        Platform is running successfully!
      </p>
      <div className="space-y-4">
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
          ✅ Frontend: Running on port 5174
        </div>
        <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded">
          ✅ Backend: Running on port 4000
        </div>
        <div className="bg-purple-100 border border-purple-400 text-purple-700 px-4 py-3 rounded">
          ✅ Redux Store: Configured
        </div>
      </div>
      <div className="mt-6 text-center">
        <button className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
          Test Login (Demo)
        </button>
      </div>
    </div>
  </div>
);

function App() {
  return (
    <Provider store={store}>
      <Router>
        <Routes>
          <Route path="*" element={<TestPage />} />
        </Routes>
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#363636',
              color: '#fff',
            },
          }}
        />
      </Router>
    </Provider>
  );
}

export default App;

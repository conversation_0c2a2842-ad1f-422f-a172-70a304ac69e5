import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { FunnelIcon, Squares2X2Icon, ListBulletIcon, HeartIcon, StarIcon } from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';

const ProductsPage: React.FC = () => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('popularity');
  const [selectedFilters, setSelectedFilters] = useState({
    category: '',
    brand: '',
    priceRange: '',
    size: '',
    color: ''
  });

  // Mock products data with real fashion images
  const products = [
    {
      id: 1,
      name: "Classic White Button Shirt",
      brand: "Zara",
      price: 2999,
      originalPrice: 3999,
      image: "https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=400&h=500&fit=crop",
      rating: 4.5,
      reviews: 128,
      discount: 25,
      colors: ['white', 'blue', 'black'],
      sizes: ['S', 'M', 'L', 'XL'],
      category: 'shirts',
      isWishlisted: false
    },
    {
      id: 2,
      name: "Vintage Denim Jacket",
      brand: "Levi's",
      price: 4999,
      originalPrice: 6999,
      image: "https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=500&fit=crop",
      rating: 4.7,
      reviews: 89,
      discount: 29,
      colors: ['blue', 'black'],
      sizes: ['S', 'M', 'L', 'XL'],
      category: 'jackets',
      isWishlisted: true
    },
    {
      id: 3,
      name: "Floral Summer Dress",
      brand: "H&M",
      price: 1999,
      originalPrice: 2999,
      image: "https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=400&h=500&fit=crop",
      rating: 4.3,
      reviews: 156,
      discount: 33,
      colors: ['floral', 'red', 'blue'],
      sizes: ['XS', 'S', 'M', 'L'],
      category: 'dresses',
      isWishlisted: false
    },
    {
      id: 4,
      name: "Premium Sneakers",
      brand: "Nike",
      price: 7999,
      originalPrice: 9999,
      image: "https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=500&fit=crop",
      rating: 4.8,
      reviews: 234,
      discount: 20,
      colors: ['white', 'black', 'red'],
      sizes: ['7', '8', '9', '10', '11'],
      category: 'footwear',
      isWishlisted: false
    },
    {
      id: 5,
      name: "Casual Blazer",
      brand: "Mango",
      price: 3999,
      originalPrice: 5999,
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=500&fit=crop",
      rating: 4.4,
      reviews: 67,
      discount: 33,
      colors: ['navy', 'black', 'gray'],
      sizes: ['S', 'M', 'L', 'XL'],
      category: 'blazers',
      isWishlisted: false
    },
    {
      id: 6,
      name: "Leather Handbag",
      brand: "Coach",
      price: 12999,
      originalPrice: 15999,
      image: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=500&fit=crop",
      rating: 4.9,
      reviews: 45,
      discount: 19,
      colors: ['brown', 'black', 'tan'],
      sizes: ['One Size'],
      category: 'bags',
      isWishlisted: true
    },
    {
      id: 7,
      name: "Slim Fit Jeans",
      brand: "Wrangler",
      price: 2499,
      originalPrice: 3499,
      image: "https://images.unsplash.com/photo-1542272604-787c3835535d?w=400&h=500&fit=crop",
      rating: 4.2,
      reviews: 198,
      discount: 29,
      colors: ['blue', 'black', 'gray'],
      sizes: ['28', '30', '32', '34', '36'],
      category: 'jeans',
      isWishlisted: false
    },
    {
      id: 8,
      name: "Silk Scarf",
      brand: "Hermès",
      price: 8999,
      originalPrice: 10999,
      image: "https://images.unsplash.com/photo-1601924994987-69e26d50dc26?w=400&h=500&fit=crop",
      rating: 4.6,
      reviews: 23,
      discount: 18,
      colors: ['multicolor', 'blue', 'red'],
      sizes: ['One Size'],
      category: 'accessories',
      isWishlisted: false
    }
  ];

  const categories = [
    'All Categories',
    'Shirts',
    'Dresses',
    'Jeans',
    'Jackets',
    'Footwear',
    'Accessories',
    'Bags'
  ];

  const brands = [
    'All Brands',
    'Zara',
    'H&M',
    'Nike',
    'Levi\'s',
    'Mango',
    'Coach',
    'Wrangler',
    'Hermès'
  ];

  const priceRanges = [
    'All Prices',
    'Under ₹2,000',
    '₹2,000 - ₹5,000',
    '₹5,000 - ₹10,000',
    'Above ₹10,000'
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Fashion Collection</h1>
          <p className="text-gray-600">Discover the latest trends and timeless classics</p>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Filters Sidebar */}
          <div className="lg:w-64 flex-shrink-0">
            <div className="bg-white rounded-lg shadow-sm p-6 sticky top-4">
              <div className="flex items-center mb-6">
                <FunnelIcon className="h-5 w-5 text-gray-500 mr-2" />
                <h3 className="text-lg font-semibold text-gray-900">Filters</h3>
              </div>

              {/* Category Filter */}
              <div className="mb-6">
                <h4 className="text-sm font-medium text-gray-900 mb-3">Category</h4>
                <div className="space-y-2">
                  {categories.map((category) => (
                    <label key={category} className="flex items-center">
                      <input
                        type="radio"
                        name="category"
                        className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300"
                        defaultChecked={category === 'All Categories'}
                      />
                      <span className="ml-2 text-sm text-gray-700">{category}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Brand Filter */}
              <div className="mb-6">
                <h4 className="text-sm font-medium text-gray-900 mb-3">Brand</h4>
                <div className="space-y-2">
                  {brands.map((brand) => (
                    <label key={brand} className="flex items-center">
                      <input
                        type="checkbox"
                        className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-700">{brand}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Price Range Filter */}
              <div className="mb-6">
                <h4 className="text-sm font-medium text-gray-900 mb-3">Price Range</h4>
                <div className="space-y-2">
                  {priceRanges.map((range) => (
                    <label key={range} className="flex items-center">
                      <input
                        type="radio"
                        name="priceRange"
                        className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300"
                        defaultChecked={range === 'All Prices'}
                      />
                      <span className="ml-2 text-sm text-gray-700">{range}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Products Section */}
          <div className="flex-1">
            {/* Toolbar */}
            <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
              <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
                <div className="flex items-center gap-4">
                  <span className="text-sm text-gray-600">
                    Showing {products.length} products
                  </span>
                </div>

                <div className="flex items-center gap-4">
                  {/* Sort Dropdown */}
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-purple-500 focus:border-purple-500"
                  >
                    <option value="popularity">Sort by Popularity</option>
                    <option value="price-low">Price: Low to High</option>
                    <option value="price-high">Price: High to Low</option>
                    <option value="rating">Customer Rating</option>
                    <option value="newest">Newest First</option>
                  </select>

                  {/* View Mode Toggle */}
                  <div className="flex border border-gray-300 rounded-md">
                    <button
                      onClick={() => setViewMode('grid')}
                      className={`p-2 ${viewMode === 'grid' ? 'bg-purple-600 text-white' : 'text-gray-600 hover:text-gray-900'}`}
                    >
                      <Squares2X2Icon className="h-5 w-5" />
                    </button>
                    <button
                      onClick={() => setViewMode('list')}
                      className={`p-2 ${viewMode === 'list' ? 'bg-purple-600 text-white' : 'text-gray-600 hover:text-gray-900'}`}
                    >
                      <ListBulletIcon className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Products Grid */}
            <div className={`grid gap-6 ${viewMode === 'grid' ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1'}`}>
              {products.map((product) => (
                <div key={product.id} className={`group bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-lg transition-all duration-300 ${viewMode === 'list' ? 'flex' : ''}`}>
                  <div className={`relative overflow-hidden ${viewMode === 'list' ? 'w-48 flex-shrink-0' : ''}`}>
                    <Link to={`/products/${product.id}`}>
                      <img
                        src={product.image}
                        alt={product.name}
                        className={`object-cover group-hover:scale-105 transition-transform duration-300 ${viewMode === 'list' ? 'w-full h-full' : 'w-full h-64'}`}
                      />
                    </Link>

                    {product.discount > 0 && (
                      <div className="absolute top-3 left-3">
                        <span className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                          -{product.discount}%
                        </span>
                      </div>
                    )}

                    <button className="absolute top-3 right-3 p-2 bg-white rounded-full shadow-md hover:bg-gray-50 transition-colors">
                      {product.isWishlisted ? (
                        <HeartSolidIcon className="h-5 w-5 text-red-500" />
                      ) : (
                        <HeartIcon className="h-5 w-5 text-gray-400 hover:text-red-500" />
                      )}
                    </button>
                  </div>

                  <div className={`p-4 ${viewMode === 'list' ? 'flex-1 flex flex-col justify-between' : ''}`}>
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm text-gray-500 font-medium">{product.brand}</span>
                        <div className="flex items-center">
                          <StarIcon className="h-4 w-4 text-yellow-400 fill-current" />
                          <span className="text-sm text-gray-600 ml-1">{product.rating}</span>
                          <span className="text-sm text-gray-400 ml-1">({product.reviews})</span>
                        </div>
                      </div>

                      <Link to={`/products/${product.id}`}>
                        <h3 className="font-semibold text-gray-900 mb-2 group-hover:text-purple-600 transition-colors line-clamp-2">
                          {product.name}
                        </h3>
                      </Link>
                    </div>

                    <div className={`flex items-center justify-between ${viewMode === 'list' ? 'mt-4' : ''}`}>
                      <div className="flex items-center space-x-2">
                        <span className="text-lg font-bold text-gray-900">₹{product.price.toLocaleString()}</span>
                        {product.originalPrice > product.price && (
                          <span className="text-sm text-gray-500 line-through">₹{product.originalPrice.toLocaleString()}</span>
                        )}
                      </div>
                      <button className="bg-purple-600 text-white px-4 py-2 rounded-full text-sm font-medium hover:bg-purple-700 transition-colors">
                        Add to Cart
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination */}
            <div className="mt-12 flex justify-center">
              <nav className="flex items-center space-x-2">
                <button className="px-3 py-2 text-sm text-gray-500 hover:text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50">
                  Previous
                </button>
                {[1, 2, 3, 4, 5].map((page) => (
                  <button
                    key={page}
                    className={`px-3 py-2 text-sm border rounded-md ${
                      page === 1
                        ? 'bg-purple-600 text-white border-purple-600'
                        : 'text-gray-700 border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {page}
                  </button>
                ))}
                <button className="px-3 py-2 text-sm text-gray-500 hover:text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50">
                  Next
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductsPage;

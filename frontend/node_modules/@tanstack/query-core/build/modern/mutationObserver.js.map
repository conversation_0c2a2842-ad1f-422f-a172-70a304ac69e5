{"version": 3, "sources": ["../../src/mutationObserver.ts"], "sourcesContent": ["import { getDefaultState } from './mutation'\nimport { notify<PERSON>anager } from './notifyManager'\nimport { Subscribable } from './subscribable'\nimport { hashKey, shallowEqualObjects } from './utils'\nimport type { QueryClient } from './queryClient'\nimport type {\n  DefaultError,\n  MutateOptions,\n  MutationObserverOptions,\n  MutationObserverResult,\n} from './types'\nimport type { Action, Mutation } from './mutation'\n\n// TYPES\n\ntype MutationObserverListener<TData, TError, TVariables, TContext> = (\n  result: MutationObserverResult<TData, TError, TVariables, TContext>,\n) => void\n\n// CLASS\n\nexport class MutationObserver<\n  TData = unknown,\n  TError = DefaultError,\n  TVariables = void,\n  TContext = unknown,\n> extends Subscribable<\n  MutationObserverListener<TData, TError, TVariables, TContext>\n> {\n  options!: MutationObserverOptions<TData, TError, TVariables, TContext>\n\n  #client: QueryClient\n  #currentResult: MutationObserverResult<TData, TError, TVariables, TContext> =\n    undefined!\n  #currentMutation?: Mutation<TData, TError, TVariables, TContext>\n  #mutateOptions?: MutateOptions<TData, TError, TVariables, TContext>\n\n  constructor(\n    client: QueryClient,\n    options: MutationObserverOptions<TData, TError, TVariables, TContext>,\n  ) {\n    super()\n\n    this.#client = client\n    this.setOptions(options)\n    this.bindMethods()\n    this.#updateResult()\n  }\n\n  protected bindMethods(): void {\n    this.mutate = this.mutate.bind(this)\n    this.reset = this.reset.bind(this)\n  }\n\n  setOptions(\n    options: MutationObserverOptions<TData, TError, TVariables, TContext>,\n  ) {\n    const prevOptions = this.options as\n      | MutationObserverOptions<TData, TError, TVariables, TContext>\n      | undefined\n    this.options = this.#client.defaultMutationOptions(options)\n    if (!shallowEqualObjects(this.options, prevOptions)) {\n      this.#client.getMutationCache().notify({\n        type: 'observerOptionsUpdated',\n        mutation: this.#currentMutation,\n        observer: this,\n      })\n    }\n\n    if (\n      prevOptions?.mutationKey &&\n      this.options.mutationKey &&\n      hashKey(prevOptions.mutationKey) !== hashKey(this.options.mutationKey)\n    ) {\n      this.reset()\n    } else if (this.#currentMutation?.state.status === 'pending') {\n      this.#currentMutation.setOptions(this.options)\n    }\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.hasListeners()) {\n      this.#currentMutation?.removeObserver(this)\n    }\n  }\n\n  onMutationUpdate(action: Action<TData, TError, TVariables, TContext>): void {\n    this.#updateResult()\n\n    this.#notify(action)\n  }\n\n  getCurrentResult(): MutationObserverResult<\n    TData,\n    TError,\n    TVariables,\n    TContext\n  > {\n    return this.#currentResult\n  }\n\n  reset(): void {\n    // reset needs to remove the observer from the mutation because there is no way to \"get it back\"\n    // another mutate call will yield a new mutation!\n    this.#currentMutation?.removeObserver(this)\n    this.#currentMutation = undefined\n    this.#updateResult()\n    this.#notify()\n  }\n\n  mutate(\n    variables: TVariables,\n    options?: MutateOptions<TData, TError, TVariables, TContext>,\n  ): Promise<TData> {\n    this.#mutateOptions = options\n\n    this.#currentMutation?.removeObserver(this)\n\n    this.#currentMutation = this.#client\n      .getMutationCache()\n      .build(this.#client, this.options)\n\n    this.#currentMutation.addObserver(this)\n\n    return this.#currentMutation.execute(variables)\n  }\n\n  #updateResult(): void {\n    const state =\n      this.#currentMutation?.state ??\n      getDefaultState<TData, TError, TVariables, TContext>()\n\n    this.#currentResult = {\n      ...state,\n      isPending: state.status === 'pending',\n      isSuccess: state.status === 'success',\n      isError: state.status === 'error',\n      isIdle: state.status === 'idle',\n      mutate: this.mutate,\n      reset: this.reset,\n    } as MutationObserverResult<TData, TError, TVariables, TContext>\n  }\n\n  #notify(action?: Action<TData, TError, TVariables, TContext>): void {\n    notifyManager.batch(() => {\n      // First trigger the mutate callbacks\n      if (this.#mutateOptions && this.hasListeners()) {\n        const variables = this.#currentResult.variables!\n        const context = this.#currentResult.context\n\n        if (action?.type === 'success') {\n          this.#mutateOptions.onSuccess?.(action.data, variables, context!)\n          this.#mutateOptions.onSettled?.(action.data, null, variables, context)\n        } else if (action?.type === 'error') {\n          this.#mutateOptions.onError?.(action.error, variables, context)\n          this.#mutateOptions.onSettled?.(\n            undefined,\n            action.error,\n            variables,\n            context,\n          )\n        }\n      }\n\n      // Then trigger the listeners\n      this.listeners.forEach((listener) => {\n        listener(this.#currentResult)\n      })\n    })\n  }\n}\n"], "mappings": ";AAAA,SAAS,uBAAuB;AAChC,SAAS,qBAAqB;AAC9B,SAAS,oBAAoB;AAC7B,SAAS,SAAS,2BAA2B;AAkBtC,IAAM,mBAAN,cAKG,aAER;AAAA,EAGA;AAAA,EACA,iBACE;AAAA,EACF;AAAA,EACA;AAAA,EAEA,YACE,QACA,SACA;AACA,UAAM;AAEN,SAAK,UAAU;AACf,SAAK,WAAW,OAAO;AACvB,SAAK,YAAY;AACjB,SAAK,cAAc;AAAA,EACrB;AAAA,EAEU,cAAoB;AAC5B,SAAK,SAAS,KAAK,OAAO,KAAK,IAAI;AACnC,SAAK,QAAQ,KAAK,MAAM,KAAK,IAAI;AAAA,EACnC;AAAA,EAEA,WACE,SACA;AACA,UAAM,cAAc,KAAK;AAGzB,SAAK,UAAU,KAAK,QAAQ,uBAAuB,OAAO;AAC1D,QAAI,CAAC,oBAAoB,KAAK,SAAS,WAAW,GAAG;AACnD,WAAK,QAAQ,iBAAiB,EAAE,OAAO;AAAA,QACrC,MAAM;AAAA,QACN,UAAU,KAAK;AAAA,QACf,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AAEA,QACE,aAAa,eACb,KAAK,QAAQ,eACb,QAAQ,YAAY,WAAW,MAAM,QAAQ,KAAK,QAAQ,WAAW,GACrE;AACA,WAAK,MAAM;AAAA,IACb,WAAW,KAAK,kBAAkB,MAAM,WAAW,WAAW;AAC5D,WAAK,iBAAiB,WAAW,KAAK,OAAO;AAAA,IAC/C;AAAA,EACF;AAAA,EAEU,gBAAsB;AAC9B,QAAI,CAAC,KAAK,aAAa,GAAG;AACxB,WAAK,kBAAkB,eAAe,IAAI;AAAA,IAC5C;AAAA,EACF;AAAA,EAEA,iBAAiB,QAA2D;AAC1E,SAAK,cAAc;AAEnB,SAAK,QAAQ,MAAM;AAAA,EACrB;AAAA,EAEA,mBAKE;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,QAAc;AAGZ,SAAK,kBAAkB,eAAe,IAAI;AAC1C,SAAK,mBAAmB;AACxB,SAAK,cAAc;AACnB,SAAK,QAAQ;AAAA,EACf;AAAA,EAEA,OACE,WACA,SACgB;AAChB,SAAK,iBAAiB;AAEtB,SAAK,kBAAkB,eAAe,IAAI;AAE1C,SAAK,mBAAmB,KAAK,QAC1B,iBAAiB,EACjB,MAAM,KAAK,SAAS,KAAK,OAAO;AAEnC,SAAK,iBAAiB,YAAY,IAAI;AAEtC,WAAO,KAAK,iBAAiB,QAAQ,SAAS;AAAA,EAChD;AAAA,EAEA,gBAAsB;AACpB,UAAM,QACJ,KAAK,kBAAkB,SACvB,gBAAqD;AAEvD,SAAK,iBAAiB;AAAA,MACpB,GAAG;AAAA,MACH,WAAW,MAAM,WAAW;AAAA,MAC5B,WAAW,MAAM,WAAW;AAAA,MAC5B,SAAS,MAAM,WAAW;AAAA,MAC1B,QAAQ,MAAM,WAAW;AAAA,MACzB,QAAQ,KAAK;AAAA,MACb,OAAO,KAAK;AAAA,IACd;AAAA,EACF;AAAA,EAEA,QAAQ,QAA4D;AAClE,kBAAc,MAAM,MAAM;AAExB,UAAI,KAAK,kBAAkB,KAAK,aAAa,GAAG;AAC9C,cAAM,YAAY,KAAK,eAAe;AACtC,cAAM,UAAU,KAAK,eAAe;AAEpC,YAAI,QAAQ,SAAS,WAAW;AAC9B,eAAK,eAAe,YAAY,OAAO,MAAM,WAAW,OAAQ;AAChE,eAAK,eAAe,YAAY,OAAO,MAAM,MAAM,WAAW,OAAO;AAAA,QACvE,WAAW,QAAQ,SAAS,SAAS;AACnC,eAAK,eAAe,UAAU,OAAO,OAAO,WAAW,OAAO;AAC9D,eAAK,eAAe;AAAA,YAClB;AAAA,YACA,OAAO;AAAA,YACP;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAGA,WAAK,UAAU,QAAQ,CAAC,aAAa;AACnC,iBAAS,KAAK,cAAc;AAAA,MAC9B,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;", "names": []}
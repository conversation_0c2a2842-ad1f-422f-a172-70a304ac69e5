{"version": 3, "sources": ["../../src/index.ts"], "sourcesContent": ["/* istanbul ignore file */\n\nexport { CancelledError } from './retryer'\nexport { QueryCache } from './queryCache'\nexport type { QueryCacheNotifyEvent } from './queryCache'\nexport { QueryClient } from './queryClient'\nexport { QueryObserver } from './queryObserver'\nexport { QueriesObserver } from './queriesObserver'\nexport { InfiniteQueryObserver } from './infiniteQueryObserver'\nexport { MutationCache } from './mutationCache'\nexport type { MutationCacheNotifyEvent } from './mutationCache'\nexport { MutationObserver } from './mutationObserver'\nexport { notifyManager, defaultScheduler } from './notifyManager'\nexport { focusManager } from './focusManager'\nexport { onlineManager } from './onlineManager'\nexport {\n  hashKey,\n  partialMatchKey,\n  replaceEqualDeep,\n  isServer,\n  matchQuery,\n  matchMutation,\n  keepPreviousData,\n  skipToken,\n  noop,\n  shouldThrowError,\n} from './utils'\nexport type { MutationFilters, QueryFilters, Updater, SkipToken } from './utils'\nexport { isCancelledError } from './retryer'\nexport {\n  dehydrate,\n  hydrate,\n  defaultShouldDehydrateQuery,\n  defaultShouldDehydrateMutation,\n} from './hydration'\n\nexport { streamedQuery as experimental_streamedQuery } from './streamedQuery'\n\n// Types\nexport * from './types'\nexport type { QueryState } from './query'\nexport { Query } from './query'\nexport type { MutationState } from './mutation'\nexport { Mutation } from './mutation'\nexport type {\n  DehydrateOptions,\n  DehydratedState,\n  HydrateOptions,\n} from './hydration'\nexport type { QueriesObserverOptions } from './queriesObserver'\n"], "mappings": ";;;AAEA,SAAS,sBAAsB;AAC/B,SAAS,kBAAkB;AAE3B,SAAS,mBAAmB;AAC5B,SAAS,qBAAqB;AAC9B,SAAS,uBAAuB;AAChC,SAAS,6BAA6B;AACtC,SAAS,qBAAqB;AAE9B,SAAS,wBAAwB;AACjC,SAAS,eAAe,wBAAwB;AAChD,SAAS,oBAAoB;AAC7B,SAAS,qBAAqB;AAC9B;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AAEP,SAAS,wBAAwB;AACjC;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AAEP,SAA0B,qBAAkC;AAG5D,cAAc;AAEd,SAAS,aAAa;AAEtB,SAAS,gBAAgB;", "names": []}
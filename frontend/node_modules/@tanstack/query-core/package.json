{"name": "@tanstack/query-core", "version": "5.80.5", "description": "The framework agnostic core that powers TanStack Query", "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/TanStack/query.git", "directory": "packages/query-core"}, "homepage": "https://tanstack.com/query", "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}, "type": "module", "types": "build/legacy/index.d.ts", "main": "build/legacy/index.cjs", "module": "build/legacy/index.js", "react-native": "src/index.ts", "exports": {".": {"@tanstack/custom-condition": "./src/index.ts", "import": {"types": "./build/modern/index.d.ts", "default": "./build/modern/index.js"}, "require": {"types": "./build/modern/index.d.cts", "default": "./build/modern/index.cjs"}}, "./package.json": "./package.json"}, "sideEffects": false, "files": ["build", "src", "!src/__tests__"], "devDependencies": {"npm-run-all2": "^5.0.0", "@tanstack/query-test-utils": "0.0.0"}, "scripts": {}}
"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Bulkexports
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const Version_1 = __importDefault(require("../../base/Version"));
const export_1 = require("./v1/export");
const exportConfiguration_1 = require("./v1/exportConfiguration");
class V1 extends Version_1.default {
    /**
     * Initialize the V1 version of Bulkexports
     *
     * @param domain - The Twilio (Twilio.Bulkexports) domain
     */
    constructor(domain) {
        super(domain, "v1");
    }
    /** Getter for exports resource */
    get exports() {
        this._exports = this._exports || (0, export_1.ExportListInstance)(this);
        return this._exports;
    }
    /** Getter for exportConfiguration resource */
    get exportConfiguration() {
        this._exportConfiguration =
            this._exportConfiguration || (0, exportConfiguration_1.ExportConfigurationListInstance)(this);
        return this._exportConfiguration;
    }
}
exports.default = V1;

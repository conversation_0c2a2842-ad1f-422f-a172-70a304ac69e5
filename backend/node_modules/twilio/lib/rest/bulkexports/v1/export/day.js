"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Bulkexports
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DayPage = exports.DayListInstance = exports.DayInstance = exports.DayContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../base/Page"));
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
const utility_1 = require("../../../../base/utility");
class DayContextImpl {
    constructor(_version, resourceType, day) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(resourceType)) {
            throw new Error("Parameter 'resourceType' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(day)) {
            throw new Error("Parameter 'day' is not valid.");
        }
        this._solution = { resourceType, day };
        this._uri = `/Exports/${resourceType}/Days/${day}`;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new DayInstance(operationVersion, payload, instance._solution.resourceType, instance._solution.day));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.DayContextImpl = DayContextImpl;
class DayInstance {
    constructor(_version, payload, resourceType, day) {
        this._version = _version;
        this.redirectTo = payload.redirect_to;
        this.day = payload.day;
        this.size = deserialize.integer(payload.size);
        this.createDate = payload.create_date;
        this.friendlyName = payload.friendly_name;
        this.resourceType = payload.resource_type;
        this._solution = { resourceType, day: day || this.day };
    }
    get _proxy() {
        this._context =
            this._context ||
                new DayContextImpl(this._version, this._solution.resourceType, this._solution.day);
        return this._context;
    }
    /**
     * Fetch a DayInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed DayInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            redirectTo: this.redirectTo,
            day: this.day,
            size: this.size,
            createDate: this.createDate,
            friendlyName: this.friendlyName,
            resourceType: this.resourceType,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.DayInstance = DayInstance;
function DayListInstance(version, resourceType) {
    if (!(0, utility_1.isValidPathParam)(resourceType)) {
        throw new Error("Parameter 'resourceType' is not valid.");
    }
    const instance = ((day) => instance.get(day));
    instance.get = function get(day) {
        return new DayContextImpl(version, resourceType, day);
    };
    instance._version = version;
    instance._solution = { resourceType };
    instance._uri = `/Exports/${resourceType}/Days`;
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new DayPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new DayPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.DayListInstance = DayListInstance;
class DayPage extends Page_1.default {
    /**
     * Initialize the DayPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of DayInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new DayInstance(this._version, payload, this._solution.resourceType);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.DayPage = DayPage;

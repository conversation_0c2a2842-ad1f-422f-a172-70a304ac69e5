"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Frontline
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const Version_1 = __importDefault(require("../../base/Version"));
const user_1 = require("./v1/user");
class V1 extends Version_1.default {
    /**
     * Initialize the V1 version of FrontlineApi
     *
     * @param domain - The Twilio (Twilio.FrontlineApi) domain
     */
    constructor(domain) {
        super(domain, "v1");
    }
    /** Getter for users resource */
    get users() {
        this._users = this._users || (0, user_1.UserListInstance)(this);
        return this._users;
    }
}
exports.default = V1;

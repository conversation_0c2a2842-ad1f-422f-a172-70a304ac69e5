"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Intelligence
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OperatorResultPage = exports.OperatorResultListInstance = exports.OperatorResultInstance = exports.OperatorResultContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../base/Page"));
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
const utility_1 = require("../../../../base/utility");
class OperatorResultContextImpl {
    constructor(_version, transcriptSid, operatorSid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(transcriptSid)) {
            throw new Error("Parameter 'transcriptSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(operatorSid)) {
            throw new Error("Parameter 'operatorSid' is not valid.");
        }
        this._solution = { transcriptSid, operatorSid };
        this._uri = `/Transcripts/${transcriptSid}/OperatorResults/${operatorSid}`;
    }
    fetch(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["redacted"] !== undefined)
            data["Redacted"] = serialize.bool(params["redacted"]);
        const headers = {};
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new OperatorResultInstance(operationVersion, payload, instance._solution.transcriptSid, instance._solution.operatorSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.OperatorResultContextImpl = OperatorResultContextImpl;
class OperatorResultInstance {
    constructor(_version, payload, transcriptSid, operatorSid) {
        this._version = _version;
        this.operatorType = payload.operator_type;
        this.name = payload.name;
        this.operatorSid = payload.operator_sid;
        this.extractMatch = payload.extract_match;
        this.matchProbability = payload.match_probability;
        this.normalizedResult = payload.normalized_result;
        this.utteranceResults = payload.utterance_results;
        this.utteranceMatch = payload.utterance_match;
        this.predictedLabel = payload.predicted_label;
        this.predictedProbability = payload.predicted_probability;
        this.labelProbabilities = payload.label_probabilities;
        this.extractResults = payload.extract_results;
        this.textGenerationResults = payload.text_generation_results;
        this.transcriptSid = payload.transcript_sid;
        this.url = payload.url;
        this._solution = {
            transcriptSid,
            operatorSid: operatorSid || this.operatorSid,
        };
    }
    get _proxy() {
        this._context =
            this._context ||
                new OperatorResultContextImpl(this._version, this._solution.transcriptSid, this._solution.operatorSid);
        return this._context;
    }
    fetch(params, callback) {
        return this._proxy.fetch(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            operatorType: this.operatorType,
            name: this.name,
            operatorSid: this.operatorSid,
            extractMatch: this.extractMatch,
            matchProbability: this.matchProbability,
            normalizedResult: this.normalizedResult,
            utteranceResults: this.utteranceResults,
            utteranceMatch: this.utteranceMatch,
            predictedLabel: this.predictedLabel,
            predictedProbability: this.predictedProbability,
            labelProbabilities: this.labelProbabilities,
            extractResults: this.extractResults,
            textGenerationResults: this.textGenerationResults,
            transcriptSid: this.transcriptSid,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.OperatorResultInstance = OperatorResultInstance;
function OperatorResultListInstance(version, transcriptSid) {
    if (!(0, utility_1.isValidPathParam)(transcriptSid)) {
        throw new Error("Parameter 'transcriptSid' is not valid.");
    }
    const instance = ((operatorSid) => instance.get(operatorSid));
    instance.get = function get(operatorSid) {
        return new OperatorResultContextImpl(version, transcriptSid, operatorSid);
    };
    instance._version = version;
    instance._solution = { transcriptSid };
    instance._uri = `/Transcripts/${transcriptSid}/OperatorResults`;
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["redacted"] !== undefined)
            data["Redacted"] = serialize.bool(params["redacted"]);
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new OperatorResultPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new OperatorResultPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.OperatorResultListInstance = OperatorResultListInstance;
class OperatorResultPage extends Page_1.default {
    /**
     * Initialize the OperatorResultPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of OperatorResultInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new OperatorResultInstance(this._version, payload, this._solution.transcriptSid);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.OperatorResultPage = OperatorResultPage;

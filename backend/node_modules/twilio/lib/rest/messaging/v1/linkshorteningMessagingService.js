"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Messaging
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.LinkshorteningMessagingServiceListInstance = exports.LinkshorteningMessagingServiceInstance = exports.LinkshorteningMessagingServiceContextImpl = void 0;
const util_1 = require("util");
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
class LinkshorteningMessagingServiceContextImpl {
    constructor(_version, domainSid, messagingServiceSid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(domainSid)) {
            throw new Error("Parameter 'domainSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(messagingServiceSid)) {
            throw new Error("Parameter 'messagingServiceSid' is not valid.");
        }
        this._solution = { domainSid, messagingServiceSid };
        this._uri = `/LinkShortening/Domains/${domainSid}/MessagingServices/${messagingServiceSid}`;
    }
    create(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
        });
        operationPromise = operationPromise.then((payload) => new LinkshorteningMessagingServiceInstance(operationVersion, payload, instance._solution.domainSid, instance._solution.messagingServiceSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    remove(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.remove({
            uri: instance._uri,
            method: "delete",
        });
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.LinkshorteningMessagingServiceContextImpl = LinkshorteningMessagingServiceContextImpl;
class LinkshorteningMessagingServiceInstance {
    constructor(_version, payload, domainSid, messagingServiceSid) {
        this._version = _version;
        this.domainSid = payload.domain_sid;
        this.messagingServiceSid = payload.messaging_service_sid;
        this.url = payload.url;
        this._solution = {
            domainSid: domainSid || this.domainSid,
            messagingServiceSid: messagingServiceSid || this.messagingServiceSid,
        };
    }
    get _proxy() {
        this._context =
            this._context ||
                new LinkshorteningMessagingServiceContextImpl(this._version, this._solution.domainSid, this._solution.messagingServiceSid);
        return this._context;
    }
    /**
     * Create a LinkshorteningMessagingServiceInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed LinkshorteningMessagingServiceInstance
     */
    create(callback) {
        return this._proxy.create(callback);
    }
    /**
     * Remove a LinkshorteningMessagingServiceInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed boolean
     */
    remove(callback) {
        return this._proxy.remove(callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            domainSid: this.domainSid,
            messagingServiceSid: this.messagingServiceSid,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.LinkshorteningMessagingServiceInstance = LinkshorteningMessagingServiceInstance;
function LinkshorteningMessagingServiceListInstance(version) {
    const instance = ((domainSid, messagingServiceSid) => instance.get(domainSid, messagingServiceSid));
    instance.get = function get(domainSid, messagingServiceSid) {
        return new LinkshorteningMessagingServiceContextImpl(version, domainSid, messagingServiceSid);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = ``;
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.LinkshorteningMessagingServiceListInstance = LinkshorteningMessagingServiceListInstance;

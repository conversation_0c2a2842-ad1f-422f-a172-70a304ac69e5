"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Messaging
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.BrandRegistrationOtpInstance = exports.BrandRegistrationOtpListInstance = void 0;
const util_1 = require("util");
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
const utility_1 = require("../../../../base/utility");
function BrandRegistrationOtpListInstance(version, brandRegistrationSid) {
    if (!(0, utility_1.isValidPathParam)(brandRegistrationSid)) {
        throw new Error("Parameter 'brandRegistrationSid' is not valid.");
    }
    const instance = {};
    instance._version = version;
    instance._solution = { brandRegistrationSid };
    instance._uri = `/a2p/BrandRegistrations/${brandRegistrationSid}/SmsOtp`;
    instance.create = function create(callback) {
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
        });
        operationPromise = operationPromise.then((payload) => new BrandRegistrationOtpInstance(operationVersion, payload, instance._solution.brandRegistrationSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.BrandRegistrationOtpListInstance = BrandRegistrationOtpListInstance;
class BrandRegistrationOtpInstance {
    constructor(_version, payload, brandRegistrationSid) {
        this._version = _version;
        this.accountSid = payload.account_sid;
        this.brandRegistrationSid = payload.brand_registration_sid;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            accountSid: this.accountSid,
            brandRegistrationSid: this.brandRegistrationSid,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.BrandRegistrationOtpInstance = BrandRegistrationOtpInstance;

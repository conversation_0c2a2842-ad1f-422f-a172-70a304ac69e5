"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Messaging
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DomainConfigMessagingServiceListInstance = exports.DomainConfigMessagingServiceInstance = exports.DomainConfigMessagingServiceContextImpl = void 0;
const util_1 = require("util");
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
class DomainConfigMessagingServiceContextImpl {
    constructor(_version, messagingServiceSid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(messagingServiceSid)) {
            throw new Error("Parameter 'messagingServiceSid' is not valid.");
        }
        this._solution = { messagingServiceSid };
        this._uri = `/LinkShortening/MessagingService/${messagingServiceSid}/DomainConfig`;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new DomainConfigMessagingServiceInstance(operationVersion, payload, instance._solution.messagingServiceSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.DomainConfigMessagingServiceContextImpl = DomainConfigMessagingServiceContextImpl;
class DomainConfigMessagingServiceInstance {
    constructor(_version, payload, messagingServiceSid) {
        this._version = _version;
        this.domainSid = payload.domain_sid;
        this.configSid = payload.config_sid;
        this.messagingServiceSid = payload.messaging_service_sid;
        this.fallbackUrl = payload.fallback_url;
        this.callbackUrl = payload.callback_url;
        this.continueOnFailure = payload.continue_on_failure;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.url = payload.url;
        this._solution = {
            messagingServiceSid: messagingServiceSid || this.messagingServiceSid,
        };
    }
    get _proxy() {
        this._context =
            this._context ||
                new DomainConfigMessagingServiceContextImpl(this._version, this._solution.messagingServiceSid);
        return this._context;
    }
    /**
     * Fetch a DomainConfigMessagingServiceInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed DomainConfigMessagingServiceInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            domainSid: this.domainSid,
            configSid: this.configSid,
            messagingServiceSid: this.messagingServiceSid,
            fallbackUrl: this.fallbackUrl,
            callbackUrl: this.callbackUrl,
            continueOnFailure: this.continueOnFailure,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.DomainConfigMessagingServiceInstance = DomainConfigMessagingServiceInstance;
function DomainConfigMessagingServiceListInstance(version) {
    const instance = ((messagingServiceSid) => instance.get(messagingServiceSid));
    instance.get = function get(messagingServiceSid) {
        return new DomainConfigMessagingServiceContextImpl(version, messagingServiceSid);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = ``;
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.DomainConfigMessagingServiceListInstance = DomainConfigMessagingServiceListInstance;

"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Messaging
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DomainConfigListInstance = exports.DomainConfigInstance = exports.DomainConfigContextImpl = void 0;
const util_1 = require("util");
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
class DomainConfigContextImpl {
    constructor(_version, domainSid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(domainSid)) {
            throw new Error("Parameter 'domainSid' is not valid.");
        }
        this._solution = { domainSid };
        this._uri = `/LinkShortening/Domains/${domainSid}/Config`;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new DomainConfigInstance(operationVersion, payload, instance._solution.domainSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["fallbackUrl"] !== undefined)
            data["FallbackUrl"] = params["fallbackUrl"];
        if (params["callbackUrl"] !== undefined)
            data["CallbackUrl"] = params["callbackUrl"];
        if (params["continueOnFailure"] !== undefined)
            data["ContinueOnFailure"] = serialize.bool(params["continueOnFailure"]);
        if (params["disableHttps"] !== undefined)
            data["DisableHttps"] = serialize.bool(params["disableHttps"]);
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new DomainConfigInstance(operationVersion, payload, instance._solution.domainSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.DomainConfigContextImpl = DomainConfigContextImpl;
class DomainConfigInstance {
    constructor(_version, payload, domainSid) {
        this._version = _version;
        this.domainSid = payload.domain_sid;
        this.configSid = payload.config_sid;
        this.fallbackUrl = payload.fallback_url;
        this.callbackUrl = payload.callback_url;
        this.continueOnFailure = payload.continue_on_failure;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.url = payload.url;
        this.disableHttps = payload.disable_https;
        this._solution = { domainSid: domainSid || this.domainSid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new DomainConfigContextImpl(this._version, this._solution.domainSid);
        return this._context;
    }
    /**
     * Fetch a DomainConfigInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed DomainConfigInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            domainSid: this.domainSid,
            configSid: this.configSid,
            fallbackUrl: this.fallbackUrl,
            callbackUrl: this.callbackUrl,
            continueOnFailure: this.continueOnFailure,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            url: this.url,
            disableHttps: this.disableHttps,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.DomainConfigInstance = DomainConfigInstance;
function DomainConfigListInstance(version) {
    const instance = ((domainSid) => instance.get(domainSid));
    instance.get = function get(domainSid) {
        return new DomainConfigContextImpl(version, domainSid);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = ``;
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.DomainConfigListInstance = DomainConfigListInstance;

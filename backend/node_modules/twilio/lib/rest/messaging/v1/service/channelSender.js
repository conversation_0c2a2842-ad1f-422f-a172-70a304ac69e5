"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Messaging
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChannelSenderPage = exports.ChannelSenderListInstance = exports.ChannelSenderInstance = exports.ChannelSenderContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../base/Page"));
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
const utility_1 = require("../../../../base/utility");
class ChannelSenderContextImpl {
    constructor(_version, messagingServiceSid, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(messagingServiceSid)) {
            throw new Error("Parameter 'messagingServiceSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { messagingServiceSid, sid };
        this._uri = `/Services/${messagingServiceSid}/ChannelSenders/${sid}`;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new ChannelSenderInstance(operationVersion, payload, instance._solution.messagingServiceSid, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ChannelSenderContextImpl = ChannelSenderContextImpl;
class ChannelSenderInstance {
    constructor(_version, payload, messagingServiceSid, sid) {
        this._version = _version;
        this.accountSid = payload.account_sid;
        this.messagingServiceSid = payload.messaging_service_sid;
        this.sid = payload.sid;
        this.sender = payload.sender;
        this.senderType = payload.sender_type;
        this.countryCode = payload.country_code;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.url = payload.url;
        this._solution = { messagingServiceSid, sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new ChannelSenderContextImpl(this._version, this._solution.messagingServiceSid, this._solution.sid);
        return this._context;
    }
    /**
     * Fetch a ChannelSenderInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed ChannelSenderInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            accountSid: this.accountSid,
            messagingServiceSid: this.messagingServiceSid,
            sid: this.sid,
            sender: this.sender,
            senderType: this.senderType,
            countryCode: this.countryCode,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ChannelSenderInstance = ChannelSenderInstance;
function ChannelSenderListInstance(version, messagingServiceSid) {
    if (!(0, utility_1.isValidPathParam)(messagingServiceSid)) {
        throw new Error("Parameter 'messagingServiceSid' is not valid.");
    }
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new ChannelSenderContextImpl(version, messagingServiceSid, sid);
    };
    instance._version = version;
    instance._solution = { messagingServiceSid };
    instance._uri = `/Services/${messagingServiceSid}/ChannelSenders`;
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new ChannelSenderPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new ChannelSenderPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.ChannelSenderListInstance = ChannelSenderListInstance;
class ChannelSenderPage extends Page_1.default {
    /**
     * Initialize the ChannelSenderPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of ChannelSenderInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new ChannelSenderInstance(this._version, payload, this._solution.messagingServiceSid);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ChannelSenderPage = ChannelSenderPage;

"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Messaging
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.LinkshorteningMessagingServiceDomainAssociationListInstance = exports.LinkshorteningMessagingServiceDomainAssociationInstance = exports.LinkshorteningMessagingServiceDomainAssociationContextImpl = void 0;
const util_1 = require("util");
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
class LinkshorteningMessagingServiceDomainAssociationContextImpl {
    constructor(_version, messagingServiceSid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(messagingServiceSid)) {
            throw new Error("Parameter 'messagingServiceSid' is not valid.");
        }
        this._solution = { messagingServiceSid };
        this._uri = `/LinkShortening/MessagingServices/${messagingServiceSid}/Domain`;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new LinkshorteningMessagingServiceDomainAssociationInstance(operationVersion, payload, instance._solution.messagingServiceSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.LinkshorteningMessagingServiceDomainAssociationContextImpl = LinkshorteningMessagingServiceDomainAssociationContextImpl;
class LinkshorteningMessagingServiceDomainAssociationInstance {
    constructor(_version, payload, messagingServiceSid) {
        this._version = _version;
        this.domainSid = payload.domain_sid;
        this.messagingServiceSid = payload.messaging_service_sid;
        this.url = payload.url;
        this._solution = {
            messagingServiceSid: messagingServiceSid || this.messagingServiceSid,
        };
    }
    get _proxy() {
        this._context =
            this._context ||
                new LinkshorteningMessagingServiceDomainAssociationContextImpl(this._version, this._solution.messagingServiceSid);
        return this._context;
    }
    /**
     * Fetch a LinkshorteningMessagingServiceDomainAssociationInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed LinkshorteningMessagingServiceDomainAssociationInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            domainSid: this.domainSid,
            messagingServiceSid: this.messagingServiceSid,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.LinkshorteningMessagingServiceDomainAssociationInstance = LinkshorteningMessagingServiceDomainAssociationInstance;
function LinkshorteningMessagingServiceDomainAssociationListInstance(version) {
    const instance = ((messagingServiceSid) => instance.get(messagingServiceSid));
    instance.get = function get(messagingServiceSid) {
        return new LinkshorteningMessagingServiceDomainAssociationContextImpl(version, messagingServiceSid);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = ``;
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.LinkshorteningMessagingServiceDomainAssociationListInstance = LinkshorteningMessagingServiceDomainAssociationListInstance;

"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Accounts
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.CredentialListInstance = void 0;
const util_1 = require("util");
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const aws_1 = require("./credential/aws");
const publicKey_1 = require("./credential/publicKey");
function CredentialListInstance(version) {
    const instance = {};
    instance._version = version;
    instance._solution = {};
    instance._uri = `/Credentials`;
    Object.defineProperty(instance, "aws", {
        get: function aws() {
            if (!instance._aws) {
                instance._aws = (0, aws_1.AwsListInstance)(instance._version);
            }
            return instance._aws;
        },
    });
    Object.defineProperty(instance, "publicKey", {
        get: function publicKey() {
            if (!instance._publicKey) {
                instance._publicKey = (0, publicKey_1.PublicKeyListInstance)(instance._version);
            }
            return instance._publicKey;
        },
    });
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.CredentialListInstance = CredentialListInstance;

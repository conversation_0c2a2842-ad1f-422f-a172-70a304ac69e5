"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Content
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const Version_1 = __importDefault(require("../../base/Version"));
const content_1 = require("./v1/content");
const contentAndApprovals_1 = require("./v1/contentAndApprovals");
const legacyContent_1 = require("./v1/legacyContent");
class V1 extends Version_1.default {
    /**
     * Initialize the V1 version of Content
     *
     * @param domain - The Twilio (Twilio.Content) domain
     */
    constructor(domain) {
        super(domain, "v1");
    }
    /** Getter for contents resource */
    get contents() {
        this._contents = this._contents || (0, content_1.ContentListInstance)(this);
        return this._contents;
    }
    /** Getter for contentAndApprovals resource */
    get contentAndApprovals() {
        this._contentAndApprovals =
            this._contentAndApprovals || (0, contentAndApprovals_1.ContentAndApprovalsListInstance)(this);
        return this._contentAndApprovals;
    }
    /** Getter for legacyContents resource */
    get legacyContents() {
        this._legacyContents =
            this._legacyContents || (0, legacyContent_1.LegacyContentListInstance)(this);
        return this._legacyContents;
    }
}
exports.default = V1;

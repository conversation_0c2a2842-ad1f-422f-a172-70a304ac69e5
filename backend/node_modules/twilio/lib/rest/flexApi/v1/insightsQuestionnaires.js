"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Flex
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InsightsQuestionnairesPage = exports.InsightsQuestionnairesListInstance = exports.InsightsQuestionnairesInstance = exports.InsightsQuestionnairesContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../base/Page"));
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
class InsightsQuestionnairesContextImpl {
    constructor(_version, questionnaireSid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(questionnaireSid)) {
            throw new Error("Parameter 'questionnaireSid' is not valid.");
        }
        this._solution = { questionnaireSid };
        this._uri = `/Insights/QualityManagement/Questionnaires/${questionnaireSid}`;
    }
    remove(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        const headers = {};
        if (params["authorization"] !== undefined)
            headers["Authorization"] = params["authorization"];
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.remove({
            uri: instance._uri,
            method: "delete",
            params: data,
            headers,
        });
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    fetch(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        const headers = {};
        if (params["authorization"] !== undefined)
            headers["Authorization"] = params["authorization"];
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new InsightsQuestionnairesInstance(operationVersion, payload, instance._solution.questionnaireSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["active"] === null || params["active"] === undefined) {
            throw new Error("Required parameter \"params['active']\" missing.");
        }
        let data = {};
        data["Active"] = serialize.bool(params["active"]);
        if (params["name"] !== undefined)
            data["Name"] = params["name"];
        if (params["description"] !== undefined)
            data["Description"] = params["description"];
        if (params["questionSids"] !== undefined)
            data["QuestionSids"] = serialize.map(params["questionSids"], (e) => e);
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        if (params["authorization"] !== undefined)
            headers["Authorization"] = params["authorization"];
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new InsightsQuestionnairesInstance(operationVersion, payload, instance._solution.questionnaireSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.InsightsQuestionnairesContextImpl = InsightsQuestionnairesContextImpl;
class InsightsQuestionnairesInstance {
    constructor(_version, payload, questionnaireSid) {
        this._version = _version;
        this.accountSid = payload.account_sid;
        this.questionnaireSid = payload.questionnaire_sid;
        this.name = payload.name;
        this.description = payload.description;
        this.active = payload.active;
        this.questions = payload.questions;
        this.url = payload.url;
        this._solution = {
            questionnaireSid: questionnaireSid || this.questionnaireSid,
        };
    }
    get _proxy() {
        this._context =
            this._context ||
                new InsightsQuestionnairesContextImpl(this._version, this._solution.questionnaireSid);
        return this._context;
    }
    remove(params, callback) {
        return this._proxy.remove(params, callback);
    }
    fetch(params, callback) {
        return this._proxy.fetch(params, callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            accountSid: this.accountSid,
            questionnaireSid: this.questionnaireSid,
            name: this.name,
            description: this.description,
            active: this.active,
            questions: this.questions,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.InsightsQuestionnairesInstance = InsightsQuestionnairesInstance;
function InsightsQuestionnairesListInstance(version) {
    const instance = ((questionnaireSid) => instance.get(questionnaireSid));
    instance.get = function get(questionnaireSid) {
        return new InsightsQuestionnairesContextImpl(version, questionnaireSid);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = `/Insights/QualityManagement/Questionnaires`;
    instance.create = function create(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["name"] === null || params["name"] === undefined) {
            throw new Error("Required parameter \"params['name']\" missing.");
        }
        let data = {};
        data["Name"] = params["name"];
        if (params["description"] !== undefined)
            data["Description"] = params["description"];
        if (params["active"] !== undefined)
            data["Active"] = serialize.bool(params["active"]);
        if (params["questionSids"] !== undefined)
            data["QuestionSids"] = serialize.map(params["questionSids"], (e) => e);
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        if (params["authorization"] !== undefined)
            headers["Authorization"] = params["authorization"];
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new InsightsQuestionnairesInstance(operationVersion, payload));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["includeInactive"] !== undefined)
            data["IncludeInactive"] = serialize.bool(params["includeInactive"]);
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        if (params["authorization"] !== undefined)
            headers["Authorization"] = params["authorization"];
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new InsightsQuestionnairesPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new InsightsQuestionnairesPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.InsightsQuestionnairesListInstance = InsightsQuestionnairesListInstance;
class InsightsQuestionnairesPage extends Page_1.default {
    /**
     * Initialize the InsightsQuestionnairesPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of InsightsQuestionnairesInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new InsightsQuestionnairesInstance(this._version, payload);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.InsightsQuestionnairesPage = InsightsQuestionnairesPage;

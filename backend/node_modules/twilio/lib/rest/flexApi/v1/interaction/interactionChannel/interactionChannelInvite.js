"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Flex
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InteractionChannelInvitePage = exports.InteractionChannelInviteInstance = exports.InteractionChannelInviteListInstance = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../../base/Page"));
const deserialize = require("../../../../../base/deserialize");
const serialize = require("../../../../../base/serialize");
const utility_1 = require("../../../../../base/utility");
function InteractionChannelInviteListInstance(version, interactionSid, channelSid) {
    if (!(0, utility_1.isValidPathParam)(interactionSid)) {
        throw new Error("Parameter 'interactionSid' is not valid.");
    }
    if (!(0, utility_1.isValidPathParam)(channelSid)) {
        throw new Error("Parameter 'channelSid' is not valid.");
    }
    const instance = {};
    instance._version = version;
    instance._solution = { interactionSid, channelSid };
    instance._uri = `/Interactions/${interactionSid}/Channels/${channelSid}/Invites`;
    instance.create = function create(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["routing"] === null || params["routing"] === undefined) {
            throw new Error("Required parameter \"params['routing']\" missing.");
        }
        let data = {};
        data["Routing"] = serialize.object(params["routing"]);
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new InteractionChannelInviteInstance(operationVersion, payload, instance._solution.interactionSid, instance._solution.channelSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new InteractionChannelInvitePage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new InteractionChannelInvitePage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.InteractionChannelInviteListInstance = InteractionChannelInviteListInstance;
class InteractionChannelInviteInstance {
    constructor(_version, payload, interactionSid, channelSid) {
        this._version = _version;
        this.sid = payload.sid;
        this.interactionSid = payload.interaction_sid;
        this.channelSid = payload.channel_sid;
        this.routing = payload.routing;
        this.url = payload.url;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            sid: this.sid,
            interactionSid: this.interactionSid,
            channelSid: this.channelSid,
            routing: this.routing,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.InteractionChannelInviteInstance = InteractionChannelInviteInstance;
class InteractionChannelInvitePage extends Page_1.default {
    /**
     * Initialize the InteractionChannelInvitePage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of InteractionChannelInviteInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new InteractionChannelInviteInstance(this._version, payload, this._solution.interactionSid, this._solution.channelSid);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.InteractionChannelInvitePage = InteractionChannelInvitePage;

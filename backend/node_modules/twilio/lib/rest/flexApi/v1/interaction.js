"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Flex
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.InteractionListInstance = exports.InteractionInstance = exports.InteractionContextImpl = void 0;
const util_1 = require("util");
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
const interactionChannel_1 = require("./interaction/interactionChannel");
class InteractionContextImpl {
    constructor(_version, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { sid };
        this._uri = `/Interactions/${sid}`;
    }
    get channels() {
        this._channels =
            this._channels ||
                (0, interactionChannel_1.InteractionChannelListInstance)(this._version, this._solution.sid);
        return this._channels;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new InteractionInstance(operationVersion, payload, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.InteractionContextImpl = InteractionContextImpl;
class InteractionInstance {
    constructor(_version, payload, sid) {
        this._version = _version;
        this.sid = payload.sid;
        this.channel = payload.channel;
        this.routing = payload.routing;
        this.url = payload.url;
        this.links = payload.links;
        this.interactionContextSid = payload.interaction_context_sid;
        this._solution = { sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new InteractionContextImpl(this._version, this._solution.sid);
        return this._context;
    }
    /**
     * Fetch a InteractionInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed InteractionInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    /**
     * Access the channels.
     */
    channels() {
        return this._proxy.channels;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            sid: this.sid,
            channel: this.channel,
            routing: this.routing,
            url: this.url,
            links: this.links,
            interactionContextSid: this.interactionContextSid,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.InteractionInstance = InteractionInstance;
function InteractionListInstance(version) {
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new InteractionContextImpl(version, sid);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = `/Interactions`;
    instance.create = function create(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["channel"] === null || params["channel"] === undefined) {
            throw new Error("Required parameter \"params['channel']\" missing.");
        }
        if (params["routing"] === null || params["routing"] === undefined) {
            throw new Error("Required parameter \"params['routing']\" missing.");
        }
        let data = {};
        data["Channel"] = serialize.object(params["channel"]);
        data["Routing"] = serialize.object(params["routing"]);
        if (params["interactionContextSid"] !== undefined)
            data["InteractionContextSid"] = params["interactionContextSid"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new InteractionInstance(operationVersion, payload));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.InteractionListInstance = InteractionListInstance;

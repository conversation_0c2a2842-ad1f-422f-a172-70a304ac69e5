"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Insights
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CallSummariesPage = exports.CallSummariesInstance = exports.CallSummariesListInstance = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../base/Page"));
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
function CallSummariesListInstance(version) {
    const instance = {};
    instance._version = version;
    instance._solution = {};
    instance._uri = `/Voice/Summaries`;
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["from"] !== undefined)
            data["From"] = params["from"];
        if (params["to"] !== undefined)
            data["To"] = params["to"];
        if (params["fromCarrier"] !== undefined)
            data["FromCarrier"] = params["fromCarrier"];
        if (params["toCarrier"] !== undefined)
            data["ToCarrier"] = params["toCarrier"];
        if (params["fromCountryCode"] !== undefined)
            data["FromCountryCode"] = params["fromCountryCode"];
        if (params["toCountryCode"] !== undefined)
            data["ToCountryCode"] = params["toCountryCode"];
        if (params["branded"] !== undefined)
            data["Branded"] = serialize.bool(params["branded"]);
        if (params["verifiedCaller"] !== undefined)
            data["VerifiedCaller"] = serialize.bool(params["verifiedCaller"]);
        if (params["hasTag"] !== undefined)
            data["HasTag"] = serialize.bool(params["hasTag"]);
        if (params["startTime"] !== undefined)
            data["StartTime"] = params["startTime"];
        if (params["endTime"] !== undefined)
            data["EndTime"] = params["endTime"];
        if (params["callType"] !== undefined)
            data["CallType"] = params["callType"];
        if (params["callState"] !== undefined)
            data["CallState"] = params["callState"];
        if (params["direction"] !== undefined)
            data["Direction"] = params["direction"];
        if (params["processingState"] !== undefined)
            data["ProcessingState"] = params["processingState"];
        if (params["sortBy"] !== undefined)
            data["SortBy"] = params["sortBy"];
        if (params["subaccount"] !== undefined)
            data["Subaccount"] = params["subaccount"];
        if (params["abnormalSession"] !== undefined)
            data["AbnormalSession"] = serialize.bool(params["abnormalSession"]);
        if (params["answeredBy"] !== undefined)
            data["AnsweredBy"] = params["answeredBy"];
        if (params["answeredByAnnotation"] !== undefined)
            data["AnsweredByAnnotation"] = params["answeredByAnnotation"];
        if (params["connectivityIssueAnnotation"] !== undefined)
            data["ConnectivityIssueAnnotation"] =
                params["connectivityIssueAnnotation"];
        if (params["qualityIssueAnnotation"] !== undefined)
            data["QualityIssueAnnotation"] = params["qualityIssueAnnotation"];
        if (params["spamAnnotation"] !== undefined)
            data["SpamAnnotation"] = serialize.bool(params["spamAnnotation"]);
        if (params["callScoreAnnotation"] !== undefined)
            data["CallScoreAnnotation"] = params["callScoreAnnotation"];
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new CallSummariesPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new CallSummariesPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.CallSummariesListInstance = CallSummariesListInstance;
class CallSummariesInstance {
    constructor(_version, payload) {
        this._version = _version;
        this.accountSid = payload.account_sid;
        this.callSid = payload.call_sid;
        this.answeredBy = payload.answered_by;
        this.callType = payload.call_type;
        this.callState = payload.call_state;
        this.processingState = payload.processing_state;
        this.createdTime = deserialize.iso8601DateTime(payload.created_time);
        this.startTime = deserialize.iso8601DateTime(payload.start_time);
        this.endTime = deserialize.iso8601DateTime(payload.end_time);
        this.duration = deserialize.integer(payload.duration);
        this.connectDuration = deserialize.integer(payload.connect_duration);
        this.from = payload.from;
        this.to = payload.to;
        this.carrierEdge = payload.carrier_edge;
        this.clientEdge = payload.client_edge;
        this.sdkEdge = payload.sdk_edge;
        this.sipEdge = payload.sip_edge;
        this.tags = payload.tags;
        this.url = payload.url;
        this.attributes = payload.attributes;
        this.properties = payload.properties;
        this.trust = payload.trust;
        this.annotation = payload.annotation;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            accountSid: this.accountSid,
            callSid: this.callSid,
            answeredBy: this.answeredBy,
            callType: this.callType,
            callState: this.callState,
            processingState: this.processingState,
            createdTime: this.createdTime,
            startTime: this.startTime,
            endTime: this.endTime,
            duration: this.duration,
            connectDuration: this.connectDuration,
            from: this.from,
            to: this.to,
            carrierEdge: this.carrierEdge,
            clientEdge: this.clientEdge,
            sdkEdge: this.sdkEdge,
            sipEdge: this.sipEdge,
            tags: this.tags,
            url: this.url,
            attributes: this.attributes,
            properties: this.properties,
            trust: this.trust,
            annotation: this.annotation,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.CallSummariesInstance = CallSummariesInstance;
class CallSummariesPage extends Page_1.default {
    /**
     * Initialize the CallSummariesPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of CallSummariesInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new CallSummariesInstance(this._version, payload);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.CallSummariesPage = CallSummariesPage;

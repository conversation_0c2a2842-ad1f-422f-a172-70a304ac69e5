"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Insights
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const Version_1 = __importDefault(require("../../base/Version"));
const call_1 = require("./v1/call");
const callSummaries_1 = require("./v1/callSummaries");
const conference_1 = require("./v1/conference");
const room_1 = require("./v1/room");
const setting_1 = require("./v1/setting");
class V1 extends Version_1.default {
    /**
     * Initialize the V1 version of Insights
     *
     * @param domain - The Twilio (Twilio.Insights) domain
     */
    constructor(domain) {
        super(domain, "v1");
    }
    /** Getter for calls resource */
    get calls() {
        this._calls = this._calls || (0, call_1.CallListInstance)(this);
        return this._calls;
    }
    /** Getter for callSummaries resource */
    get callSummaries() {
        this._callSummaries =
            this._callSummaries || (0, callSummaries_1.CallSummariesListInstance)(this);
        return this._callSummaries;
    }
    /** Getter for conferences resource */
    get conferences() {
        this._conferences = this._conferences || (0, conference_1.ConferenceListInstance)(this);
        return this._conferences;
    }
    /** Getter for rooms resource */
    get rooms() {
        this._rooms = this._rooms || (0, room_1.RoomListInstance)(this);
        return this._rooms;
    }
    /** Getter for settings resource */
    get settings() {
        this._settings = this._settings || (0, setting_1.SettingListInstance)(this);
        return this._settings;
    }
}
exports.default = V1;

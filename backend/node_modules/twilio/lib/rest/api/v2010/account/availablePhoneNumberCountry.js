"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Api
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AvailablePhoneNumberCountryPage = exports.AvailablePhoneNumberCountryListInstance = exports.AvailablePhoneNumberCountryInstance = exports.AvailablePhoneNumberCountryContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../base/Page"));
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
const utility_1 = require("../../../../base/utility");
const local_1 = require("./availablePhoneNumberCountry/local");
const machineToMachine_1 = require("./availablePhoneNumberCountry/machineToMachine");
const mobile_1 = require("./availablePhoneNumberCountry/mobile");
const national_1 = require("./availablePhoneNumberCountry/national");
const sharedCost_1 = require("./availablePhoneNumberCountry/sharedCost");
const tollFree_1 = require("./availablePhoneNumberCountry/tollFree");
const voip_1 = require("./availablePhoneNumberCountry/voip");
class AvailablePhoneNumberCountryContextImpl {
    constructor(_version, accountSid, countryCode) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(accountSid)) {
            throw new Error("Parameter 'accountSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(countryCode)) {
            throw new Error("Parameter 'countryCode' is not valid.");
        }
        this._solution = { accountSid, countryCode };
        this._uri = `/Accounts/${accountSid}/AvailablePhoneNumbers/${countryCode}.json`;
    }
    get local() {
        this._local =
            this._local ||
                (0, local_1.LocalListInstance)(this._version, this._solution.accountSid, this._solution.countryCode);
        return this._local;
    }
    get machineToMachine() {
        this._machineToMachine =
            this._machineToMachine ||
                (0, machineToMachine_1.MachineToMachineListInstance)(this._version, this._solution.accountSid, this._solution.countryCode);
        return this._machineToMachine;
    }
    get mobile() {
        this._mobile =
            this._mobile ||
                (0, mobile_1.MobileListInstance)(this._version, this._solution.accountSid, this._solution.countryCode);
        return this._mobile;
    }
    get national() {
        this._national =
            this._national ||
                (0, national_1.NationalListInstance)(this._version, this._solution.accountSid, this._solution.countryCode);
        return this._national;
    }
    get sharedCost() {
        this._sharedCost =
            this._sharedCost ||
                (0, sharedCost_1.SharedCostListInstance)(this._version, this._solution.accountSid, this._solution.countryCode);
        return this._sharedCost;
    }
    get tollFree() {
        this._tollFree =
            this._tollFree ||
                (0, tollFree_1.TollFreeListInstance)(this._version, this._solution.accountSid, this._solution.countryCode);
        return this._tollFree;
    }
    get voip() {
        this._voip =
            this._voip ||
                (0, voip_1.VoipListInstance)(this._version, this._solution.accountSid, this._solution.countryCode);
        return this._voip;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new AvailablePhoneNumberCountryInstance(operationVersion, payload, instance._solution.accountSid, instance._solution.countryCode));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.AvailablePhoneNumberCountryContextImpl = AvailablePhoneNumberCountryContextImpl;
class AvailablePhoneNumberCountryInstance {
    constructor(_version, payload, accountSid, countryCode) {
        this._version = _version;
        this.countryCode = payload.country_code;
        this.country = payload.country;
        this.uri = payload.uri;
        this.beta = payload.beta;
        this.subresourceUris = payload.subresource_uris;
        this._solution = {
            accountSid,
            countryCode: countryCode || this.countryCode,
        };
    }
    get _proxy() {
        this._context =
            this._context ||
                new AvailablePhoneNumberCountryContextImpl(this._version, this._solution.accountSid, this._solution.countryCode);
        return this._context;
    }
    /**
     * Fetch a AvailablePhoneNumberCountryInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed AvailablePhoneNumberCountryInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    /**
     * Access the local.
     */
    local() {
        return this._proxy.local;
    }
    /**
     * Access the machineToMachine.
     */
    machineToMachine() {
        return this._proxy.machineToMachine;
    }
    /**
     * Access the mobile.
     */
    mobile() {
        return this._proxy.mobile;
    }
    /**
     * Access the national.
     */
    national() {
        return this._proxy.national;
    }
    /**
     * Access the sharedCost.
     */
    sharedCost() {
        return this._proxy.sharedCost;
    }
    /**
     * Access the tollFree.
     */
    tollFree() {
        return this._proxy.tollFree;
    }
    /**
     * Access the voip.
     */
    voip() {
        return this._proxy.voip;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            countryCode: this.countryCode,
            country: this.country,
            uri: this.uri,
            beta: this.beta,
            subresourceUris: this.subresourceUris,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.AvailablePhoneNumberCountryInstance = AvailablePhoneNumberCountryInstance;
function AvailablePhoneNumberCountryListInstance(version, accountSid) {
    if (!(0, utility_1.isValidPathParam)(accountSid)) {
        throw new Error("Parameter 'accountSid' is not valid.");
    }
    const instance = ((countryCode) => instance.get(countryCode));
    instance.get = function get(countryCode) {
        return new AvailablePhoneNumberCountryContextImpl(version, accountSid, countryCode);
    };
    instance._version = version;
    instance._solution = { accountSid };
    instance._uri = `/Accounts/${accountSid}/AvailablePhoneNumbers.json`;
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new AvailablePhoneNumberCountryPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new AvailablePhoneNumberCountryPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.AvailablePhoneNumberCountryListInstance = AvailablePhoneNumberCountryListInstance;
class AvailablePhoneNumberCountryPage extends Page_1.default {
    /**
     * Initialize the AvailablePhoneNumberCountryPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of AvailablePhoneNumberCountryInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new AvailablePhoneNumberCountryInstance(this._version, payload, this._solution.accountSid);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.AvailablePhoneNumberCountryPage = AvailablePhoneNumberCountryPage;

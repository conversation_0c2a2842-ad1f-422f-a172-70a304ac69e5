"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Api
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthorizedConnectAppPage = exports.AuthorizedConnectAppListInstance = exports.AuthorizedConnectAppInstance = exports.AuthorizedConnectAppContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../base/Page"));
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
const utility_1 = require("../../../../base/utility");
class AuthorizedConnectAppContextImpl {
    constructor(_version, accountSid, connectAppSid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(accountSid)) {
            throw new Error("Parameter 'accountSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(connectAppSid)) {
            throw new Error("Parameter 'connectAppSid' is not valid.");
        }
        this._solution = { accountSid, connectAppSid };
        this._uri = `/Accounts/${accountSid}/AuthorizedConnectApps/${connectAppSid}.json`;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new AuthorizedConnectAppInstance(operationVersion, payload, instance._solution.accountSid, instance._solution.connectAppSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.AuthorizedConnectAppContextImpl = AuthorizedConnectAppContextImpl;
class AuthorizedConnectAppInstance {
    constructor(_version, payload, accountSid, connectAppSid) {
        this._version = _version;
        this.accountSid = payload.account_sid;
        this.connectAppCompanyName = payload.connect_app_company_name;
        this.connectAppDescription = payload.connect_app_description;
        this.connectAppFriendlyName = payload.connect_app_friendly_name;
        this.connectAppHomepageUrl = payload.connect_app_homepage_url;
        this.connectAppSid = payload.connect_app_sid;
        this.permissions = payload.permissions;
        this.uri = payload.uri;
        this._solution = {
            accountSid,
            connectAppSid: connectAppSid || this.connectAppSid,
        };
    }
    get _proxy() {
        this._context =
            this._context ||
                new AuthorizedConnectAppContextImpl(this._version, this._solution.accountSid, this._solution.connectAppSid);
        return this._context;
    }
    /**
     * Fetch a AuthorizedConnectAppInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed AuthorizedConnectAppInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            accountSid: this.accountSid,
            connectAppCompanyName: this.connectAppCompanyName,
            connectAppDescription: this.connectAppDescription,
            connectAppFriendlyName: this.connectAppFriendlyName,
            connectAppHomepageUrl: this.connectAppHomepageUrl,
            connectAppSid: this.connectAppSid,
            permissions: this.permissions,
            uri: this.uri,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.AuthorizedConnectAppInstance = AuthorizedConnectAppInstance;
function AuthorizedConnectAppListInstance(version, accountSid) {
    if (!(0, utility_1.isValidPathParam)(accountSid)) {
        throw new Error("Parameter 'accountSid' is not valid.");
    }
    const instance = ((connectAppSid) => instance.get(connectAppSid));
    instance.get = function get(connectAppSid) {
        return new AuthorizedConnectAppContextImpl(version, accountSid, connectAppSid);
    };
    instance._version = version;
    instance._solution = { accountSid };
    instance._uri = `/Accounts/${accountSid}/AuthorizedConnectApps.json`;
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new AuthorizedConnectAppPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new AuthorizedConnectAppPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.AuthorizedConnectAppListInstance = AuthorizedConnectAppListInstance;
class AuthorizedConnectAppPage extends Page_1.default {
    /**
     * Initialize the AuthorizedConnectAppPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of AuthorizedConnectAppInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new AuthorizedConnectAppInstance(this._version, payload, this._solution.accountSid);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.AuthorizedConnectAppPage = AuthorizedConnectAppPage;

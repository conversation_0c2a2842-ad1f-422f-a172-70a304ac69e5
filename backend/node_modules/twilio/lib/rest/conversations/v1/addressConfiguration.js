"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Conversations
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddressConfigurationPage = exports.AddressConfigurationListInstance = exports.AddressConfigurationInstance = exports.AddressConfigurationContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../base/Page"));
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
class AddressConfigurationContextImpl {
    constructor(_version, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { sid };
        this._uri = `/Configuration/Addresses/${sid}`;
    }
    remove(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.remove({
            uri: instance._uri,
            method: "delete",
        });
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new AddressConfigurationInstance(operationVersion, payload, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["friendlyName"] !== undefined)
            data["FriendlyName"] = params["friendlyName"];
        if (params["autoCreation.enabled"] !== undefined)
            data["AutoCreation.Enabled"] = serialize.bool(params["autoCreation.enabled"]);
        if (params["autoCreation.type"] !== undefined)
            data["AutoCreation.Type"] = params["autoCreation.type"];
        if (params["autoCreation.conversationServiceSid"] !== undefined)
            data["AutoCreation.ConversationServiceSid"] =
                params["autoCreation.conversationServiceSid"];
        if (params["autoCreation.webhookUrl"] !== undefined)
            data["AutoCreation.WebhookUrl"] = params["autoCreation.webhookUrl"];
        if (params["autoCreation.webhookMethod"] !== undefined)
            data["AutoCreation.WebhookMethod"] = params["autoCreation.webhookMethod"];
        if (params["autoCreation.webhookFilters"] !== undefined)
            data["AutoCreation.WebhookFilters"] = serialize.map(params["autoCreation.webhookFilters"], (e) => e);
        if (params["autoCreation.studioFlowSid"] !== undefined)
            data["AutoCreation.StudioFlowSid"] = params["autoCreation.studioFlowSid"];
        if (params["autoCreation.studioRetryCount"] !== undefined)
            data["AutoCreation.StudioRetryCount"] =
                params["autoCreation.studioRetryCount"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new AddressConfigurationInstance(operationVersion, payload, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.AddressConfigurationContextImpl = AddressConfigurationContextImpl;
class AddressConfigurationInstance {
    constructor(_version, payload, sid) {
        this._version = _version;
        this.sid = payload.sid;
        this.accountSid = payload.account_sid;
        this.type = payload.type;
        this.address = payload.address;
        this.friendlyName = payload.friendly_name;
        this.autoCreation = payload.auto_creation;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.url = payload.url;
        this.addressCountry = payload.address_country;
        this._solution = { sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new AddressConfigurationContextImpl(this._version, this._solution.sid);
        return this._context;
    }
    /**
     * Remove a AddressConfigurationInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed boolean
     */
    remove(callback) {
        return this._proxy.remove(callback);
    }
    /**
     * Fetch a AddressConfigurationInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed AddressConfigurationInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            sid: this.sid,
            accountSid: this.accountSid,
            type: this.type,
            address: this.address,
            friendlyName: this.friendlyName,
            autoCreation: this.autoCreation,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            url: this.url,
            addressCountry: this.addressCountry,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.AddressConfigurationInstance = AddressConfigurationInstance;
function AddressConfigurationListInstance(version) {
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new AddressConfigurationContextImpl(version, sid);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = `/Configuration/Addresses`;
    instance.create = function create(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["type"] === null || params["type"] === undefined) {
            throw new Error("Required parameter \"params['type']\" missing.");
        }
        if (params["address"] === null || params["address"] === undefined) {
            throw new Error("Required parameter \"params['address']\" missing.");
        }
        let data = {};
        data["Type"] = params["type"];
        data["Address"] = params["address"];
        if (params["friendlyName"] !== undefined)
            data["FriendlyName"] = params["friendlyName"];
        if (params["autoCreation.enabled"] !== undefined)
            data["AutoCreation.Enabled"] = serialize.bool(params["autoCreation.enabled"]);
        if (params["autoCreation.type"] !== undefined)
            data["AutoCreation.Type"] = params["autoCreation.type"];
        if (params["autoCreation.conversationServiceSid"] !== undefined)
            data["AutoCreation.ConversationServiceSid"] =
                params["autoCreation.conversationServiceSid"];
        if (params["autoCreation.webhookUrl"] !== undefined)
            data["AutoCreation.WebhookUrl"] = params["autoCreation.webhookUrl"];
        if (params["autoCreation.webhookMethod"] !== undefined)
            data["AutoCreation.WebhookMethod"] = params["autoCreation.webhookMethod"];
        if (params["autoCreation.webhookFilters"] !== undefined)
            data["AutoCreation.WebhookFilters"] = serialize.map(params["autoCreation.webhookFilters"], (e) => e);
        if (params["autoCreation.studioFlowSid"] !== undefined)
            data["AutoCreation.StudioFlowSid"] = params["autoCreation.studioFlowSid"];
        if (params["autoCreation.studioRetryCount"] !== undefined)
            data["AutoCreation.StudioRetryCount"] =
                params["autoCreation.studioRetryCount"];
        if (params["addressCountry"] !== undefined)
            data["AddressCountry"] = params["addressCountry"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new AddressConfigurationInstance(operationVersion, payload));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["type"] !== undefined)
            data["Type"] = params["type"];
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new AddressConfigurationPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new AddressConfigurationPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.AddressConfigurationListInstance = AddressConfigurationListInstance;
class AddressConfigurationPage extends Page_1.default {
    /**
     * Initialize the AddressConfigurationPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of AddressConfigurationInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new AddressConfigurationInstance(this._version, payload);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.AddressConfigurationPage = AddressConfigurationPage;

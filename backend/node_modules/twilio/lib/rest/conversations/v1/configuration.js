"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Conversations
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigurationListInstance = exports.ConfigurationInstance = exports.ConfigurationContextImpl = void 0;
const util_1 = require("util");
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const webhook_1 = require("./configuration/webhook");
class ConfigurationContextImpl {
    constructor(_version) {
        this._version = _version;
        this._solution = {};
        this._uri = `/Configuration`;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new ConfigurationInstance(operationVersion, payload));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["defaultChatServiceSid"] !== undefined)
            data["DefaultChatServiceSid"] = params["defaultChatServiceSid"];
        if (params["defaultMessagingServiceSid"] !== undefined)
            data["DefaultMessagingServiceSid"] = params["defaultMessagingServiceSid"];
        if (params["defaultInactiveTimer"] !== undefined)
            data["DefaultInactiveTimer"] = params["defaultInactiveTimer"];
        if (params["defaultClosedTimer"] !== undefined)
            data["DefaultClosedTimer"] = params["defaultClosedTimer"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new ConfigurationInstance(operationVersion, payload));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ConfigurationContextImpl = ConfigurationContextImpl;
class ConfigurationInstance {
    constructor(_version, payload) {
        this._version = _version;
        this.accountSid = payload.account_sid;
        this.defaultChatServiceSid = payload.default_chat_service_sid;
        this.defaultMessagingServiceSid = payload.default_messaging_service_sid;
        this.defaultInactiveTimer = payload.default_inactive_timer;
        this.defaultClosedTimer = payload.default_closed_timer;
        this.url = payload.url;
        this.links = payload.links;
        this._solution = {};
    }
    get _proxy() {
        this._context =
            this._context || new ConfigurationContextImpl(this._version);
        return this._context;
    }
    /**
     * Fetch a ConfigurationInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed ConfigurationInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            accountSid: this.accountSid,
            defaultChatServiceSid: this.defaultChatServiceSid,
            defaultMessagingServiceSid: this.defaultMessagingServiceSid,
            defaultInactiveTimer: this.defaultInactiveTimer,
            defaultClosedTimer: this.defaultClosedTimer,
            url: this.url,
            links: this.links,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ConfigurationInstance = ConfigurationInstance;
function ConfigurationListInstance(version) {
    const instance = (() => instance.get());
    instance.get = function get() {
        return new ConfigurationContextImpl(version);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = ``;
    Object.defineProperty(instance, "webhooks", {
        get: function webhooks() {
            if (!instance._webhooks) {
                instance._webhooks = (0, webhook_1.WebhookListInstance)(instance._version);
            }
            return instance._webhooks;
        },
    });
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.ConfigurationListInstance = ConfigurationListInstance;

"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Events
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SinkPage = exports.SinkListInstance = exports.SinkInstance = exports.SinkContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../base/Page"));
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
const sinkTest_1 = require("./sink/sinkTest");
const sinkValidate_1 = require("./sink/sinkValidate");
class SinkContextImpl {
    constructor(_version, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { sid };
        this._uri = `/Sinks/${sid}`;
    }
    get sinkTest() {
        this._sinkTest =
            this._sinkTest || (0, sinkTest_1.SinkTestListInstance)(this._version, this._solution.sid);
        return this._sinkTest;
    }
    get sinkValidate() {
        this._sinkValidate =
            this._sinkValidate ||
                (0, sinkValidate_1.SinkValidateListInstance)(this._version, this._solution.sid);
        return this._sinkValidate;
    }
    remove(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.remove({
            uri: instance._uri,
            method: "delete",
        });
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new SinkInstance(operationVersion, payload, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["description"] === null || params["description"] === undefined) {
            throw new Error("Required parameter \"params['description']\" missing.");
        }
        let data = {};
        data["Description"] = params["description"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new SinkInstance(operationVersion, payload, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.SinkContextImpl = SinkContextImpl;
class SinkInstance {
    constructor(_version, payload, sid) {
        this._version = _version;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.description = payload.description;
        this.sid = payload.sid;
        this.sinkConfiguration = payload.sink_configuration;
        this.sinkType = payload.sink_type;
        this.status = payload.status;
        this.url = payload.url;
        this.links = payload.links;
        this._solution = { sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context || new SinkContextImpl(this._version, this._solution.sid);
        return this._context;
    }
    /**
     * Remove a SinkInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed boolean
     */
    remove(callback) {
        return this._proxy.remove(callback);
    }
    /**
     * Fetch a SinkInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed SinkInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Access the sinkTest.
     */
    sinkTest() {
        return this._proxy.sinkTest;
    }
    /**
     * Access the sinkValidate.
     */
    sinkValidate() {
        return this._proxy.sinkValidate;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            description: this.description,
            sid: this.sid,
            sinkConfiguration: this.sinkConfiguration,
            sinkType: this.sinkType,
            status: this.status,
            url: this.url,
            links: this.links,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.SinkInstance = SinkInstance;
function SinkListInstance(version) {
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new SinkContextImpl(version, sid);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = `/Sinks`;
    instance.create = function create(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["description"] === null || params["description"] === undefined) {
            throw new Error("Required parameter \"params['description']\" missing.");
        }
        if (params["sinkConfiguration"] === null ||
            params["sinkConfiguration"] === undefined) {
            throw new Error("Required parameter \"params['sinkConfiguration']\" missing.");
        }
        if (params["sinkType"] === null || params["sinkType"] === undefined) {
            throw new Error("Required parameter \"params['sinkType']\" missing.");
        }
        let data = {};
        data["Description"] = params["description"];
        data["SinkConfiguration"] = serialize.object(params["sinkConfiguration"]);
        data["SinkType"] = params["sinkType"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new SinkInstance(operationVersion, payload));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["inUse"] !== undefined)
            data["InUse"] = serialize.bool(params["inUse"]);
        if (params["status"] !== undefined)
            data["Status"] = params["status"];
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new SinkPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new SinkPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.SinkListInstance = SinkListInstance;
class SinkPage extends Page_1.default {
    /**
     * Initialize the SinkPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of SinkInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new SinkInstance(this._version, payload);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.SinkPage = SinkPage;

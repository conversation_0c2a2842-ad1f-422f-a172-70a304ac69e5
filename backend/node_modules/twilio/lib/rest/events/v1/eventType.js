"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Events
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventTypePage = exports.EventTypeListInstance = exports.EventTypeInstance = exports.EventTypeContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../base/Page"));
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
class EventTypeContextImpl {
    constructor(_version, type) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(type)) {
            throw new Error("Parameter 'type' is not valid.");
        }
        this._solution = { type };
        this._uri = `/Types/${type}`;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new EventTypeInstance(operationVersion, payload, instance._solution.type));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.EventTypeContextImpl = EventTypeContextImpl;
class EventTypeInstance {
    constructor(_version, payload, type) {
        this._version = _version;
        this.type = payload.type;
        this.schemaId = payload.schema_id;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.description = payload.description;
        this.url = payload.url;
        this.links = payload.links;
        this._solution = { type: type || this.type };
    }
    get _proxy() {
        this._context =
            this._context ||
                new EventTypeContextImpl(this._version, this._solution.type);
        return this._context;
    }
    /**
     * Fetch a EventTypeInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed EventTypeInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            type: this.type,
            schemaId: this.schemaId,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            description: this.description,
            url: this.url,
            links: this.links,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.EventTypeInstance = EventTypeInstance;
function EventTypeListInstance(version) {
    const instance = ((type) => instance.get(type));
    instance.get = function get(type) {
        return new EventTypeContextImpl(version, type);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = `/Types`;
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["schemaId"] !== undefined)
            data["SchemaId"] = params["schemaId"];
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new EventTypePage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new EventTypePage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.EventTypeListInstance = EventTypeListInstance;
class EventTypePage extends Page_1.default {
    /**
     * Initialize the EventTypePage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of EventTypeInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new EventTypeInstance(this._version, payload);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.EventTypePage = EventTypePage;

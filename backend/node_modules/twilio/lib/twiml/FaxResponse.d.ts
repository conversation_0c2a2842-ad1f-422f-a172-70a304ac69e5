/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
import { XMLElement } from "xmlbuilder";
import TwiML from "./TwiML";
declare class FaxResponse extends TwiML {
    /**
     * <Response> TwiML for Faxes
     */
    constructor();
    /**
     * Comments in <Response>
     *
     * @param comment - XML Comment
     */
    comment(comment: string): XMLElement;
    /**
     * Comments after <Response>
     *
     * @param comment - XML Comment
     */
    commentAfter(comment: string): XMLElement;
    /**
     * Comments before <Response>
     *
     * @param comment - XML Comment
     */
    commentBefore(comment: string): XMLElement;
    /**
     * <Receive> TwiML Verb
     *
     * @param attributes - TwiML attributes
     */
    receive(attributes?: FaxResponse.ReceiveAttributes): FaxResponse.Receive;
}
declare namespace FaxResponse {
    type ReceiveMediaType = "application/pdf" | "image/tiff";
    type ReceivePageSize = "letter" | "legal" | "a4";
    /**
     * Attributes to pass to receive
     */
    export interface ReceiveAttributes {
        /** action - Receive action URL */
        action?: string;
        /** mediaType - The media type used to store media in the fax media store */
        mediaType?: ReceiveMediaType;
        /** method - Receive action URL method */
        method?: string;
        /** pageSize - What size to interpret received pages as */
        pageSize?: ReceivePageSize;
        /** storeMedia - Whether or not to store received media in the fax media store */
        storeMedia?: boolean;
    }
    export class Receive extends TwiML {
        receive: XMLElement;
        /**
         * <Receive> TwiML Verb
         */
        constructor(receive: XMLElement);
    }
    export {};
}
export = FaxResponse;

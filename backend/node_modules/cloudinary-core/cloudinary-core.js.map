{"version": 3, "sources": ["webpack://cloudinary/webpack/universalModuleDefinition", "webpack://cloudinary/webpack/bootstrap", "webpack://cloudinary/./src/utf8_encode.js", "webpack://cloudinary/./src/crc32.js", "webpack://cloudinary/./src/sdkAnalytics/stringPad.js", "webpack://cloudinary/./src/sdkAnalytics/base64Map.js", "webpack://cloudinary/./src/sdkAnalytics/reverseVersion.js", "webpack://cloudinary/./src/sdkAnalytics/encodeVersion.js", "webpack://cloudinary/./src/sdkAnalytics/getSDKAnalyticsSignature.js", "webpack://cloudinary/./src/sdkAnalytics/getAnalyticsOptions.js", "webpack://cloudinary/./src/util/lazyLoad.js", "webpack://cloudinary/./src/constants.js", "webpack://cloudinary/./src/util/baseutil.js", "webpack://cloudinary/./src/util/browser.js", "webpack://cloudinary/./src/util/lodash.js", "webpack://cloudinary/./src/expression.js", "webpack://cloudinary/./src/condition.js", "webpack://cloudinary/./src/configuration.js", "webpack://cloudinary/./src/layer/layer.js", "webpack://cloudinary/./src/layer/textlayer.js", "webpack://cloudinary/./src/layer/subtitleslayer.js", "webpack://cloudinary/./src/layer/fetchlayer.js", "webpack://cloudinary/./src/parameters.js", "webpack://cloudinary/./src/transformation.js", "webpack://cloudinary/./src/tags/htmltag.js", "webpack://cloudinary/./src/url.js", "webpack://cloudinary/./src/util/generateBreakpoints.js", "webpack://cloudinary/./src/util/srcsetUtils.js", "webpack://cloudinary/./src/tags/imagetag.js", "webpack://cloudinary/./src/tags/sourcetag.js", "webpack://cloudinary/./src/tags/picturetag.js", "webpack://cloudinary/./src/tags/videotag.js", "webpack://cloudinary/./src/tags/clienthintsmetatag.js", "webpack://cloudinary/./src/util/parse/normalizeToArray.js", "webpack://cloudinary/./src/util/features/transparentVideo/mountCloudinaryVideoTag.js", "webpack://cloudinary/./src/util/transformations/addFlag.js", "webpack://cloudinary/./src/util/features/transparentVideo/enforceOptionsForTransparentVideo.js", "webpack://cloudinary/./src/util/xhr/loadScript.js", "webpack://cloudinary/./src/util/xhr/getBlobFromURL.js", "webpack://cloudinary/./src/util/features/transparentVideo/createHiddenVideoTag.js", "webpack://cloudinary/./src/util/features/transparentVideo/instantiateSeeThru.js", "webpack://cloudinary/./src/util/features/transparentVideo/mountSeeThruCanvasTag.js", "webpack://cloudinary/./src/util/features/transparentVideo/checkSupportForTransparency.js", "webpack://cloudinary/./src/cloudinary.js", "webpack://cloudinary/./src/namespace/cloudinary-core.js", "webpack://cloudinary/external {\"commonjs\":\"lodash/assign\",\"commonjs2\":\"lodash/assign\",\"amd\":\"lodash/assign\",\"root\":[\"_\",\"assign\"]}", "webpack://cloudinary/external {\"commonjs\":\"lodash/cloneDeep\",\"commonjs2\":\"lodash/cloneDeep\",\"amd\":\"lodash/cloneDeep\",\"root\":[\"_\",\"cloneDeep\"]}", "webpack://cloudinary/external {\"commonjs\":\"lodash/compact\",\"commonjs2\":\"lodash/compact\",\"amd\":\"lodash/compact\",\"root\":[\"_\",\"compact\"]}", "webpack://cloudinary/external {\"commonjs\":\"lodash/difference\",\"commonjs2\":\"lodash/difference\",\"amd\":\"lodash/difference\",\"root\":[\"_\",\"difference\"]}", "webpack://cloudinary/external {\"commonjs\":\"lodash/functions\",\"commonjs2\":\"lodash/functions\",\"amd\":\"lodash/functions\",\"root\":[\"_\",\"functions\"]}", "webpack://cloudinary/external {\"commonjs\":\"lodash/identity\",\"commonjs2\":\"lodash/identity\",\"amd\":\"lodash/identity\",\"root\":[\"_\",\"identity\"]}", "webpack://cloudinary/external {\"commonjs\":\"lodash/includes\",\"commonjs2\":\"lodash/includes\",\"amd\":\"lodash/includes\",\"root\":[\"_\",\"includes\"]}", "webpack://cloudinary/external {\"commonjs\":\"lodash/isArray\",\"commonjs2\":\"lodash/isArray\",\"amd\":\"lodash/isArray\",\"root\":[\"_\",\"isArray\"]}", "webpack://cloudinary/external {\"commonjs\":\"lodash/isElement\",\"commonjs2\":\"lodash/isElement\",\"amd\":\"lodash/isElement\",\"root\":[\"_\",\"isElement\"]}", "webpack://cloudinary/external {\"commonjs\":\"lodash/isFunction\",\"commonjs2\":\"lodash/isFunction\",\"amd\":\"lodash/isFunction\",\"root\":[\"_\",\"isFunction\"]}", "webpack://cloudinary/external {\"commonjs\":\"lodash/isPlainObject\",\"commonjs2\":\"lodash/isPlainObject\",\"amd\":\"lodash/isPlainObject\",\"root\":[\"_\",\"isPlainObject\"]}", "webpack://cloudinary/external {\"commonjs\":\"lodash/isString\",\"commonjs2\":\"lodash/isString\",\"amd\":\"lodash/isString\",\"root\":[\"_\",\"isString\"]}", "webpack://cloudinary/external {\"commonjs\":\"lodash/merge\",\"commonjs2\":\"lodash/merge\",\"amd\":\"lodash/merge\",\"root\":[\"_\",\"merge\"]}", "webpack://cloudinary/external {\"commonjs\":\"lodash/trim\",\"commonjs2\":\"lodash/trim\",\"amd\":\"lodash/trim\",\"root\":[\"_\",\"trim\"]}"], "names": ["utf8_encode", "argString", "c1", "enc", "end", "n", "start", "string", "stringl", "utftext", "length", "charCodeAt", "String", "fromCharCode", "slice", "crc32", "str", "crc", "i", "iTop", "table", "x", "y", "substr", "stringPad", "value", "targetLength", "padString", "repeatStringNumTimes", "times", "repeatedString", "chars", "num", "map", "_toConsumableArray", "for<PERSON>ach", "char", "key", "toString", "reverseVersion", "sem<PERSON><PERSON>", "split", "Error", "reverse", "segment", "join", "encodeVersion", "strResult", "parts", "paddedStringLength", "paddedReversedSemver", "parseInt", "paddedBinary", "match", "bitString", "base64Map", "getSDKAnalyticsSignature", "analyticsOptions", "arguments", "undefined", "twoPartVersion", "removePatchFromSemver", "techVersion", "encodedSDKVersion", "sdkSemver", "encodedTechVersion", "featureCode", "feature", "SDKCode", "sdkCode", "algoVersion", "concat", "e", "semVerStr", "getAnalyticsOptions", "options", "urlAnalytics", "accessibility", "loading", "responsive", "placeholder", "isIntersectionObserverSupported", "window", "_typeof", "IntersectionObserver", "isNativeLazyLoadSupported", "HTMLImageElement", "prototype", "detectIntersection", "el", "onIntersect", "observer", "entries", "entry", "isIntersecting", "unobserve", "target", "threshold", "observe", "VERSION", "CF_SHARED_CDN", "OLD_AKAMAI_SHARED_CDN", "AKAMAI_SHARED_CDN", "SHARED_CDN", "DEFAULT_TIMEOUT_MS", "DEFAULT_POSTER_OPTIONS", "format", "resource_type", "DEFAULT_VIDEO_SOURCE_TYPES", "SEO_TYPES", "DEFAULT_IMAGE_PARAMS", "transformation", "type", "DEFAULT_VIDEO_PARAMS", "fallback_content", "source_transformation", "source_types", "DEFAULT_VIDEO_SOURCES", "codecs", "transformations", "video_codec", "DEFAULT_EXTERNAL_LIBRARIES", "seeThru", "PLACEHOLDER_IMAGE_MODES", "effect", "quality", "fetch_format", "width", "aspect_ratio", "crop", "background", "height", "gravity", "variables", "ACCESSIBILITY_MODES", "darkmode", "brightmode", "monochrome", "colorblind", "URL_KEYS", "omit", "obj", "keys", "srcKeys", "Object", "filter", "contains", "filtered", "allStrings", "list", "every", "isString", "without", "array", "item", "v", "isNumberLike", "isNaN", "parseFloat", "smartEscape", "unsafe", "replace", "c", "toUpperCase", "defaults", "destination", "_len", "sources", "Array", "_key", "reduce", "dest", "source", "objectProto", "objToString", "isObject", "funcTag", "isFunction", "call", "reWords", "lower", "upper", "RegExp", "camelCase", "words", "word", "char<PERSON>t", "toLocaleUpperCase", "toLocaleLowerCase", "snakeCase", "convertKeys", "converter", "result", "isEmpty", "withCamelCase<PERSON>eys", "withSnakeCaseKeys", "base64Encode", "btoa", "<PERSON><PERSON><PERSON>", "input", "from", "base64EncodeURL", "url", "decodeURI", "encodeURI", "extractUrlParams", "patchFetchFormat", "optionConsume", "option_name", "default_value", "size", "hasOwnProperty", "getUserAgent", "navigator", "userAgent", "isAndroid", "test", "isEdge", "isChrome", "<PERSON><PERSON><PERSON><PERSON>", "nodeContains", "getData", "element", "name", "getAttribute", "getAttr", "data", "j<PERSON><PERSON><PERSON>", "fn", "isElement", "setData", "setAttribute", "setAttr", "attr", "removeAttribute", "setAttributes", "attributes", "results", "push", "hasClass", "className", "addClass", "trim", "getStyles", "elem", "ownerDocument", "defaultView", "opener", "getComputedStyle", "cssExpand", "a", "b", "adown", "bup", "nodeType", "documentElement", "parentNode", "domStyle", "style", "curCSS", "computed", "max<PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "ret", "rmargin", "getPropertyValue", "rnumnonpx", "cssValue", "convert", "styles", "val", "augmentWidthOrHeight", "extra", "isBorderBox", "len", "side", "sides", "pnum", "getWidthOrHeight", "valueIsBorderBox", "offsetWidth", "offsetHeight", "Expression", "expressionStr", "_classCallCheck", "expressions", "normalize", "_createClass", "serialize", "getParent", "parent", "setParent", "predicate", "operator", "OPERATORS", "and", "or", "then", "aspectRatio", "pageCount", "faceCount", "_new", "expression", "operators", "operatorsPattern", "operatorsReplaceRE", "predefinedVarsPattern", "PREDEFINED_VARS", "userVariablePattern", "variablesReplaceRE", "variable", "initialWidth", "initialHeight", "initialAspectRatio", "currentPage", "tags", "pageX", "pageY", "BOUNDRY", "Condition", "_Expression", "conditionStr", "_callSuper", "_inherits", "duration", "initialDuration", "Configuration", "configuration", "cloneDeep", "DEFAULT_CONFIGURATION_PARAMS", "init", "fromEnvironment", "fromDocument", "set", "get", "merge", "config", "assign", "meta_elements", "document", "querySelectorAll", "_this", "cloudinary_url", "query", "uri", "uriRegex", "process", "env", "CLOUDINARY_URL", "exec", "_value$split", "_value$split2", "_slicedToArray", "k", "new_config", "new_value", "isPlainObject", "toOptions", "responsive_class", "responsive_use_breakpoints", "round_dpr", "secure", "location", "protocol", "CONFIG_PARAMS", "Layer", "ref", "resourceType", "publicId", "getPublicId", "getFullPublicId", "components", "compact", "clone", "constructor", "TextLayer", "_Layer", "fontFamily", "fontSize", "fontWeight", "fontStyle", "textDecoration", "textAlign", "stroke", "letterSpacing", "lineSpacing", "fontHinting", "fontAntialiasing", "text", "textStyle", "hasPublicId", "hasStyle", "re", "res", "textSource", "textStyleIdentifier", "index", "unshift", "Subtitles<PERSON><PERSON>er", "_TextLayer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Param", "shortName", "identity", "origValue", "valid", "isArray", "norm_color", "build_array", "arg", "process_video_params", "param", "video", "codec", "profile", "level", "b_frames", "ArrayParam", "_Param", "sep", "arrayValue", "flat", "t", "_this2", "_get", "_getPrototypeOf", "TransformationParam", "_Param2", "_this3", "_this4", "joined", "Transformation", "origValue1", "number_pattern", "offset_any_pattern", "RangeParam", "_Param3", "norm_range_value", "offset", "modifier", "RawParam", "_Param4", "LayerParam", "_Param5", "layerOptions", "layer", "ExpressionParam", "_Param6", "assignNotNull", "TransformationBase", "trans", "<PERSON><PERSON><PERSON><PERSON>", "opt", "otherOptions", "chained", "tr", "object", "fromOptions", "abbr", "defaultValue", "rawParam", "last<PERSON>rg<PERSON><PERSON>back", "rangeParam", "arrayParam", "transformationParam", "layerParam", "getValue", "remove", "temp", "VAR_NAME_RE", "sort", "toPlainObject", "hash", "chain", "names", "getOwnPropertyNames", "resetTransformations", "fromTransformation", "other", "camel<PERSON><PERSON>", "_len2", "values", "_key2", "methods", "apply", "<PERSON><PERSON><PERSON><PERSON>", "ifParam", "j", "paramList", "ref1", "ref2", "ref3", "ref4", "resultArray", "transformationList", "transformationString", "vars", "processVar", "difference", "len1", "param_separator", "trans_separator", "toHtmlAttributes", "attrName", "snakeCase<PERSON>ey", "PARAM_NAMES", "toHtml", "listNames", "isValidParamName", "indexOf", "args", "callback", "<PERSON><PERSON><PERSON><PERSON>", "_varArray$j", "processCustomFunction", "_ref", "function_type", "_TransformationBase", "angle", "audioCodec", "audioFrequency", "bitRate", "border", "color", "colorSpace", "customFunction", "customPreFunction", "defaultImage", "delay", "density", "dpr", "_else", "endIf", "endOffset", "fallback<PERSON><PERSON><PERSON>", "fetchFormat", "flags", "fps", "htmlHeight", "htmlWidth", "_if", "ifVal", "trIf", "trRest", "keyframeInterval", "ocr", "end_o", "start_o", "_ref2", "_ref3", "startOffset", "opacity", "overlay", "page", "poster", "prefix", "radius", "rawTransformation", "sourceTypes", "sourceTransformation", "streamingProfile", "underlay", "videoCodec", "videoSampling", "zoom", "HtmlTag", "htmlAttrs", "attrs", "pairs", "escapeQuotes", "toAttribute", "getOptions", "getOption", "htmlAttributes", "removeAttr", "content", "openTag", "tag", "closeTag", "toDOM", "createElement", "isResponsive", "responsiveClass", "dataSrc", "makeUrl", "host", "pathname", "isUrl", "cdnSubdomainNumber", "handleSignature", "signature", "isFormatted", "handlePrefix", "cloud_name", "cdnPart", "subdomain", "path", "private_cdn", "cdn_subdomain", "secure_cdn_subdomain", "secure_distribution", "cname", "handleResourceType", "_ref$resource_type", "_ref$type", "url_suffix", "use_root_path", "shorten", "encodePublicId", "encodeURIComponent", "formatPublicId", "decodeURIComponent", "error", "trust_public_id", "validate", "handleVersion", "isForceVersion", "force_version", "isVersionExist", "version", "handleTransformation", "_objectWithoutProperties", "_excluded", "placeholderTransformations", "blur", "preparePublicId", "urlString", "prepareOptions", "resultUrl", "sdkAnalyticsSignature", "appender", "auth_token", "generateBreakpoints", "srcset", "breakpoints", "_map", "min_width", "max_width", "max_images", "Number", "_map2", "some", "stepSize", "Math", "ceil", "max", "current", "utils", "scaledUrl", "public_id", "config<PERSON><PERSON><PERSON>", "raw_transformation", "getOrGenerateBreakpoints", "generateSrcsetAttribute", "generateSizesAttribute", "generateImageResponsiveAttributes", "srcsetData", "responsiveAttributes", "generateSizes", "sizes", "generateSrcset", "srcsetAttr", "sizesAttr", "generateMediaAttr", "mediaQuery", "srcsetUrl", "ImageTag", "_HtmlTag", "srcAttribute", "srcsetParam", "client_hints", "SourceTag", "media", "PictureTag", "widthList", "VIDEO_TAG_PARAMS", "VideoTag", "setSourceTransformation", "setSourceTypes", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fallback", "innerTags", "src", "createSourceTag", "srcType", "defaultOptions", "hasSourceTags", "sourceType", "mimeType", "videoType", "codecsStr", "ClientHintsMetaTag", "normalizeToArray", "elements", "mountCloudinaryVideoTag", "htmlElContainer", "clInstance", "Promise", "resolve", "reject", "innerHTML", "videoTag", "cloudinaryVideoElement", "querySelector", "addFlagToOptions", "flag", "enforceOptionsForTransparentVideo", "autoplay", "muted", "controls", "max_timeout_ms", "externalLibraries", "loadScript", "scriptURL", "isAlreadyLoaded", "scriptTag", "timerID", "setTimeout", "status", "message", "onerror", "clearTimeout", "onload", "head", "append<PERSON><PERSON><PERSON>", "rejectOnTimeout", "maxTimeoutMS", "getBlobFromURL", "urlToLoad", "fetchBlob", "fetch", "loadUrlUsingFetch", "loadUrlUsingXhr", "blob", "payload", "blobURL", "URL", "createObjectURL", "response", "xhr", "XMLHttpRequest", "responseType", "open", "send", "createHiddenVideoTag", "videoOptions", "playsinline", "loop", "videoURL", "visibility", "position", "revokeObjectURL", "instantiateSeeThru", "videoElement", "customClass", "autoPlay", "_window", "seeThruInstance", "create", "ready", "canvasElement", "get<PERSON>anvas", "play", "mountSeeThruCanvasTag", "err", "checkSupportForTransparency", "canPlay", "canPlayType", "applyBreakpoints", "closestAbove", "defaultBreakpoints", "findC<PERSON><PERSON><PERSON><PERSON><PERSON>", "updateDpr", "steps", "resizing", "calc_breakpoint", "containerWidth", "Element", "display", "roundDpr", "device_pixel_ratio", "requiredWidth", "imageWidth", "Cloudinary", "devicePixelRatioCache", "responsiveConfig", "responsiveResizeInitialized", "newConfig", "newValue", "video_url", "video_thumbnail_url", "constants", "transformation_string", "image", "img", "imageTag", "cloudinary_update", "pictureTag", "sourceTag", "video_thumbnail", "facebook_profile_image", "twitter_profile_image", "twitter_name_profile_image", "gravatar_image", "fetch_image", "sprite_css", "bootstrap", "responsiveResize", "timeout", "responsive_resize", "makeResponsive", "debounce", "reset", "run", "wait", "waitFunc", "responsive_debounce", "addEventListener", "removeEventListener", "point", "calc_stoppoint", "devicePixelRatio", "dprString", "processImageTags", "nodes", "images", "node", "tagName", "imgOptions", "setUrl", "responsive_preserve_height", "isLazyLoading", "isLazyLoadSupported", "setAttributeIfExists", "fromAttribute", "attributeValue", "injectTransparentVideoElement", "isNatively<PERSON><PERSON><PERSON><PERSON>nt", "mountPromise", "<PERSON><PERSON>"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;QCVA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;;;QAGA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA,0CAA0C,gCAAgC;QAC1E;QACA;;QAEA;QACA;QACA;QACA,wDAAwD,kBAAkB;QAC1E;QACA,iDAAiD,cAAc;QAC/D;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,yCAAyC,iCAAiC;QAC1E,gHAAgH,mBAAmB,EAAE;QACrI;QACA;;QAEA;QACA;QACA;QACA,2BAA2B,0BAA0B,EAAE;QACvD,iCAAiC,eAAe;QAChD;QACA;QACA;;QAEA;QACA,sDAAsD,+DAA+D;;QAErH;QACA;;;QAGA;QACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClFA;AACA;AACA;AACA;AACA,IAAIA,WAAW;AAEAA,+DAAW,GAAG,SAAAA,YAASC,SAAS,EAAE;EAC/C,IAAIC,EAAE,EAAEC,GAAG,EAAEC,GAAG,EAAEC,CAAC,EAAEC,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO;EACpD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIR,SAAS,KAAK,IAAI,IAAI,OAAOA,SAAS,KAAK,WAAW,EAAE;IAC1D,OAAO,EAAE;EACX;EACAM,MAAM,GAAGN,SAAS,GAAG,EAAE;EACvB;EACAQ,OAAO,GAAG,EAAE;EACZH,KAAK,GAAG,KAAK,CAAC;EACdF,GAAG,GAAG,KAAK,CAAC;EACZI,OAAO,GAAG,CAAC;EACXF,KAAK,GAAGF,GAAG,GAAG,CAAC;EACfI,OAAO,GAAGD,MAAM,CAACG,MAAM;EACvBL,CAAC,GAAG,CAAC;EACL,OAAOA,CAAC,GAAGG,OAAO,EAAE;IAClBN,EAAE,GAAGK,MAAM,CAACI,UAAU,CAACN,CAAC,CAAC;IACzBF,GAAG,GAAG,IAAI;IACV,IAAID,EAAE,GAAG,GAAG,EAAE;MACZE,GAAG,EAAE;IACP,CAAC,MAAM,IAAIF,EAAE,GAAG,GAAG,IAAIA,EAAE,GAAG,IAAI,EAAE;MAChCC,GAAG,GAAGS,MAAM,CAACC,YAAY,CAACX,EAAE,IAAI,CAAC,GAAG,GAAG,EAAEA,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC;IACzD,CAAC,MAAM;MACLC,GAAG,GAAGS,MAAM,CAACC,YAAY,CAACX,EAAE,IAAI,EAAE,GAAG,GAAG,EAAEA,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,EAAEA,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC;IAC9E;IACA,IAAIC,GAAG,KAAK,IAAI,EAAE;MAChB,IAAIC,GAAG,GAAGE,KAAK,EAAE;QACfG,OAAO,IAAIF,MAAM,CAACO,KAAK,CAACR,KAAK,EAAEF,GAAG,CAAC;MACrC;MACAK,OAAO,IAAIN,GAAG;MACdG,KAAK,GAAGF,GAAG,GAAGC,CAAC,GAAG,CAAC;IACrB;IACAA,CAAC,EAAE;EACL;EACA,IAAID,GAAG,GAAGE,KAAK,EAAE;IACfG,OAAO,IAAIF,MAAM,CAACO,KAAK,CAACR,KAAK,EAAEE,OAAO,CAAC;EACzC;EACA,OAAOC,OAAO;AAChB,CAAC,E;;ACtDuC;;AAExC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,KAAKA,CAACC,GAAG,EAAE;EAClB,IAAIC,GAAG,EAAEC,CAAC,EAAEC,IAAI,EAAEC,KAAK,EAAEC,CAAC,EAAEC,CAAC;EAC7B;EACA;EACA;EACA;EACA;EACA;EACA;EACAN,GAAG,GAAGhB,eAAW,CAACgB,GAAG,CAAC;EACtBI,KAAK,GAAG,iwEAAiwE;EACzwEH,GAAG,GAAG,CAAC;EACPI,CAAC,GAAG,CAAC;EACLC,CAAC,GAAG,CAAC;EACLL,GAAG,GAAGA,GAAG,GAAG,CAAC,CAAC;EACdC,CAAC,GAAG,CAAC;EACLC,IAAI,GAAGH,GAAG,CAACN,MAAM;EACjB,OAAOQ,CAAC,GAAGC,IAAI,EAAE;IACfG,CAAC,GAAG,CAACL,GAAG,GAAGD,GAAG,CAACL,UAAU,CAACO,CAAC,CAAC,IAAI,IAAI;IACpCG,CAAC,GAAG,IAAI,GAAGD,KAAK,CAACG,MAAM,CAACD,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IACjCL,GAAG,GAAGA,GAAG,KAAK,CAAC,GAAGI,CAAC;IACnBH,CAAC,EAAE;EACL;EACAD,GAAG,GAAGA,GAAG,GAAG,CAAC,CAAC;EACd;EACA,IAAIA,GAAG,GAAG,CAAC,EAAE;IACXA,GAAG,IAAI,UAAU;EACnB;EACA,OAAOA,GAAG;AACZ;AAEeF,mDAAK,E;;AC1CL,SAASS,SAASA,CAACC,KAAK,EAAEC,YAAY,EAACC,SAAS,EAAE;EAC/DD,YAAY,GAAGA,YAAY,IAAE,CAAC,CAAC,CAAC;EAChCC,SAAS,GAAGf,MAAM,CAAE,OAAOe,SAAS,KAAK,WAAW,GAAGA,SAAS,GAAG,GAAI,CAAC;EACxE,IAAIF,KAAK,CAACf,MAAM,GAAGgB,YAAY,EAAE;IAC/B,OAAOd,MAAM,CAACa,KAAK,CAAC;EACtB,CAAC,MACI;IACHC,YAAY,GAAGA,YAAY,GAACD,KAAK,CAACf,MAAM;IACxC,IAAIgB,YAAY,GAAGC,SAAS,CAACjB,MAAM,EAAE;MACnCiB,SAAS,IAAIC,oBAAoB,CAACD,SAAS,EAAED,YAAY,GAACC,SAAS,CAACjB,MAAM,CAAC;IAC7E;IACA,OAAOiB,SAAS,CAACb,KAAK,CAAC,CAAC,EAACY,YAAY,CAAC,GAAGd,MAAM,CAACa,KAAK,CAAC;EACxD;AACF;AAEA,SAASG,oBAAoBA,CAACrB,MAAM,EAAEsB,KAAK,EAAE;EAC3C,IAAIC,cAAc,GAAG,EAAE;EACvB,OAAOD,KAAK,GAAG,CAAC,EAAE;IAChBC,cAAc,IAAIvB,MAAM;IACxBsB,KAAK,EAAE;EACT;EACA,OAAOC,cAAc;AACvB,C;;;;;;;;ACtBoC;AACpC,IAAIC,KAAK,GAAG,kEAAkE;AAC9E,IAAIC,aAAG,GAAG,CAAC;AACX,IAAIC,GAAG,GAAG,CAAC,CAAC;AAEZC,kBAAA,CAAIH,KAAK,EAAEI,OAAO,CAAC,UAACC,KAAI,EAAK;EAC3B,IAAIC,GAAG,GAAGL,aAAG,CAACM,QAAQ,CAAC,CAAC,CAAC;EACzBD,GAAG,GAAGb,SAAS,CAACa,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;EAC5BJ,GAAG,CAACI,GAAG,CAAC,GAAGD,KAAI;EACfJ,aAAG,EAAE;AACP,CAAC,CAAC;;AAGF;AACA;AACA;AACeC,iDAAG,E;;AChBkB;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASM,cAAcA,CAACC,MAAM,EAAE;EAC7C,IAAIA,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC/B,MAAM,GAAG,CAAC,EAAE;IAChC,MAAM,IAAIgC,KAAK,CAAC,iDAAiD,CAAC;EACpE;;EAEA;EACA,OAAOF,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAACE,OAAO,CAAC,CAAC,CAACV,GAAG,CAAC,UAACW,OAAO,EAAK;IAClD,OAAOpB,SAAS,CAACoB,OAAO,EAAC,CAAC,EAAE,GAAG,CAAC;EAClC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;AACd,C;;ACnBoC;AACU;AACV;;AAEpC;AACA;AACA;AACA;AACA;AACe,SAASC,aAAaA,CAACN,MAAM,EAAE;EAC5C,IAAIO,SAAS,GAAG,EAAE;;EAElB;EACA,IAAIC,KAAK,GAAGR,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC/B,MAAM;EACpC,IAAIuC,kBAAkB,GAAGD,KAAK,GAAG,CAAC,CAAC,CAAC;;EAEpC;EACA;EACA,IAAIE,oBAAoB,GAAGX,cAAc,CAACC,MAAM,CAAC;;EAEjD;EACA,IAAIR,GAAG,GAAGmB,QAAQ,CAACD,oBAAoB,CAACT,KAAK,CAAC,GAAG,CAAC,CAACI,IAAI,CAAC,EAAE,CAAC,CAAC;;EAE5D;EACA;;EAEA,IAAIO,YAAY,GAAGpB,GAAG,CAACM,QAAQ,CAAC,CAAC,CAAC;EAClCc,YAAY,GAAG5B,SAAS,CAAC4B,YAAY,EAAEH,kBAAkB,EAAE,GAAG,CAAC;;EAE/D;EACA;EACA,IAAIG,YAAY,CAAC1C,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;IACjC,MAAM,wCAAwC;EAChD;;EAEA;EACA0C,YAAY,CAACC,KAAK,CAAC,SAAS,CAAC,CAAClB,OAAO,CAAC,UAACmB,SAAS,EAAK;IACnD;IACAP,SAAS,IAAIQ,SAAS,CAACD,SAAS,CAAC;EACnC,CAAC,CAAC;EAEF,OAAOP,SAAS;AAClB,C;;AC1C+C;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASS,wBAAwBA,CAAA,EAAsB;EAAA,IAArBC,gBAAgB,GAAAC,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAC,CAAC,CAAC;EAClE,IAAI;IACF,IAAIE,cAAc,GAAGC,qBAAqB,CAACJ,gBAAgB,CAACK,WAAW,CAAC;IACxE,IAAIC,iBAAiB,GAAGjB,aAAa,CAACW,gBAAgB,CAACO,SAAS,CAAC;IACjE,IAAIC,kBAAkB,GAAGnB,aAAa,CAACc,cAAc,CAAC;IACtD,IAAIM,WAAW,GAAGT,gBAAgB,CAACU,OAAO;IAC1C,IAAIC,OAAO,GAAGX,gBAAgB,CAACY,OAAO;IACtC,IAAIC,WAAW,GAAG,GAAG,CAAC,CAAC;;IAEvB,UAAAC,MAAA,CAAUD,WAAW,EAAAC,MAAA,CAAGH,OAAO,EAAAG,MAAA,CAAGR,iBAAiB,EAAAQ,MAAA,CAAGN,kBAAkB,EAAAM,MAAA,CAAGL,WAAW;EACxF,CAAC,CAAC,OAAOM,CAAC,EAAE;IACV;IACA,OAAO,GAAG;EACZ;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASX,qBAAqBA,CAACY,SAAS,EAAE;EACxC,IAAIzB,KAAK,GAAGyB,SAAS,CAAChC,KAAK,CAAC,GAAG,CAAC;EAEhC,UAAA8B,MAAA,CAAUvB,KAAK,CAAC,CAAC,CAAC,OAAAuB,MAAA,CAAIvB,KAAK,CAAC,CAAC,CAAC;AAChC,C;;ACrCA;AACA;AACA;AACA;AACA;AACe,SAAS0B,mBAAmBA,CAACC,OAAO,EAAE;EACnD,IAAIlB,gBAAgB,GAAG;IACrBO,SAAS,EAAEW,OAAO,CAACX,SAAS;IAC5BF,WAAW,EAAEa,OAAO,CAACb,WAAW;IAChCO,OAAO,EAAEM,OAAO,CAACN,OAAO;IACxBF,OAAO,EAAE;EACX,CAAC;EACD,IAAIQ,OAAO,CAACC,YAAY,EAAE;IACxB,IAAID,OAAO,CAACE,aAAa,EAAE;MACzBpB,gBAAgB,CAACU,OAAO,GAAG,GAAG;IAChC;IACA,IAAIQ,OAAO,CAACG,OAAO,KAAK,MAAM,EAAE;MAC9BrB,gBAAgB,CAACU,OAAO,GAAG,GAAG;IAChC;IACA,IAAIQ,OAAO,CAACI,UAAU,EAAE;MACtBtB,gBAAgB,CAACU,OAAO,GAAG,GAAG;IAChC;IACA,IAAIQ,OAAO,CAACK,WAAW,EAAE;MACvBvB,gBAAgB,CAACU,OAAO,GAAG,GAAG;IAChC;IACA,OAAOV,gBAAgB;EACzB,CAAC,MAAM;IACL,OAAO,CAAC,CAAC;EACX;AACF,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7BA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACO,SAASwB,+BAA+BA,CAAA,EAAG;EAChD;EACA,OAAO,QAAOC,MAAM,iCAAAC,OAAA,CAAND,MAAM,OAAK,QAAQ,IAAIA,MAAM,CAACE,oBAAoB;AAClE;;AAEA;AACA;AACA;AACA;AACO,SAASC,yBAAyBA,CAAA,EAAG;EAC1C,OAAO,QAAOC,gBAAgB,iCAAAH,OAAA,CAAhBG,gBAAgB,OAAK,QAAQ,IAAIA,gBAAgB,CAACC,SAAS,CAACT,OAAO;AACnF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASU,kBAAkBA,CAACC,EAAE,EAAEC,WAAW,EAAE;EAClD,IAAI;IACF,IAAIL,yBAAyB,CAAC,CAAC,IAAI,CAACJ,+BAA+B,CAAC,CAAC,EAAE;MACrE;MACAS,WAAW,CAAC,CAAC;MACb;IACF;;IAEA;IACA,IAAMC,QAAQ,GAAG,IAAIP,oBAAoB,CACvC,UAACQ,OAAO,EAAK;MACXA,OAAO,CAACzD,OAAO,CAAC,UAAA0D,KAAK,EAAI;QACvB,IAAIA,KAAK,CAACC,cAAc,EAAE;UACxBJ,WAAW,CAAC,CAAC;UACbC,QAAQ,CAACI,SAAS,CAACF,KAAK,CAACG,MAAM,CAAC;QAClC;MACF,CAAC,CAAC;IACJ,CAAC,EAAE;MAACC,SAAS,EAAE,CAAC,CAAC,EAAE,IAAI;IAAC,CAAC,CAAC;IAC5BN,QAAQ,CAACO,OAAO,CAACT,EAAE,CAAC;EACtB,CAAC,CAAC,OAAOjB,CAAC,EAAE;IACVkB,WAAW,CAAC,CAAC;EACf;AACF,C;;ACjDO,IAAIS,OAAO,GAAG,OAAO;AAErB,IAAIC,aAAa,GAAG,+BAA+B;AAEnD,IAAIC,qBAAqB,GAAG,2BAA2B;AAEvD,IAAIC,iBAAiB,GAAG,oBAAoB;AAE5C,IAAIC,UAAU,GAAGD,iBAAiB;AAElC,IAAIE,kBAAkB,GAAG,KAAK;AAE9B,IAAIC,sBAAsB,GAAG;EAClCC,MAAM,EAAE,KAAK;EACbC,aAAa,EAAE;AACjB,CAAC;AAEM,IAAIC,0BAA0B,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC;AAEvD,IAAIC,SAAS,GAAG;EACrB,cAAc,EAAE,QAAQ;EACxB,eAAe,EAAE,gBAAgB;EACjC,qBAAqB,EAAE,sBAAsB;EAC7C,YAAY,EAAE,OAAO;EACrB,cAAc,EAAE;AAClB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACO,IAAIC,oBAAoB,GAAG;EAChCH,aAAa,EAAE,OAAO;EACtBI,cAAc,EAAE,EAAE;EAClBC,IAAI,EAAE;AACR,CAAC;;AAED;AACA;AACA;AACA;AACA;AACO,IAAIC,oBAAoB,GAAG;EAChCC,gBAAgB,EAAE,EAAE;EACpBP,aAAa,EAAE,OAAO;EACtBQ,qBAAqB,EAAE,CAAC,CAAC;EACzBC,YAAY,EAAER,0BAA0B;EACxCG,cAAc,EAAE,EAAE;EAClBC,IAAI,EAAE;AACR,CAAC;;AAED;AACA;AACA;AACA;AACO,IAAMK,qBAAqB,GAAG,CACnC;EACEL,IAAI,EAAE,KAAK;EACXM,MAAM,EAAE,MAAM;EACdC,eAAe,EAAE;IAACC,WAAW,EAAE;EAAM;AACvC,CAAC,EACD;EACER,IAAI,EAAE,MAAM;EACZM,MAAM,EAAE,KAAK;EACbC,eAAe,EAAE;IAACC,WAAW,EAAE;EAAK;AACtC,CAAC,EACD;EACER,IAAI,EAAE,KAAK;EACXO,eAAe,EAAE;IAACC,WAAW,EAAE;EAAM;AACvC,CAAC,EACD;EACER,IAAI,EAAE,MAAM;EACZO,eAAe,EAAE;IAACC,WAAW,EAAE;EAAM;AACvC,CAAC,CACF;AAEM,IAAMC,0BAA0B,GAAG;EACxCC,OAAO,EAAE;AACX,CAAC;;AAED;AACA;AACA;AACA;AACO,IAAMC,uBAAuB,GAAG;EACrC,MAAM,EAAE,CAAC;IAACC,MAAM,EAAE,WAAW;IAAEC,OAAO,EAAE,CAAC;IAAEC,YAAY,EAAE;EAAM,CAAC,CAAC;EAAE;EACnE,UAAU,EAAE,CAAC;IAACF,MAAM,EAAE,UAAU;IAAEC,OAAO,EAAE,CAAC;IAAEC,YAAY,EAAE;EAAM,CAAC,CAAC;EACpE;EACA,yBAAyB,EAAE,CACzB;IAACC,KAAK,EAAE,UAAU;IAAEC,YAAY,EAAE,CAAC;IAAEC,IAAI,EAAE,KAAK;IAAEC,UAAU,EAAE;EAAM,CAAC,EACrE;IAACD,IAAI,EAAE,MAAM;IAAEF,KAAK,EAAE,CAAC;IAAEI,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE;EAAY,CAAC,EAC1D;IAACN,YAAY,EAAE,MAAM;IAAED,OAAO,EAAE;EAAM,CAAC,CACxC;EACD;EACA,mBAAmB,EAAE,CACnB;IAACQ,SAAS,EAAE,CAAC,CAAC,YAAY,EAAE,GAAG,CAAC,EAAE,CAAC,aAAa,EAAE,GAAG,CAAC;EAAC,CAAC,EACxD;IAACN,KAAK,EAAE,UAAU;IAAEC,YAAY,EAAE,CAAC;IAAEC,IAAI,EAAE,KAAK;IAAEC,UAAU,EAAE;EAAM,CAAC,EACrE;IAACD,IAAI,EAAE,MAAM;IAAEF,KAAK,EAAE,EAAE;IAAEI,MAAM,EAAE,EAAE;IAAEC,OAAO,EAAE;EAAY,CAAC,EAC5D;IAACL,KAAK,EAAE,YAAY;IAAEI,MAAM,EAAE,aAAa;IAAEF,IAAI,EAAE;EAAM,CAAC,EAC1D;IAACH,YAAY,EAAE,MAAM;IAAED,OAAO,EAAE;EAAM,CAAC,CACxC;EACD,WAAW,EAAE,CAAC;IAACD,MAAM,EAAE,iBAAiB;IAAEE,YAAY,EAAE;EAAK,CAAC;AAChE,CAAC;;AAED;AACA;AACA;AACA;AACO,IAAMQ,mBAAmB,GAAG;EACjCC,QAAQ,EAAE,eAAe;EACzBC,UAAU,EAAE,eAAe;EAC3BC,UAAU,EAAE,WAAW;EACvBC,UAAU,EAAE;AACd,CAAC;;AAED;AACA;AACA;AACA;AACO,IAAMC,QAAQ,GAAG,CACtB,eAAe,EACf,YAAY,EACZ,YAAY,EACZ,eAAe,EACf,YAAY,EACZ,OAAO,EACP,QAAQ,EACR,aAAa,EACb,aAAa,EACb,eAAe,EACf,QAAQ,EACR,sBAAsB,EACtB,qBAAqB,EACrB,SAAS,EACT,UAAU,EACV,WAAW,EACX,cAAc,EACd,MAAM,EACN,YAAY,EACZ,eAAe,EACf,SAAS,CACV;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,G;;;ACzKA;AACA;AACA;AAC2C;AACL;AAE/B,SAASC,IAAIA,CAACC,GAAG,EAAEC,IAAI,EAAE;EAC9BD,GAAG,GAAGA,GAAG,IAAI,CAAC,CAAC;EACf,IAAIE,OAAO,GAAGC,MAAM,CAACF,IAAI,CAACD,GAAG,CAAC,CAACI,MAAM,CAAC,UAAA5G,GAAG;IAAA,OAAI,CAAC6G,gCAAQ,CAACJ,IAAI,EAAEzG,GAAG,CAAC;EAAA,EAAC;EAClE,IAAI8G,QAAQ,GAAG,CAAC,CAAC;EACjBJ,OAAO,CAAC5G,OAAO,CAAC,UAAAE,GAAG;IAAA,OAAI8G,QAAQ,CAAC9G,GAAG,CAAC,GAAGwG,GAAG,CAACxG,GAAG,CAAC;EAAA,EAAC;EAChD,OAAO8G,QAAQ;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACO,IAAIC,mBAAU,GAAG,SAAbA,UAAUA,CAAYC,IAAI,EAAE;EACrC,OAAOA,IAAI,CAAC3I,MAAM,IAAI2I,IAAI,CAACC,KAAK,CAACC,gCAAQ,CAAC;AAC5C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACO,IAAIC,OAAO,GAAG,SAAVA,OAAOA,CAAYC,KAAK,EAAEC,IAAI,EAAE;EACzC,OAAOD,KAAK,CAACR,MAAM,CAAC,UAAAU,CAAC;IAAA,OAAEA,CAAC,KAAGD,IAAI;EAAA,EAAC;AAClC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,IAAIE,YAAY,GAAG,SAAfA,YAAYA,CAAYnI,KAAK,EAAE;EACxC,OAAQA,KAAK,IAAI,IAAI,IAAK,CAACoI,KAAK,CAACC,UAAU,CAACrI,KAAK,CAAC,CAAC;AACrD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACO,IAAIsI,WAAW,GAAG,SAAdA,WAAWA,CAAYxJ,MAAM,EAAsC;EAAA,IAApCyJ,MAAM,GAAAtG,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,yBAAyB;EAC1E,OAAOnD,MAAM,CAAC0J,OAAO,CAACD,MAAM,EAAE,UAAS3G,KAAK,EAAE;IAC5C,OAAOA,KAAK,CAACZ,KAAK,CAAC,EAAE,CAAC,CAACR,GAAG,CAAC,UAASiI,CAAC,EAAE;MACrC,OAAO,GAAG,GAAGA,CAAC,CAACvJ,UAAU,CAAC,CAAC,CAAC,CAAC2B,QAAQ,CAAC,EAAE,CAAC,CAAC6H,WAAW,CAAC,CAAC;IACzD,CAAC,CAAC,CAACtH,IAAI,CAAC,EAAE,CAAC;EACb,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,IAAIuH,QAAQ,GAAG,SAAXA,QAAQA,CAAYC,WAAW,EAAc;EAAA,SAAAC,IAAA,GAAA5G,SAAA,CAAAhD,MAAA,EAAT6J,OAAO,OAAAC,KAAA,CAAAF,IAAA,OAAAA,IAAA,WAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;IAAPF,OAAO,CAAAE,IAAA,QAAA/G,SAAA,CAAA+G,IAAA;EAAA;EACpD,OAAOF,OAAO,CAACG,MAAM,CAAC,UAASC,IAAI,EAAEC,MAAM,EAAE;IAC3C,IAAIvI,GAAG,EAAEZ,KAAK;IACd,KAAKY,GAAG,IAAIuI,MAAM,EAAE;MAClBnJ,KAAK,GAAGmJ,MAAM,CAACvI,GAAG,CAAC;MACnB,IAAIsI,IAAI,CAACtI,GAAG,CAAC,KAAK,KAAK,CAAC,EAAE;QACxBsI,IAAI,CAACtI,GAAG,CAAC,GAAGZ,KAAK;MACnB;IACF;IACA,OAAOkJ,IAAI;EACb,CAAC,EAAEN,WAAW,CAAC;AACjB,CAAC;;AAED;AACO,IAAIQ,WAAW,GAAG7B,MAAM,CAACzD,SAAS;;AAEzC;AACA;AACA;AACA;AACO,IAAIuF,WAAW,GAAGD,WAAW,CAACvI,QAAQ;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,IAAIyI,QAAQ,GAAG,SAAXA,QAAQA,CAAYtJ,KAAK,EAAE;EACpC,IAAIuF,IAAI;EACR;EACA;EACAA,IAAI,GAAA7B,eAAA,CAAU1D,KAAK;EACnB,OAAO,CAAC,CAACA,KAAK,KAAKuF,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,UAAU,CAAC;AAC9D,CAAC;AAEM,IAAIgE,OAAO,GAAG,mBAAmB;;AAExC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,IAAIC,UAAU,GAAG,SAAbA,UAAUA,CAAYxJ,KAAK,EAAE;EACtC;EACA;EACA;EACA,OAAOsJ,QAAQ,CAACtJ,KAAK,CAAC,IAAIqJ,WAAW,CAACI,IAAI,CAACzJ,KAAK,CAAC,KAAKuJ,OAAO;AAC/D,CAAC;;AAED;AACA;AACO,IAAIG,OAAO,GAAI,YAAW;EAC/B,IAAIC,KAAK,EAAEC,KAAK;EAChBA,KAAK,GAAG,OAAO;EACfD,KAAK,GAAG,QAAQ;EAChB,OAAOE,MAAM,CAACD,KAAK,GAAG,MAAM,GAAGA,KAAK,GAAGD,KAAK,GAAG,IAAI,GAAGC,KAAK,GAAG,GAAG,GAAGD,KAAK,GAAG,GAAG,GAAGC,KAAK,GAAG,UAAU,EAAE,GAAG,CAAC;AAC5G,CAAC,CAAE,CAAC;;AAEJ;AACA;AACA;AACA;AACA;AACA;AACO,IAAIE,SAAS,GAAG,SAAZA,SAASA,CAAYX,MAAM,EAAE;EACtC,IAAIY,KAAK,GAAGZ,MAAM,CAACvH,KAAK,CAAC8H,OAAO,CAAC;EACjCK,KAAK,GAAGA,KAAK,CAACvJ,GAAG,CAAC,UAAAwJ,IAAI;IAAA,OAAGA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC,GAAGF,IAAI,CAAC3K,KAAK,CAAC,CAAC,CAAC,CAAC8K,iBAAiB,CAAC,CAAC;EAAA,EAAC;EAChGJ,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAACI,iBAAiB,CAAC,CAAC;EAEvC,OAAOJ,KAAK,CAAC3I,IAAI,CAAC,EAAE,CAAC;AACvB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACO,IAAIgJ,SAAS,GAAG,SAAZA,SAASA,CAAYjB,MAAM,EAAE;EACtC,IAAIY,KAAK,GAAGZ,MAAM,CAACvH,KAAK,CAAC8H,OAAO,CAAC;EACjCK,KAAK,GAAGA,KAAK,CAACvJ,GAAG,CAAC,UAAAwJ,IAAI;IAAA,OAAGA,IAAI,CAACG,iBAAiB,CAAC,CAAC;EAAA,EAAC;EAClD,OAAOJ,KAAK,CAAC3I,IAAI,CAAC,GAAG,CAAC;AACxB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACO,IAAIiJ,WAAW,GAAG,SAAdA,WAAWA,CAAYlB,MAAM,EAAEmB,SAAS,EAAE;EACnD,IAAIC,MAAM,EAAEvK,KAAK;EACjBuK,MAAM,GAAG,CAAC,CAAC;EACX,KAAK,IAAI3J,GAAG,IAAIuI,MAAM,EAAE;IACtBnJ,KAAK,GAAGmJ,MAAM,CAACvI,GAAG,CAAC;IACnB,IAAG0J,SAAS,EAAE;MACZ1J,GAAG,GAAG0J,SAAS,CAAC1J,GAAG,CAAC;IACtB;IACA,IAAI,CAAC4J,OAAO,CAAC5J,GAAG,CAAC,EAAE;MACjB2J,MAAM,CAAC3J,GAAG,CAAC,GAAGZ,KAAK;IACrB;EACF;EACA,OAAOuK,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACO,IAAIE,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAYtB,MAAM,EAAE;EAC9C,OAAOkB,WAAW,CAAClB,MAAM,EAAEW,SAAS,CAAC;AACvC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACO,IAAIY,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAYvB,MAAM,EAAE;EAC9C,OAAOkB,WAAW,CAAClB,MAAM,EAAEiB,SAAS,CAAC;AACvC,CAAC;;AAED;AACA;AACO,IAAIO,YAAY,GAAG,OAAOC,IAAI,KAAK,WAAW,IAAIpB,UAAU,CAACoB,IAAI,CAAC,GAAGA,IAAI,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAIrB,UAAU,CAACqB,MAAM,CAAC,GAAG,UAASC,KAAK,EAAE;EACvJ,IAAI,EAAEA,KAAK,YAAYD,MAAM,CAAC,EAAE;IAC9BC,KAAK,GAAG,IAAID,MAAM,CAACE,IAAI,CAAC5L,MAAM,CAAC2L,KAAK,CAAC,EAAE,QAAQ,CAAC;EAClD;EACA,OAAOA,KAAK,CAACjK,QAAQ,CAAC,QAAQ,CAAC;AACjC,CAAC,GAAG,UAASiK,KAAK,EAAE;EAClB,MAAM,IAAI7J,KAAK,CAAC,mCAAmC,CAAC;AACtD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACO,IAAI+J,eAAe,GAAG,SAAlBA,eAAeA,CAAYC,GAAG,EAAE;EACzC,IAAI;IACFA,GAAG,GAAGC,SAAS,CAACD,GAAG,CAAC;EACtB,CAAC,SAAS;IACRA,GAAG,GAAGE,SAAS,CAACF,GAAG,CAAC;EACtB;EACA,OAAON,YAAY,CAACM,GAAG,CAAC;AAC1B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACO,SAASG,gBAAgBA,CAAClI,OAAO,EAAE;EACxC,OAAOgE,QAAQ,CAAC+B,MAAM,CAAC,UAAC7B,GAAG,EAAExG,GAAG,EAAK;IACnC,IAAIsC,OAAO,CAACtC,GAAG,CAAC,IAAI,IAAI,EAAE;MACxBwG,GAAG,CAACxG,GAAG,CAAC,GAAGsC,OAAO,CAACtC,GAAG,CAAC;IACzB;IACA,OAAOwG,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;;AAGA;AACA;AACA;AACA;AACA;AACO,SAASiE,gBAAgBA,CAACnI,OAAO,EAAE;EACxC,IAAGA,OAAO,IAAI,IAAI,EAAE;IAClBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,IAAIA,OAAO,CAACqC,IAAI,KAAK,OAAO,EAAE;IAC5B,IAAIrC,OAAO,CAACmD,YAAY,IAAI,IAAI,EAAE;MAChCnD,OAAO,CAACmD,YAAY,GAAGiF,aAAa,CAACpI,OAAO,EAAE,QAAQ,CAAC;IACzD;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASoI,aAAaA,CAACpI,OAAO,EAAEqI,WAAW,EAAEC,aAAa,EAAE;EACjE,IAAIjB,MAAM,GAAGrH,OAAO,CAACqI,WAAW,CAAC;EACjC,OAAOrI,OAAO,CAACqI,WAAW,CAAC;EAC3B,IAAIhB,MAAM,IAAI,IAAI,EAAE;IAClB,OAAOA,MAAM;EACf,CAAC,MAAM;IACL,OAAOiB,aAAa;EACtB;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAShB,OAAOA,CAACxK,KAAK,EAAE;EAC7B,IAAGA,KAAK,IAAI,IAAI,EAAE;IAChB,OAAO,IAAI;EACb;EACA,IAAI,OAAOA,KAAK,CAACf,MAAM,IAAI,QAAQ,EAAE;IACnC,OAAOe,KAAK,CAACf,MAAM,KAAK,CAAC;EAC3B;EACA,IAAI,OAAOe,KAAK,CAACyL,IAAI,IAAI,QAAQ,EAAE;IACjC,OAAOzL,KAAK,CAACyL,IAAI,KAAK,CAAC;EACzB;EACA,IAAG/H,eAAA,CAAO1D,KAAK,KAAI,QAAQ,EAAE;IAC3B,KAAI,IAAIY,GAAG,IAAIZ,KAAK,EAAE;MACpB,IAAGA,KAAK,CAAC0L,cAAc,CAAC9K,GAAG,CAAC,EAAE;QAC5B,OAAO,KAAK;MACd;IACF;IACA,OAAO,IAAI;EACb;EACA,OAAO,IAAI;AACb,C;;ACnUA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAAS+K,YAAYA,CAAA,EAAE;EACrB,OAAOC,SAAS,IAAIA,SAAS,CAACC,SAAS,IAAI,EAAE;AAC/C;;AAEA;AACA;AACA;AACA;AACO,SAASC,SAASA,CAAA,EAAE;EACzB,IAAMD,SAAS,GAAGF,YAAY,CAAC,CAAC;EAChC,OAAQ,UAAU,CAAEI,IAAI,CAACF,SAAS,CAAC;AACrC;;AAEA;AACA;AACA;AACA;AACO,SAASG,MAAMA,CAAA,EAAE;EACtB,IAAMH,SAAS,GAAGF,YAAY,CAAC,CAAC;EAChC,OAAQ,MAAM,CAAEI,IAAI,CAACF,SAAS,CAAC;AACjC;;AAEA;AACA;AACA;AACA;AACO,SAASI,QAAQA,CAAA,EAAE;EACxB,IAAMJ,SAAS,GAAGF,YAAY,CAAC,CAAC;EAChC,OAAO,CAACK,MAAM,CAAC,CAAC,KAAM,SAAS,CAAED,IAAI,CAACF,SAAS,CAAC,IAAK,QAAQ,CAAEE,IAAI,CAACF,SAAS,CAAC,CAAC;AACjF;;AAEA;AACA;AACA;AACA;AACO,SAASK,QAAQA,CAAA,EAAE;EACxB;EACA;EACA;EACA,IAAML,SAAS,GAAGF,YAAY,CAAC,CAAC;EAChC,OAAQ,SAAS,CAAEI,IAAI,CAACF,SAAS,CAAC,IAAI,CAACI,QAAQ,CAAC,CAAC,IAAI,CAACH,SAAS,CAAC,CAAC,IAAI,CAACE,MAAM,CAAC,CAAC;AAChF,C;;AClDA,IAAIG,YAAY;AAEgE;AACV;AAChB;AAI/B;AAIG;AAIF;AAIG;AAID;AAID;AAIA;AAID;AAIM;AAIL;AAIH;AAIG;AAEgB;AACE;AACZ;AAEJ;AACA;AACD;AAIpB;;AAGN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,IAAIC,cAAO,GAAG,SAAVA,OAAOA,CAAaC,OAAO,EAAEC,IAAI,EAAE;EAC5C,QAAQ,KAAK;IACX,KAAK,EAAED,OAAO,IAAI,IAAI,CAAC;MACrB,OAAO,KAAK,CAAC;IACf,KAAK,CAAC7C,oCAAU,CAAC6C,OAAO,CAACE,YAAY,CAAC;MACpC,OAAOF,OAAO,CAACE,YAAY,SAAAzJ,MAAA,CAASwJ,IAAI,CAAE,CAAC;IAC7C,KAAK,CAAC9C,oCAAU,CAAC6C,OAAO,CAACG,OAAO,CAAC;MAC/B,OAAOH,OAAO,CAACG,OAAO,SAAA1J,MAAA,CAASwJ,IAAI,CAAE,CAAC;IACxC,KAAK,CAAC9C,oCAAU,CAAC6C,OAAO,CAACI,IAAI,CAAC;MAC5B,OAAOJ,OAAO,CAACI,IAAI,CAACH,IAAI,CAAC;IAC3B,KAAK,EAAE9C,oCAAU,CAAC,OAAOkD,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,EAAE,IAAID,MAAM,CAACC,EAAE,CAACF,IAAI,CAAC,IAAIG,kCAAS,CAACP,OAAO,CAAC,CAAC;MACpG,OAAOK,MAAM,CAACL,OAAO,CAAC,CAACI,IAAI,CAACH,IAAI,CAAC;EACrC;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,IAAIO,cAAO,GAAG,SAAVA,OAAOA,CAAaR,OAAO,EAAEC,IAAI,EAAEtM,KAAK,EAAE;EACnD,QAAQ,KAAK;IACX,KAAK,EAAEqM,OAAO,IAAI,IAAI,CAAC;MACrB,OAAO,KAAK,CAAC;IACf,KAAK,CAAC7C,oCAAU,CAAC6C,OAAO,CAACS,YAAY,CAAC;MACpC,OAAOT,OAAO,CAACS,YAAY,SAAAhK,MAAA,CAASwJ,IAAI,GAAItM,KAAK,CAAC;IACpD,KAAK,CAACwJ,oCAAU,CAAC6C,OAAO,CAACU,OAAO,CAAC;MAC/B,OAAOV,OAAO,CAACU,OAAO,SAAAjK,MAAA,CAASwJ,IAAI,GAAItM,KAAK,CAAC;IAC/C,KAAK,CAACwJ,oCAAU,CAAC6C,OAAO,CAACI,IAAI,CAAC;MAC5B,OAAOJ,OAAO,CAACI,IAAI,CAACH,IAAI,EAAEtM,KAAK,CAAC;IAClC,KAAK,EAAEwJ,oCAAU,CAAC,OAAOkD,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,EAAE,IAAID,MAAM,CAACC,EAAE,CAACF,IAAI,CAAC,IAAIG,kCAAS,CAACP,OAAO,CAAC,CAAC;MACpG,OAAOK,MAAM,CAACL,OAAO,CAAC,CAACI,IAAI,CAACH,IAAI,EAAEtM,KAAK,CAAC;EAC5C;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,IAAIuM,mBAAY,GAAG,SAAfA,YAAYA,CAAaF,OAAO,EAAEC,IAAI,EAAE;EACjD,QAAQ,KAAK;IACX,KAAK,EAAED,OAAO,IAAI,IAAI,CAAC;MACrB,OAAO,KAAK,CAAC;IACf,KAAK,CAAC7C,oCAAU,CAAC6C,OAAO,CAACE,YAAY,CAAC;MACpC,OAAOF,OAAO,CAACE,YAAY,CAACD,IAAI,CAAC;IACnC,KAAK,CAAC9C,oCAAU,CAAC6C,OAAO,CAACW,IAAI,CAAC;MAC5B,OAAOX,OAAO,CAACW,IAAI,CAACV,IAAI,CAAC;IAC3B,KAAK,CAAC9C,oCAAU,CAAC6C,OAAO,CAACG,OAAO,CAAC;MAC/B,OAAOH,OAAO,CAACG,OAAO,CAACF,IAAI,CAAC;EAChC;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,IAAIQ,mBAAY,GAAG,SAAfA,YAAYA,CAAaT,OAAO,EAAEC,IAAI,EAAEtM,KAAK,EAAE;EACxD,QAAQ,KAAK;IACX,KAAK,EAAEqM,OAAO,IAAI,IAAI,CAAC;MACrB,OAAO,KAAK,CAAC;IACf,KAAK,CAAC7C,oCAAU,CAAC6C,OAAO,CAACS,YAAY,CAAC;MACpC,OAAOT,OAAO,CAACS,YAAY,CAACR,IAAI,EAAEtM,KAAK,CAAC;IAC1C,KAAK,CAACwJ,oCAAU,CAAC6C,OAAO,CAACW,IAAI,CAAC;MAC5B,OAAOX,OAAO,CAACW,IAAI,CAACV,IAAI,EAAEtM,KAAK,CAAC;IAClC,KAAK,CAACwJ,oCAAU,CAAC6C,OAAO,CAACU,OAAO,CAAC;MAC/B,OAAOV,OAAO,CAACU,OAAO,CAACT,IAAI,EAAEtM,KAAK,CAAC;EACvC;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACO,IAAIiN,sBAAe,GAAG,SAAlBA,eAAeA,CAAaZ,OAAO,EAAEC,IAAI,EAAE;EACpD,QAAQ,KAAK;IACX,KAAK,EAAED,OAAO,IAAI,IAAI,CAAC;MACrB,OAAO,KAAK,CAAC;IACf,KAAK,CAAC7C,oCAAU,CAAC6C,OAAO,CAACY,eAAe,CAAC;MACvC,OAAOZ,OAAO,CAACY,eAAe,CAACX,IAAI,CAAC;IACtC;MACE,OAAOQ,mBAAY,CAACT,OAAO,EAAE,KAAK,CAAC,CAAC;EACxC;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACO,IAAIa,aAAa,GAAG,SAAhBA,aAAaA,CAAab,OAAO,EAAEc,UAAU,EAAE;EACxD,IAAIb,IAAI,EAAEc,OAAO,EAAEpN,KAAK;EACxBoN,OAAO,GAAG,EAAE;EACZ,KAAKd,IAAI,IAAIa,UAAU,EAAE;IACvBnN,KAAK,GAAGmN,UAAU,CAACb,IAAI,CAAC;IACxB,IAAItM,KAAK,IAAI,IAAI,EAAE;MACjBoN,OAAO,CAACC,IAAI,CAACP,mBAAY,CAACT,OAAO,EAAEC,IAAI,EAAEtM,KAAK,CAAC,CAAC;IAClD,CAAC,MAAM;MACLoN,OAAO,CAACC,IAAI,CAACJ,sBAAe,CAACZ,OAAO,EAAEC,IAAI,CAAC,CAAC;IAC9C;EACF;EACA,OAAOc,OAAO;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACO,IAAIE,eAAQ,GAAG,SAAXA,QAAQA,CAAajB,OAAO,EAAEC,IAAI,EAAE;EAC7C,IAAIM,kCAAS,CAACP,OAAO,CAAC,EAAE;IACtB,OAAOA,OAAO,CAACkB,SAAS,CAAC3L,KAAK,CAAC,IAAIiI,MAAM,OAAA/G,MAAA,CAAOwJ,IAAI,QAAK,CAAC,CAAC;EAC7D;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACO,IAAIkB,eAAQ,GAAG,SAAXA,QAAQA,CAAanB,OAAO,EAAEC,IAAI,EAAE;EAC7C,IAAI,CAACD,OAAO,CAACkB,SAAS,CAAC3L,KAAK,CAAC,IAAIiI,MAAM,OAAA/G,MAAA,CAAOwJ,IAAI,QAAK,CAAC,CAAC,EAAE;IACzD,OAAOD,OAAO,CAACkB,SAAS,GAAGE,wBAAI,IAAA3K,MAAA,CAAIuJ,OAAO,CAACkB,SAAS,OAAAzK,MAAA,CAAIwJ,IAAI,CAAE,CAAC;EACjE;AACF,CAAC;;AAED;AACO,IAAIoB,SAAS,GAAG,SAAZA,SAASA,CAAaC,IAAI,EAAE;EACrC;EACA;EACA;EACA,IAAIA,IAAI,CAACC,aAAa,CAACC,WAAW,CAACC,MAAM,EAAE;IACzC,OAAOH,IAAI,CAACC,aAAa,CAACC,WAAW,CAACE,gBAAgB,CAACJ,IAAI,EAAE,IAAI,CAAC;EACpE;EACA,OAAOlK,MAAM,CAACsK,gBAAgB,CAACJ,IAAI,EAAE,IAAI,CAAC;AAC5C,CAAC;AAEM,IAAIK,SAAS,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC;AAEzD7B,YAAY,GAAG,SAAAA,aAAU8B,CAAC,EAAEC,CAAC,EAAE;EAC7B,IAAIC,KAAK,EAAEC,GAAG;EACdD,KAAK,GAAIF,CAAC,CAACI,QAAQ,KAAK,CAAC,GAAGJ,CAAC,CAACK,eAAe,GAAGL,CAAE;EAClDG,GAAG,GAAGF,CAAC,IAAIA,CAAC,CAACK,UAAU;EACvB,OAAON,CAAC,KAAKG,GAAG,IAAI,CAAC,EAAEA,GAAG,IAAIA,GAAG,CAACC,QAAQ,KAAK,CAAC,IAAIF,KAAK,CAAC1G,QAAQ,CAAC2G,GAAG,CAAC,CAAC;AAC1E,CAAC;;AAED;AACO,IAAII,QAAQ,GAAG,SAAXA,QAAQA,CAAab,IAAI,EAAErB,IAAI,EAAE;EAC1C,IAAI,EAAE,CAACqB,IAAI,IAAIA,IAAI,CAACU,QAAQ,KAAK,CAAC,IAAIV,IAAI,CAACU,QAAQ,KAAK,CAAC,IAAI,CAACV,IAAI,CAACc,KAAK,CAAC,EAAE;IACzE,OAAOd,IAAI,CAACc,KAAK,CAACnC,IAAI,CAAC;EACzB;AACF,CAAC;AAEM,IAAIoC,MAAM,GAAG,SAATA,MAAMA,CAAaf,IAAI,EAAErB,IAAI,EAAEqC,QAAQ,EAAE;EAClD,IAAIC,QAAQ,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,OAAO,EAAEN,KAAK,EAAEnI,KAAK;EAClDyI,OAAO,GAAG,SAAS;EACnBzI,KAAK,GAAG,KAAK,CAAC;EACduI,QAAQ,GAAG,KAAK,CAAC;EACjBD,QAAQ,GAAG,KAAK,CAAC;EACjBE,GAAG,GAAG,KAAK,CAAC;EACZL,KAAK,GAAGd,IAAI,CAACc,KAAK;EAClBE,QAAQ,GAAGA,QAAQ,IAAIjB,SAAS,CAACC,IAAI,CAAC;EACtC,IAAIgB,QAAQ,EAAE;IACZ;IACA;IACAG,GAAG,GAAGH,QAAQ,CAACK,gBAAgB,CAAC1C,IAAI,CAAC,IAAIqC,QAAQ,CAACrC,IAAI,CAAC;EACzD;EACA,IAAIqC,QAAQ,EAAE;IACZ,IAAIG,GAAG,KAAK,EAAE,IAAI,CAAC3C,YAAY,CAACwB,IAAI,CAACC,aAAa,EAAED,IAAI,CAAC,EAAE;MACzDmB,GAAG,GAAGN,QAAQ,CAACb,IAAI,EAAErB,IAAI,CAAC;IAC5B;IACA;IACA;IACA;IACA;IACA,IAAI2C,SAAS,CAAClD,IAAI,CAAC+C,GAAG,CAAC,IAAIC,OAAO,CAAChD,IAAI,CAACO,IAAI,CAAC,EAAE;MAC7C;MACAhG,KAAK,GAAGmI,KAAK,CAACnI,KAAK;MACnBuI,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;MACzBD,QAAQ,GAAGH,KAAK,CAACG,QAAQ;MACzB;MACAH,KAAK,CAACI,QAAQ,GAAGJ,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACnI,KAAK,GAAGwI,GAAG;MACnDA,GAAG,GAAGH,QAAQ,CAACrI,KAAK;MACpB;MACAmI,KAAK,CAACnI,KAAK,GAAGA,KAAK;MACnBmI,KAAK,CAACI,QAAQ,GAAGA,QAAQ;MACzBJ,KAAK,CAACG,QAAQ,GAAGA,QAAQ;IAC3B;EACF;EACA;EACA;EACA,IAAIE,GAAG,KAAK5M,SAAS,EAAE;IACrB,OAAO4M,GAAG,GAAG,EAAE;EACjB,CAAC,MAAM;IACL,OAAOA,GAAG;EACZ;AACF,CAAC;AAEM,IAAII,QAAQ,GAAG,SAAXA,QAAQA,CAAavB,IAAI,EAAErB,IAAI,EAAE6C,OAAO,EAAEC,MAAM,EAAE;EAC3D,IAAIC,GAAG;EACPA,GAAG,GAAGX,MAAM,CAACf,IAAI,EAAErB,IAAI,EAAE8C,MAAM,CAAC;EAChC,IAAID,OAAO,EAAE;IACX,OAAO9G,UAAU,CAACgH,GAAG,CAAC;EACxB,CAAC,MAAM;IACL,OAAOA,GAAG;EACZ;AACF,CAAC;AAEM,IAAIC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAa3B,IAAI,EAAErB,IAAI,EAAEiD,KAAK,EAAEC,WAAW,EAAEJ,MAAM,EAAE;EAClF,IAAI3P,CAAC,EAAEgQ,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEN,GAAG;EAC5B;EACA;EACA,IAAIE,KAAK,MAAMC,WAAW,GAAG,QAAQ,GAAG,SAAS,CAAC,EAAE;IAClD,OAAO,CAAC;EACV,CAAC,MAAM;IACLG,KAAK,GAAGrD,IAAI,KAAK,OAAO,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC;IAChE+C,GAAG,GAAG,CAAC;IACP,KAAK5P,CAAC,GAAG,CAAC,EAAEgQ,GAAG,GAAGE,KAAK,CAAC1Q,MAAM,EAAEQ,CAAC,GAAGgQ,GAAG,EAAEhQ,CAAC,EAAE,EAAE;MAC5CiQ,IAAI,GAAGC,KAAK,CAAClQ,CAAC,CAAC;MACf,IAAI8P,KAAK,KAAK,QAAQ,EAAE;QACtB;QACAF,GAAG,IAAIH,QAAQ,CAACvB,IAAI,EAAE4B,KAAK,GAAGG,IAAI,EAAE,IAAI,EAAEN,MAAM,CAAC;MACnD;MACA,IAAII,WAAW,EAAE;QACf,IAAID,KAAK,KAAK,SAAS,EAAE;UACvB;UACAF,GAAG,IAAIH,QAAQ,CAACvB,IAAI,YAAA7K,MAAA,CAAY4M,IAAI,GAAI,IAAI,EAAEN,MAAM,CAAC;QACvD;QACA,IAAIG,KAAK,KAAK,QAAQ,EAAE;UACtB;UACAF,GAAG,IAAIH,QAAQ,CAACvB,IAAI,WAAA7K,MAAA,CAAW4M,IAAI,YAAS,IAAI,EAAEN,MAAM,CAAC;QAC3D;MACF,CAAC,MAAM;QACL;QACAC,GAAG,IAAIH,QAAQ,CAACvB,IAAI,YAAA7K,MAAA,CAAY4M,IAAI,GAAI,IAAI,EAAEN,MAAM,CAAC;QACrD,IAAIG,KAAK,KAAK,SAAS,EAAE;UACvB;UACAF,GAAG,IAAIH,QAAQ,CAACvB,IAAI,WAAA7K,MAAA,CAAW4M,IAAI,YAAS,IAAI,EAAEN,MAAM,CAAC;QAC3D;MACF;IACF;IACA,OAAOC,GAAG;EACZ;AACF,CAAC;AAED,IAAIO,IAAI,GAAG,qCAAqC,CAACzG,MAAM;AAEvD,IAAI8F,SAAS,GAAG,IAAIpF,MAAM,CAAC,IAAI,GAAG+F,IAAI,GAAG,iBAAiB,EAAE,GAAG,CAAC;AAEzD,IAAIC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAalC,IAAI,EAAErB,IAAI,EAAEiD,KAAK,EAAE;EACzD,IAAIC,WAAW,EAAEJ,MAAM,EAAEC,GAAG,EAAES,gBAAgB;EAC9C;EACAA,gBAAgB,GAAG,IAAI;EACvBT,GAAG,GAAI/C,IAAI,KAAK,OAAO,GAAGqB,IAAI,CAACoC,WAAW,GAAGpC,IAAI,CAACqC,YAAa;EAC/DZ,MAAM,GAAG1B,SAAS,CAACC,IAAI,CAAC;EACxB6B,WAAW,GAAGN,QAAQ,CAACvB,IAAI,EAAE,WAAW,EAAE,KAAK,EAAEyB,MAAM,CAAC,KAAK,YAAY;EACzE;EACA;EACA;EACA,IAAIC,GAAG,IAAI,CAAC,IAAKA,GAAG,IAAI,IAAK,EAAE;IAC7B;IACAA,GAAG,GAAGX,MAAM,CAACf,IAAI,EAAErB,IAAI,EAAE8C,MAAM,CAAC;IAChC,IAAIC,GAAG,GAAG,CAAC,IAAKA,GAAG,IAAI,IAAK,EAAE;MAC5BA,GAAG,GAAG1B,IAAI,CAACc,KAAK,CAACnC,IAAI,CAAC;IACxB;IACA,IAAI2C,SAAS,CAAClD,IAAI,CAACsD,GAAG,CAAC,EAAE;MACvB;MACA,OAAOA,GAAG;IACZ;IACA;IACA;IACA;IACAS,gBAAgB,GAAGN,WAAW,IAAKH,GAAG,KAAK1B,IAAI,CAACc,KAAK,CAACnC,IAAI,CAAE;IAC5D;IACA+C,GAAG,GAAGhH,UAAU,CAACgH,GAAG,CAAC,IAAI,CAAC;EAC5B;EACA;EACA,OAAOA,GAAG,GAAGC,oBAAoB,CAAC3B,IAAI,EAAErB,IAAI,EAAEiD,KAAK,KAAKC,WAAW,GAAG,QAAQ,GAAG,SAAS,CAAC,EAAEM,gBAAgB,EAAEV,MAAM,CAAC;AACxH,CAAC;AAEM,IAAI9I,YAAK,GAAG,SAARA,KAAKA,CAAa+F,OAAO,EAAE;EACpC,OAAOwD,gBAAgB,CAACxD,OAAO,EAAE,OAAO,EAAE,SAAS,CAAC;AACtD,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,G;;;;;;;;AC3cA;AACA;AACA;AACA;AACA;AACA;AALA,IAMM4D,UAAU;EACd,SAAAA,WAAYC,aAAa,EAAE;IAAAC,eAAA,OAAAF,UAAA;IACzB;AACJ;AACA;AACA;IACI,IAAI,CAACG,WAAW,GAAG,EAAE;IACrB,IAAIF,aAAa,IAAI,IAAI,EAAE;MACzB,IAAI,CAACE,WAAW,CAAC/C,IAAI,CAAC4C,UAAU,CAACI,SAAS,CAACH,aAAa,CAAC,CAAC;IAC5D;EACF;;EAEA;AACF;AACA;AACA;EAHE,OAAAI,YAAA,CAAAL,UAAA;IAAArP,GAAA;IAAAZ,KAAA;IAuCA;AACF;AACA;AACA;IACE,SAAAuQ,UAAA,EAAY;MACV,OAAON,UAAU,CAACI,SAAS,CAAC,IAAI,CAACD,WAAW,CAAChP,IAAI,CAAC,GAAG,CAAC,CAAC;IACzD;EAAC;IAAAR,GAAA;IAAAZ,KAAA,EAED,SAAAa,SAAA,EAAW;MACT,OAAO,IAAI,CAAC0P,SAAS,CAAC,CAAC;IACzB;;IAEA;AACF;AACA;AACA;EAHE;IAAA3P,GAAA;IAAAZ,KAAA,EAIA,SAAAwQ,UAAA,EAAY;MACV,OAAO,IAAI,CAACC,MAAM;IACpB;;IAEA;AACF;AACA;AACA;AACA;EAJE;IAAA7P,GAAA;IAAAZ,KAAA,EAKA,SAAA0Q,UAAUD,MAAM,EAAE;MAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;MACpB,OAAO,IAAI;IACb;;IAEA;AACF;AACA;AACA;AACA;EAJE;IAAA7P,GAAA;IAAAZ,KAAA,EAKA,SAAA2Q,UAAUrE,IAAI,EAAEsE,QAAQ,EAAE5Q,KAAK,EAAE;MAC/B,IAAIiQ,UAAU,CAACY,SAAS,CAACD,QAAQ,CAAC,IAAI,IAAI,EAAE;QAC1CA,QAAQ,GAAGX,UAAU,CAACY,SAAS,CAACD,QAAQ,CAAC;MAC3C;MACA,IAAI,CAACR,WAAW,CAAC/C,IAAI,IAAAvK,MAAA,CAAIwJ,IAAI,OAAAxJ,MAAA,CAAI8N,QAAQ,OAAA9N,MAAA,CAAI9C,KAAK,CAAE,CAAC;MACrD,OAAO,IAAI;IACb;;IAEA;AACF;AACA;EAFE;IAAAY,GAAA;IAAAZ,KAAA,EAGA,SAAA8Q,IAAA,EAAM;MACJ,IAAI,CAACV,WAAW,CAAC/C,IAAI,CAAC,KAAK,CAAC;MAC5B,OAAO,IAAI;IACb;;IAEA;AACF;AACA;EAFE;IAAAzM,GAAA;IAAAZ,KAAA,EAGA,SAAA+Q,GAAA,EAAK;MACH,IAAI,CAACX,WAAW,CAAC/C,IAAI,CAAC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;;IAEA;AACF;AACA;AACA;AACA;EAJE;IAAAzM,GAAA;IAAAZ,KAAA,EAKA,SAAAgR,KAAA,EAAO;MACL,OAAO,IAAI,CAACR,SAAS,CAAC,CAAC,MAAG,CAAC,IAAI,CAAC3P,QAAQ,CAAC,CAAC,CAAC;IAC7C;;IAEA;AACF;AACA;AACA;AACA;AACA;EALE;IAAAD,GAAA;IAAAZ,KAAA,EAMA,SAAA0G,OAAOkK,QAAQ,EAAE5Q,KAAK,EAAE;MACtB,OAAO,IAAI,CAAC2Q,SAAS,CAAC,GAAG,EAAEC,QAAQ,EAAE5Q,KAAK,CAAC;IAC7C;;IAEA;AACF;AACA;AACA;AACA;AACA;EALE;IAAAY,GAAA;IAAAZ,KAAA,EAMA,SAAAsG,MAAMsK,QAAQ,EAAE5Q,KAAK,EAAE;MACrB,OAAO,IAAI,CAAC2Q,SAAS,CAAC,GAAG,EAAEC,QAAQ,EAAE5Q,KAAK,CAAC;IAC7C;;IAEA;AACF;AACA;AACA;AACA;AACA;EALE;IAAAY,GAAA;IAAAZ,KAAA,EAMA,SAAAiR,YAAYL,QAAQ,EAAE5Q,KAAK,EAAE;MAC3B,OAAO,IAAI,CAAC2Q,SAAS,CAAC,IAAI,EAAEC,QAAQ,EAAE5Q,KAAK,CAAC;IAC9C;;IAEA;AACF;AACA;AACA;AACA;AACA;EALE;IAAAY,GAAA;IAAAZ,KAAA,EAMA,SAAAkR,UAAUN,QAAQ,EAAE5Q,KAAK,EAAE;MACzB,OAAO,IAAI,CAAC2Q,SAAS,CAAC,IAAI,EAAEC,QAAQ,EAAE5Q,KAAK,CAAC;IAC9C;;IAEA;AACF;AACA;AACA;AACA;AACA;EALE;IAAAY,GAAA;IAAAZ,KAAA,EAMA,SAAAmR,UAAUP,QAAQ,EAAE5Q,KAAK,EAAE;MACzB,OAAO,IAAI,CAAC2Q,SAAS,CAAC,IAAI,EAAEC,QAAQ,EAAE5Q,KAAK,CAAC;IAC9C;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAAA,MAAMA,MAAK,EAAE;MACX,IAAI,CAACoQ,WAAW,CAAC/C,IAAI,CAACrN,MAAK,CAAC;MAC5B,OAAO,IAAI;IACb;;IAEA;AACF;EADE;IAAAY,GAAA;IAAAZ,KAAA,EA9JA,SAAAoR,KAAWlB,aAAa,EAAE;MACxB,OAAO,IAAI,IAAI,CAACA,aAAa,CAAC;IAChC;;IAEA;AACF;AACA;AACA;AACA;AACA;EALE;IAAAtP,GAAA;IAAAZ,KAAA,EAMA,SAAAqQ,UAAiBgB,UAAU,EAAE;MAC3B,IAAIA,UAAU,IAAI,IAAI,EAAE;QACtB,OAAOA,UAAU;MACnB;MACAA,UAAU,GAAGlS,MAAM,CAACkS,UAAU,CAAC;MAC/B,IAAMC,SAAS,GAAG,0CAA0C;;MAE5D;MACA,IAAMC,gBAAgB,GAAG,IAAI,GAAGD,SAAS,GAAG,YAAY;MACxD,IAAME,kBAAkB,GAAG,IAAI3H,MAAM,CAAC0H,gBAAgB,EAAE,GAAG,CAAC;MAC5DF,UAAU,GAAGA,UAAU,CAAC7I,OAAO,CAACgJ,kBAAkB,EAAE,UAAA5P,KAAK;QAAA,OAAIqO,UAAU,CAACY,SAAS,CAACjP,KAAK,CAAC;MAAA,EAAC;;MAEzF;MACA;MACA;MACA;MACA,IAAM6P,qBAAqB,GAAG,GAAG,GAAGlK,MAAM,CAACF,IAAI,CAAC4I,UAAU,CAACyB,eAAe,CAAC,CAAClR,GAAG,CAAC,UAAA0H,CAAC;QAAA,WAAApF,MAAA,CAAMoF,CAAC,OAAApF,MAAA,CAAIoF,CAAC;MAAA,CAAE,CAAC,CAAC9G,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;MAChH,IAAMuQ,mBAAmB,GAAG,eAAe;MAE3C,IAAMC,kBAAkB,GAAG,IAAI/H,MAAM,IAAA/G,MAAA,CAAI6O,mBAAmB,OAAA7O,MAAA,CAAI2O,qBAAqB,GAAI,GAAG,CAAC;MAC7FJ,UAAU,GAAGA,UAAU,CAAC7I,OAAO,CAACoJ,kBAAkB,EAAE,UAAChQ,KAAK;QAAA,OAAMqO,UAAU,CAACyB,eAAe,CAAC9P,KAAK,CAAC,IAAIA,KAAK;MAAA,CAAC,CAAC;MAE5G,OAAOyP,UAAU,CAAC7I,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC;IAC1C;EAAC;IAAA5H,GAAA;IAAAZ,KAAA,EA+HD,SAAA6R,SAAgBvF,IAAI,EAAEtM,KAAK,EAAE;MAC3B,OAAO,IAAI,IAAI,CAACsM,IAAI,CAAC,CAACtM,KAAK,CAACA,KAAK,CAAC;IACpC;;IAEA;AACF;AACA;AACA;EAHE;IAAAY,GAAA;IAAAZ,KAAA,EAIA,SAAAsG,MAAA,EAAe;MACb,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC;IAC1B;;IAEA;AACF;AACA;AACA;EAHE;IAAA1F,GAAA;IAAAZ,KAAA,EAIA,SAAA0G,OAAA,EAAgB;MACd,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC;IAC3B;;IAEA;AACF;AACA;AACA;EAHE;IAAA9F,GAAA;IAAAZ,KAAA,EAIA,SAAA8R,aAAA,EAAsB;MACpB,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC;IACjC;;IAEA;AACF;AACA;AACA;EAHE;IAAAlR,GAAA;IAAAZ,KAAA,EAIA,SAAA+R,cAAA,EAAuB;MACrB,OAAO,IAAI,IAAI,CAAC,eAAe,CAAC;IAClC;;IAEA;AACF;AACA;AACA;EAHE;IAAAnR,GAAA;IAAAZ,KAAA,EAIA,SAAAiR,YAAA,EAAqB;MACnB,OAAO,IAAI,IAAI,CAAC,aAAa,CAAC;IAChC;;IAEA;AACF;AACA;AACA;EAHE;IAAArQ,GAAA;IAAAZ,KAAA,EAIA,SAAAgS,mBAAA,EAA4B;MAC1B,OAAO,IAAI,IAAI,CAAC,oBAAoB,CAAC;IACvC;;IAEA;AACF;AACA;AACA;EAHE;IAAApR,GAAA;IAAAZ,KAAA,EAIA,SAAAkR,UAAA,EAAmB;MACjB,OAAO,IAAI,IAAI,CAAC,WAAW,CAAC;IAC9B;;IAEA;AACF;AACA;AACA;EAHE;IAAAtQ,GAAA;IAAAZ,KAAA,EAIA,SAAAmR,UAAA,EAAmB;MACjB,OAAO,IAAI,IAAI,CAAC,WAAW,CAAC;IAC9B;;IAEA;AACF;AACA;AACA;EAHE;IAAAvQ,GAAA;IAAAZ,KAAA,EAIA,SAAAiS,YAAA,EAAqB;MACnB,OAAO,IAAI,IAAI,CAAC,aAAa,CAAC;IAChC;;IAEA;AACF;AACA;AACA;EAHE;IAAArR,GAAA;IAAAZ,KAAA,EAIA,SAAAkS,KAAA,EAAc;MACZ,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC;IACzB;;IAEA;AACF;AACA;AACA;EAHE;IAAAtR,GAAA;IAAAZ,KAAA,EAIA,SAAAmS,MAAA,EAAe;MACb,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC;IAC1B;;IAEA;AACF;AACA;AACA;EAHE;IAAAvR,GAAA;IAAAZ,KAAA,EAIA,SAAAoS,MAAA,EAAe;MACb,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC;IAC1B;EAAC;AAAA;AAIH;AACA;AACA;AACAnC,UAAU,CAACY,SAAS,GAAG;EACrB,GAAG,EAAE,IAAI;EACT,IAAI,EAAE,IAAI;EACV,GAAG,EAAE,IAAI;EACT,GAAG,EAAE,IAAI;EACT,IAAI,EAAE,KAAK;EACX,IAAI,EAAE,KAAK;EACX,IAAI,EAAE,KAAK;EACX,IAAI,EAAE,IAAI;EACV,GAAG,EAAE,KAAK;EACV,GAAG,EAAE,KAAK;EACV,GAAG,EAAE,KAAK;EACV,GAAG,EAAE,KAAK;EACV,GAAG,EAAE;AACP,CAAC;;AAED;AACA;AACA;AACAZ,UAAU,CAACyB,eAAe,GAAG;EAC3B,cAAc,EAAE,IAAI;EACpB,aAAa,EAAE,IAAI;EACnB,cAAc,EAAE,IAAI;EACpB,aAAa,EAAE,IAAI;EACnB,UAAU,EAAE,IAAI;EAChB,YAAY,EAAE,IAAI;EAClB,WAAW,EAAE,IAAI;EACjB,QAAQ,EAAE,GAAG;EACb,sBAAsB,EAAE,KAAK;EAC7B,kBAAkB,EAAE,KAAK;EACzB,gBAAgB,EAAE,IAAI;EACtB,eAAe,EAAE,IAAI;EACrB,oBAAoB,EAAE,KAAK;EAC3B,iBAAiB,EAAE,KAAK;EACxB,eAAe,EAAE,IAAI;EACrB,cAAc,EAAE,IAAI;EACpB,YAAY,EAAE,IAAI;EAClB,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,IAAI;EACd,WAAW,EAAE,IAAI;EACjB,OAAO,EAAE,IAAI;EACb,OAAO,EAAE,IAAI;EACb,MAAM,EAAE,MAAM;EACd,OAAO,EAAE;AACX,CAAC;;AAED;AACA;AACA;AACAzB,UAAU,CAACoC,OAAO,GAAG,OAAO;AAEbpC,yDAAU,E;;;;;;;;;;;;;;;AClVa;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAtBA,IAuBMqC,SAAS,0BAAAC,WAAA;EACb,SAAAD,UAAYE,YAAY,EAAE;IAAArC,wBAAA,OAAAmC,SAAA;IAAA,OAAAG,UAAA,OAAAH,SAAA,GAClBE,YAAY;EACpB;;EAEA;AACF;AACA;AACA;AACA;AACA;EALEE,SAAA,CAAAJ,SAAA,EAAAC,WAAA;EAAA,OAAAjC,qBAAA,CAAAgC,SAAA;IAAA1R,GAAA;IAAAZ,KAAA,EAMA,SAAA0G,OAAOkK,QAAQ,EAAE5Q,KAAK,EAAE;MACtB,OAAO,IAAI,CAAC2Q,SAAS,CAAC,GAAG,EAAEC,QAAQ,EAAE5Q,KAAK,CAAC;IAC7C;;IAEA;AACF;AACA;AACA;AACA;AACA;EALE;IAAAY,GAAA;IAAAZ,KAAA,EAMA,SAAAsG,MAAMsK,QAAQ,EAAE5Q,KAAK,EAAE;MACrB,OAAO,IAAI,CAAC2Q,SAAS,CAAC,GAAG,EAAEC,QAAQ,EAAE5Q,KAAK,CAAC;IAC7C;;IAEA;AACF;AACA;AACA;AACA;AACA;EALE;IAAAY,GAAA;IAAAZ,KAAA,EAMA,SAAAiR,YAAYL,QAAQ,EAAE5Q,KAAK,EAAE;MAC3B,OAAO,IAAI,CAAC2Q,SAAS,CAAC,IAAI,EAAEC,QAAQ,EAAE5Q,KAAK,CAAC;IAC9C;;IAEA;AACF;AACA;AACA;AACA;AACA;EALE;IAAAY,GAAA;IAAAZ,KAAA,EAMA,SAAAkR,UAAUN,QAAQ,EAAE5Q,KAAK,EAAE;MACzB,OAAO,IAAI,CAAC2Q,SAAS,CAAC,IAAI,EAAEC,QAAQ,EAAE5Q,KAAK,CAAC;IAC9C;;IAEA;AACF;AACA;AACA;AACA;AACA;EALE;IAAAY,GAAA;IAAAZ,KAAA,EAMA,SAAAmR,UAAUP,QAAQ,EAAE5Q,KAAK,EAAE;MACzB,OAAO,IAAI,CAAC2Q,SAAS,CAAC,IAAI,EAAEC,QAAQ,EAAE5Q,KAAK,CAAC;IAC9C;;IAEA;AACF;AACA;AACA;AACA;AACA;EALE;IAAAY,GAAA;IAAAZ,KAAA,EAMA,SAAA2S,SAAS/B,QAAQ,EAAE5Q,KAAK,EAAE;MACxB,OAAO,IAAI,CAAC2Q,SAAS,CAAC,IAAI,EAAEC,QAAQ,EAAE5Q,KAAK,CAAC;IAC9C;;IAEA;AACF;AACA;AACA;AACA;AACA;EALE;IAAAY,GAAA;IAAAZ,KAAA,EAMA,SAAA4S,gBAAgBhC,QAAQ,EAAE5Q,KAAK,EAAE;MAC/B,OAAO,IAAI,CAAC2Q,SAAS,CAAC,KAAK,EAAEC,QAAQ,EAAE5Q,KAAK,CAAC;IAC/C;EAAC;AAAA,EAzEqBiQ,UAAU;AA4EnBqC,uDAAS,E;;;;;;;;;;;;;;ACrGxB;AACA;AACA;AACA;;AAQgB;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AANA,IAOMO,2BAAa;EACjB,SAAAA,cAAY3P,OAAO,EAAE;IAAAiN,4BAAA,OAAA0C,aAAA;IACnB,IAAI,CAACC,aAAa,GAAG5P,OAAO,IAAI,IAAI,GAAG,CAAC,CAAC,GAAG6P,kCAAS,CAAC7P,OAAO,CAAC;IAC9DyF,QAAQ,CAAC,IAAI,CAACmK,aAAa,EAAEE,4BAA4B,CAAC;EAC5D;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAVE,OAAA1C,yBAAA,CAAAuC,aAAA;IAAAjS,GAAA;IAAAZ,KAAA,EAWA,SAAAiT,KAAA,EAAO;MACL,IAAI,CAACC,eAAe,CAAC,CAAC;MACtB,IAAI,CAACC,YAAY,CAAC,CAAC;MACnB,OAAO,IAAI;IACb;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EAPE;IAAAvS,GAAA;IAAAZ,KAAA,EAQA,SAAAoT,IAAI9G,IAAI,EAAEtM,KAAK,EAAE;MACf,IAAI,CAAC8S,aAAa,CAACxG,IAAI,CAAC,GAAGtM,KAAK;MAChC,OAAO,IAAI;IACb;;IAEA;AACF;AACA;AACA;AACA;AACA;EALE;IAAAY,GAAA;IAAAZ,KAAA,EAMA,SAAAqT,IAAI/G,IAAI,EAAE;MACR,OAAO,IAAI,CAACwG,aAAa,CAACxG,IAAI,CAAC;IACjC;EAAC;IAAA1L,GAAA;IAAAZ,KAAA,EAED,SAAAsT,MAAMC,MAAM,EAAE;MACZC,4BAAM,CAAC,IAAI,CAACV,aAAa,EAAEC,kCAAS,CAACQ,MAAM,CAAC,CAAC;MAC7C,OAAO,IAAI;IACb;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;EANE;IAAA3S,GAAA;IAAAZ,KAAA,EAOA,SAAAmT,aAAA,EAAe;MACb,IAAInP,EAAE,EAAEvE,CAAC,EAAEgQ,GAAG,EAAEgE,aAAa;MAC7BA,aAAa,GAAG,OAAOC,QAAQ,KAAK,WAAW,IAAIA,QAAQ,KAAK,IAAI,GAAGA,QAAQ,CAACC,gBAAgB,CAAC,2BAA2B,CAAC,GAAG,KAAK,CAAC;MACtI,IAAIF,aAAa,EAAE;QACjB,KAAKhU,CAAC,GAAG,CAAC,EAAEgQ,GAAG,GAAGgE,aAAa,CAACxU,MAAM,EAAEQ,CAAC,GAAGgQ,GAAG,EAAEhQ,CAAC,EAAE,EAAE;UACpDuE,EAAE,GAAGyP,aAAa,CAAChU,CAAC,CAAC;UACrB,IAAI,CAACqT,aAAa,CAAC9O,EAAE,CAACuI,YAAY,CAAC,MAAM,CAAC,CAAC/D,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC,GAAGxE,EAAE,CAACuI,YAAY,CAAC,SAAS,CAAC;QACrG;MACF;MACA,OAAO,IAAI;IACb;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;EANE;IAAA3L,GAAA;IAAAZ,KAAA,EAOA,SAAAkT,gBAAA,EAAkB;MAAA,IAAAU,KAAA;MAChB,IAAIC,cAAc,EAAEC,KAAK,EAAEC,GAAG,EAAEC,QAAQ;MACxC,IAAG,OAAOC,OAAO,KAAK,WAAW,IAAIA,OAAO,KAAK,IAAI,IAAIA,OAAO,CAACC,GAAG,IAAID,OAAO,CAACC,GAAG,CAACC,cAAc,EAAE;QAClGN,cAAc,GAAGI,OAAO,CAACC,GAAG,CAACC,cAAc;QAC3CH,QAAQ,GAAG,8EAA8E;QACzFD,GAAG,GAAGC,QAAQ,CAACI,IAAI,CAACP,cAAc,CAAC;QACnC,IAAIE,GAAG,EAAE;UACP,IAAIA,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;YAClB,IAAI,CAACjB,aAAa,CAAC,YAAY,CAAC,GAAGiB,GAAG,CAAC,CAAC,CAAC;UAC3C;UACA,IAAIA,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;YAClB,IAAI,CAACjB,aAAa,CAAC,SAAS,CAAC,GAAGiB,GAAG,CAAC,CAAC,CAAC;UACxC;UACA,IAAIA,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;YAClB,IAAI,CAACjB,aAAa,CAAC,YAAY,CAAC,GAAGiB,GAAG,CAAC,CAAC,CAAC;UAC3C;UACA,IAAIA,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;YAClB,IAAI,CAACjB,aAAa,CAAC,aAAa,CAAC,GAAGiB,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI;UACpD;UACA,IAAIA,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;YAClB,IAAI,CAACjB,aAAa,CAAC,qBAAqB,CAAC,GAAGiB,GAAG,CAAC,CAAC,CAAC;UACpD;UACAD,KAAK,GAAGC,GAAG,CAAC,CAAC,CAAC;UACd,IAAID,KAAK,IAAI,IAAI,EAAE;YACjBA,KAAK,CAAC9S,KAAK,CAAC,GAAG,CAAC,CAACN,OAAO,CAAC,UAAAV,KAAK,EAAE;cAC9B,IAAAqU,YAAA,GAAarU,KAAK,CAACgB,KAAK,CAAC,GAAG,CAAC;gBAAAsT,aAAA,GAAAC,cAAA,CAAAF,YAAA;gBAAxBG,CAAC,GAAAF,aAAA;gBAAEpM,CAAC,GAAAoM,aAAA;cACT,IAAIpM,CAAC,IAAI,IAAI,EAAE;gBACbA,CAAC,GAAG,IAAI;cACV;cACA0L,KAAI,CAACd,aAAa,CAAC0B,CAAC,CAAC,GAAGtM,CAAC;YAC3B,CAAC,CAAC;UACJ;QACF;MACF;MACA,OAAO,IAAI;IACb;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAbE;IAAAtH,GAAA;IAAAZ,KAAA,EAcA,SAAAuT,OAAOkB,UAAU,EAAEC,SAAS,EAAE;MAC5B,QAAQ,KAAK;QACX,KAAKA,SAAS,KAAK,KAAK,CAAC;UACvB,IAAI,CAACtB,GAAG,CAACqB,UAAU,EAAEC,SAAS,CAAC;UAC/B,OAAO,IAAI,CAAC5B,aAAa;QAC3B,KAAK,CAAChL,gCAAQ,CAAC2M,UAAU,CAAC;UACxB,OAAO,IAAI,CAACpB,GAAG,CAACoB,UAAU,CAAC;QAC7B,KAAK,CAACE,0CAAa,CAACF,UAAU,CAAC;UAC7B,IAAI,CAACnB,KAAK,CAACmB,UAAU,CAAC;UACtB,OAAO,IAAI,CAAC3B,aAAa;QAC3B;UACE;UACA,OAAO,IAAI,CAACA,aAAa;MAC7B;IACF;;IAEA;AACF;AACA;AACA;AACA;EAJE;IAAAlS,GAAA;IAAAZ,KAAA,EAKA,SAAA4U,UAAA,EAAY;MACV,OAAO7B,kCAAS,CAAC,IAAI,CAACD,aAAa,CAAC;IACtC;EAAC;AAAA;AAIH,IAAME,4BAA4B,GAAG;EACnC6B,gBAAgB,EAAE,gBAAgB;EAClCC,0BAA0B,EAAE,IAAI;EAChCC,SAAS,EAAE,IAAI;EACfC,MAAM,EAAE,CAAC,OAAOvR,MAAM,KAAK,WAAW,IAAIA,MAAM,KAAK,IAAI,GAAGA,MAAM,CAACwR,QAAQ,GAAGxR,MAAM,CAACwR,QAAQ,CAACC,QAAQ,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM;AAChI,CAAC;AAEDrC,2BAAa,CAACsC,aAAa,GAAG,CAC5B,SAAS,EACT,YAAY,EACZ,UAAU,EACV,eAAe,EACf,YAAY,EACZ,OAAO,EACP,aAAa,EACb,UAAU,EACV,eAAe,EACf,YAAY,EACZ,kBAAkB,EAClB,4BAA4B,EAC5B,kBAAkB,EAClB,WAAW,EACX,QAAQ,EACR,sBAAsB,EACtB,qBAAqB,EACrB,SAAS,EACT,MAAM,EACN,eAAe,EACf,YAAY,EACZ,eAAe,EACf,SAAS,EACT,mBAAmB,EACnB,gBAAgB,CACjB;AAEctC,iFAAa,E;;;;;;;;AC/MX;AAAA,IAEXuC,WAAK;EACT;AACF;AACA;AACA;AACA;EACE,SAAAA,MAAYlS,OAAO,EAAE;IAAA,IAAA0Q,KAAA;IAAAzD,oBAAA,OAAAiF,KAAA;IACnB,IAAI,CAAClS,OAAO,GAAG,CAAC,CAAC;IACjB,IAAIA,OAAO,IAAI,IAAI,EAAE;MACnB,CAAC,cAAc,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,CAAC,CAACxC,OAAO,CAAC,UAACE,GAAG,EAAK;QAC9D,IAAIyU,GAAG;QACP,OAAOzB,KAAI,CAAC1Q,OAAO,CAACtC,GAAG,CAAC,GAAG,CAACyU,GAAG,GAAGnS,OAAO,CAACtC,GAAG,CAAC,KAAK,IAAI,GAAGyU,GAAG,GAAGnS,OAAO,CAACkH,SAAS,CAACxJ,GAAG,CAAC,CAAC;MACzF,CAAC,CAAC;IACJ;EACF;EAAC,OAAA0P,iBAAA,CAAA8E,KAAA;IAAAxU,GAAA;IAAAZ,KAAA,EAED,SAAAsV,aAAatV,KAAK,EAAE;MAClB,IAAI,CAACkD,OAAO,CAACoS,YAAY,GAAGtV,KAAK;MACjC,OAAO,IAAI;IACb;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAAuF,KAAKvF,KAAK,EAAE;MACV,IAAI,CAACkD,OAAO,CAACqC,IAAI,GAAGvF,KAAK;MACzB,OAAO,IAAI;IACb;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAAuV,SAASvV,KAAK,EAAE;MACd,IAAI,CAACkD,OAAO,CAACqS,QAAQ,GAAGvV,KAAK;MAC7B,OAAO,IAAI;IACb;;IAEA;AACF;AACA;AACA;AACA;EAJE;IAAAY,GAAA;IAAAZ,KAAA,EAKA,SAAAwV,YAAA,EAAc;MACZ,IAAIH,GAAG;MACP,OAAO,CAACA,GAAG,GAAG,IAAI,CAACnS,OAAO,CAACqS,QAAQ,KAAK,IAAI,GAAGF,GAAG,CAAC7M,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;IACjF;;IAEA;AACF;AACA;AACA;AACA;EAJE;IAAA5H,GAAA;IAAAZ,KAAA,EAKA,SAAAyV,gBAAA,EAAkB;MAChB,IAAI,IAAI,CAACvS,OAAO,CAAC+B,MAAM,IAAI,IAAI,EAAE;QAC/B,OAAO,IAAI,CAACuQ,WAAW,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAACtS,OAAO,CAAC+B,MAAM;MACvD,CAAC,MAAM;QACL,OAAO,IAAI,CAACuQ,WAAW,CAAC,CAAC;MAC3B;IACF;EAAC;IAAA5U,GAAA;IAAAZ,KAAA,EAED,SAAAiF,OAAOjF,KAAK,EAAE;MACZ,IAAI,CAACkD,OAAO,CAAC+B,MAAM,GAAGjF,KAAK;MAC3B,OAAO,IAAI;IACb;;IAEA;AACF;AACA;AACA;EAHE;IAAAY,GAAA;IAAAZ,KAAA,EAIA,SAAAa,SAAA,EAAW;MACT,IAAI6U,UAAU;MACdA,UAAU,GAAG,EAAE;MACf,IAAI,IAAI,CAACxS,OAAO,CAACqS,QAAQ,IAAI,IAAI,EAAE;QACjC,MAAM,sBAAsB;MAC9B;MACA,IAAI,EAAE,IAAI,CAACrS,OAAO,CAACoS,YAAY,KAAK,OAAO,CAAC,EAAE;QAC5CI,UAAU,CAACrI,IAAI,CAAC,IAAI,CAACnK,OAAO,CAACoS,YAAY,CAAC;MAC5C;MACA,IAAI,EAAE,IAAI,CAACpS,OAAO,CAACqC,IAAI,KAAK,QAAQ,CAAC,EAAE;QACrCmQ,UAAU,CAACrI,IAAI,CAAC,IAAI,CAACnK,OAAO,CAACqC,IAAI,CAAC;MACpC;MACAmQ,UAAU,CAACrI,IAAI,CAAC,IAAI,CAACoI,eAAe,CAAC,CAAC,CAAC;MACvC,OAAOE,8BAAO,CAACD,UAAU,CAAC,CAACtU,IAAI,CAAC,GAAG,CAAC;IACtC;EAAC;IAAAR,GAAA;IAAAZ,KAAA,EAED,SAAA4V,MAAA,EAAQ;MACN,OAAO,IAAI,IAAI,CAACC,WAAW,CAAC,IAAI,CAAC3S,OAAO,CAAC;IAC3C;EAAC;AAAA;AAIYkS,2DAAK,E;;;;;;;;;;;;;;;AC1FQ;AAQX;AAAA,IAEXU,mBAAS,0BAAAC,MAAA;EACb;AACF;AACA;AACA;EACE,SAAAD,UAAY5S,OAAO,EAAE;IAAA,IAAA0Q,KAAA;IAAAzD,wBAAA,OAAA2F,SAAA;IACnB,IAAIzO,IAAI;IACRuM,KAAA,GAAAnB,mBAAA,OAAAqD,SAAA,GAAM5S,OAAO;IACbmE,IAAI,GAAG,CAAC,cAAc,EAAE,cAAc,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,WAAW,EAAE,gBAAgB,EAAE,WAAW,EAAE,QAAQ,EAAE,eAAe,EAAE,aAAa,EAAE,aAAa,EAAE,kBAAkB,EAAE,MAAM,EAAE,WAAW,CAAC;IAC7N,IAAInE,OAAO,IAAI,IAAI,EAAE;MACnBmE,IAAI,CAAC3G,OAAO,CAAC,UAACE,GAAG,EAAK;QACpB,IAAIyU,GAAG;QACP,OAAOzB,KAAA,CAAK1Q,OAAO,CAACtC,GAAG,CAAC,GAAG,CAACyU,GAAG,GAAGnS,OAAO,CAACtC,GAAG,CAAC,KAAK,IAAI,GAAGyU,GAAG,GAAGnS,OAAO,CAACkH,SAAS,CAACxJ,GAAG,CAAC,CAAC;MACzF,CAAC,CAAC;IACJ;IACAgT,KAAA,CAAK1Q,OAAO,CAACoS,YAAY,GAAG,MAAM;IAAC,OAAA1B,KAAA;EACrC;EAAClB,kBAAA,CAAAoD,SAAA,EAAAC,MAAA;EAAA,OAAAzF,qBAAA,CAAAwF,SAAA;IAAAlV,GAAA;IAAAZ,KAAA,EAED,SAAAsV,aAAaA,aAAY,EAAE;MACzB,MAAM,4CAA4C;IACpD;EAAC;IAAA1U,GAAA;IAAAZ,KAAA,EAED,SAAAuF,KAAKA,KAAI,EAAE;MACT,MAAM,oCAAoC;IAC5C;EAAC;IAAA3E,GAAA;IAAAZ,KAAA,EAED,SAAAiF,OAAOA,OAAM,EAAE;MACb,MAAM,sCAAsC;IAC9C;EAAC;IAAArE,GAAA;IAAAZ,KAAA,EAED,SAAAgW,WAAWA,WAAU,EAAE;MACrB,IAAI,CAAC9S,OAAO,CAAC8S,UAAU,GAAGA,WAAU;MACpC,OAAO,IAAI;IACb;EAAC;IAAApV,GAAA;IAAAZ,KAAA,EAED,SAAAiW,SAASA,SAAQ,EAAE;MACjB,IAAI,CAAC/S,OAAO,CAAC+S,QAAQ,GAAGA,SAAQ;MAChC,OAAO,IAAI;IACb;EAAC;IAAArV,GAAA;IAAAZ,KAAA,EAED,SAAAkW,WAAWA,WAAU,EAAE;MACrB,IAAI,CAAChT,OAAO,CAACgT,UAAU,GAAGA,WAAU;MACpC,OAAO,IAAI;IACb;EAAC;IAAAtV,GAAA;IAAAZ,KAAA,EAED,SAAAmW,UAAUA,UAAS,EAAE;MACnB,IAAI,CAACjT,OAAO,CAACiT,SAAS,GAAGA,UAAS;MAClC,OAAO,IAAI;IACb;EAAC;IAAAvV,GAAA;IAAAZ,KAAA,EAED,SAAAoW,eAAeA,eAAc,EAAE;MAC7B,IAAI,CAAClT,OAAO,CAACkT,cAAc,GAAGA,eAAc;MAC5C,OAAO,IAAI;IACb;EAAC;IAAAxV,GAAA;IAAAZ,KAAA,EAED,SAAAqW,UAAUA,UAAS,EAAE;MACnB,IAAI,CAACnT,OAAO,CAACmT,SAAS,GAAGA,UAAS;MAClC,OAAO,IAAI;IACb;EAAC;IAAAzV,GAAA;IAAAZ,KAAA,EAED,SAAAsW,OAAOA,OAAM,EAAE;MACb,IAAI,CAACpT,OAAO,CAACoT,MAAM,GAAGA,OAAM;MAC5B,OAAO,IAAI;IACb;EAAC;IAAA1V,GAAA;IAAAZ,KAAA,EAED,SAAAuW,cAAcA,cAAa,EAAE;MAC3B,IAAI,CAACrT,OAAO,CAACqT,aAAa,GAAGA,cAAa;MAC1C,OAAO,IAAI;IACb;EAAC;IAAA3V,GAAA;IAAAZ,KAAA,EAED,SAAAwW,YAAYA,YAAW,EAAE;MACvB,IAAI,CAACtT,OAAO,CAACsT,WAAW,GAAGA,YAAW;MACtC,OAAO,IAAI;IACb;EAAC;IAAA5V,GAAA;IAAAZ,KAAA,EAED,SAAAyW,YAAaA,YAAW,EAAC;MACvB,IAAI,CAACvT,OAAO,CAACuT,WAAW,GAAGA,YAAW;MACtC,OAAO,IAAI;IACb;EAAC;IAAA7V,GAAA;IAAAZ,KAAA,EAED,SAAA0W,iBAAkBA,iBAAgB,EAAC;MACjC,IAAI,CAACxT,OAAO,CAACwT,gBAAgB,GAAGA,iBAAgB;MAChD,OAAO,IAAI;IACb;EAAC;IAAA9V,GAAA;IAAAZ,KAAA,EAED,SAAA2W,KAAKA,KAAI,EAAE;MACT,IAAI,CAACzT,OAAO,CAACyT,IAAI,GAAGA,KAAI;MACxB,OAAO,IAAI;IACb;EAAC;IAAA/V,GAAA;IAAAZ,KAAA,EAED,SAAA4W,UAAUA,UAAS,EAAE;MACnB,IAAI,CAAC1T,OAAO,CAAC0T,SAAS,GAAGA,UAAS;MAClC,OAAO,IAAI;IACb;;IAEA;AACF;AACA;AACA;AACA;EAJE;IAAAhW,GAAA;IAAAZ,KAAA,EAKA,SAAAa,SAAA,EAAW;MACT,IAAI6U,UAAU,EAAEmB,WAAW,EAAEC,QAAQ,EAAEvB,QAAQ,EAAEwB,EAAE,EAAEC,GAAG,EAAEnY,KAAK,EAAE4P,KAAK,EAAEkI,IAAI,EAAEM,UAAU;MACxFxI,KAAK,GAAG,IAAI,CAACyI,mBAAmB,CAAC,CAAC;MAClC,IAAI,IAAI,CAAChU,OAAO,CAACqS,QAAQ,IAAI,IAAI,EAAE;QACjCA,QAAQ,GAAG,IAAI,CAACE,eAAe,CAAC,CAAC;MACnC;MACA,IAAI,IAAI,CAACvS,OAAO,CAACyT,IAAI,IAAI,IAAI,EAAE;QAC7BE,WAAW,GAAG,CAACrM,OAAO,CAAC+K,QAAQ,CAAC;QAChCuB,QAAQ,GAAG,CAACtM,OAAO,CAACiE,KAAK,CAAC;QAC1B,IAAIoI,WAAW,IAAIC,QAAQ,IAAI,CAACD,WAAW,IAAI,CAACC,QAAQ,EAAE;UACxD,MAAM,4HAA4H;QACpI;QACAC,EAAE,GAAG,oBAAoB;QACzBlY,KAAK,GAAG,CAAC;QACT;QACAoY,UAAU,GAAG3O,WAAW,CAAC,IAAI,CAACpF,OAAO,CAACyT,IAAI,EAAE,QAAQ,CAAC;QACrDA,IAAI,GAAG,EAAE;QACT,OAAOK,GAAG,GAAGD,EAAE,CAAC3C,IAAI,CAAC6C,UAAU,CAAC,EAAE;UAChCN,IAAI,IAAIrO,WAAW,CAAC2O,UAAU,CAAC5X,KAAK,CAACR,KAAK,EAAEmY,GAAG,CAACG,KAAK,CAAC,CAAC;UACvDR,IAAI,IAAIK,GAAG,CAAC,CAAC,CAAC;UACdnY,KAAK,GAAGmY,GAAG,CAACG,KAAK,GAAGH,GAAG,CAAC,CAAC,CAAC,CAAC/X,MAAM;QACnC;QACA0X,IAAI,IAAIrO,WAAW,CAAC2O,UAAU,CAAC5X,KAAK,CAACR,KAAK,CAAC,CAAC;MAC9C;MACA6W,UAAU,GAAG,CAAC,IAAI,CAACxS,OAAO,CAACoS,YAAY,EAAE7G,KAAK,EAAE8G,QAAQ,EAAEoB,IAAI,CAAC;MAC/D,OAAOhB,8BAAO,CAACD,UAAU,CAAC,CAACtU,IAAI,CAAC,GAAG,CAAC;IACtC;EAAC;IAAAR,GAAA;IAAAZ,KAAA,EAED,SAAAkX,oBAAA,EAAsB;MACpB;MACA,IAAI,CAAC1M,OAAO,CAAC,IAAI,CAACtH,OAAO,CAAC0T,SAAS,CAAC,EAAE;QACpC,OAAO,IAAI,CAAC1T,OAAO,CAAC0T,SAAS;MAC/B;MACA,IAAIlB,UAAU;MACdA,UAAU,GAAG,EAAE;MACf,IAAI,IAAI,CAACxS,OAAO,CAACgT,UAAU,KAAK,QAAQ,EAAE;QACxCR,UAAU,CAACrI,IAAI,CAAC,IAAI,CAACnK,OAAO,CAACgT,UAAU,CAAC;MAC1C;MACA,IAAI,IAAI,CAAChT,OAAO,CAACiT,SAAS,KAAK,QAAQ,EAAE;QACvCT,UAAU,CAACrI,IAAI,CAAC,IAAI,CAACnK,OAAO,CAACiT,SAAS,CAAC;MACzC;MACA,IAAI,IAAI,CAACjT,OAAO,CAACkT,cAAc,KAAK,MAAM,EAAE;QAC1CV,UAAU,CAACrI,IAAI,CAAC,IAAI,CAACnK,OAAO,CAACkT,cAAc,CAAC;MAC9C;MACAV,UAAU,CAACrI,IAAI,CAAC,IAAI,CAACnK,OAAO,CAACmT,SAAS,CAAC;MACvC,IAAI,IAAI,CAACnT,OAAO,CAACoT,MAAM,KAAK,MAAM,EAAE;QAClCZ,UAAU,CAACrI,IAAI,CAAC,IAAI,CAACnK,OAAO,CAACoT,MAAM,CAAC;MACtC;MACA,IAAI,EAAE9L,OAAO,CAAC,IAAI,CAACtH,OAAO,CAACqT,aAAa,CAAC,IAAI,CAACpO,YAAY,CAAC,IAAI,CAACjF,OAAO,CAACqT,aAAa,CAAC,CAAC,EAAE;QACvFb,UAAU,CAACrI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAACnK,OAAO,CAACqT,aAAa,CAAC;MACjE;MACA,IAAI,EAAE/L,OAAO,CAAC,IAAI,CAACtH,OAAO,CAACsT,WAAW,CAAC,IAAI,CAACrO,YAAY,CAAC,IAAI,CAACjF,OAAO,CAACsT,WAAW,CAAC,CAAC,EAAE;QACnFd,UAAU,CAACrI,IAAI,CAAC,eAAe,GAAG,IAAI,CAACnK,OAAO,CAACsT,WAAW,CAAC;MAC7D;MACA,IAAI,CAAEhM,OAAO,CAAC,IAAI,CAACtH,OAAO,CAACwT,gBAAgB,CAAE,EAAE;QAC7ChB,UAAU,CAACrI,IAAI,CAAC,YAAY,GAAC,IAAI,CAACnK,OAAO,CAACwT,gBAAgB,CAAC;MAC7D;MACA,IAAI,CAAElM,OAAO,CAAC,IAAI,CAACtH,OAAO,CAACuT,WAAW,CAAE,EAAE;QACxCf,UAAU,CAACrI,IAAI,CAAC,UAAU,GAAC,IAAI,CAACnK,OAAO,CAACuT,WAAY,CAAC;MACvD;MACA,IAAI,CAACjM,OAAO,CAACmL,8BAAO,CAACD,UAAU,CAAC,CAAC,EAAE;QACjC,IAAIlL,OAAO,CAAC,IAAI,CAACtH,OAAO,CAAC8S,UAAU,CAAC,EAAE;UACpC,iCAAAlT,MAAA,CAAiC4S,UAAU;QAC7C;QACA,IAAIlL,OAAO,CAAC,IAAI,CAACtH,OAAO,CAAC+S,QAAQ,CAAC,IAAI,CAAC9N,YAAY,CAAC,IAAI,CAACjF,OAAO,CAAC+S,QAAQ,CAAC,EAAE;UAC1E,MAAM,uBAAuB;QAC/B;MACF;MACAP,UAAU,CAAC0B,OAAO,CAAC,IAAI,CAAClU,OAAO,CAAC8S,UAAU,EAAE,IAAI,CAAC9S,OAAO,CAAC+S,QAAQ,CAAC;MAClEP,UAAU,GAAGC,8BAAO,CAACD,UAAU,CAAC,CAACtU,IAAI,CAAC,GAAG,CAAC;MAC1C,OAAOsU,UAAU;IACnB;EAAC;AAAA,EA3KqBN,WAAK;AA6K5B;AAEcU,iEAAS,E;;;;;;;;;;;;;;;ACzLY;AAAA,IAE9BuB,cAAc,0BAAAC,UAAA;EAClB;AACF;AACA;AACA;AACA;EACE,SAAAD,eAAYnU,OAAO,EAAE;IAAA,IAAA0Q,KAAA;IAAAzD,6BAAA,OAAAkH,cAAA;IACnBzD,KAAA,GAAAnB,wBAAA,OAAA4E,cAAA,GAAMnU,OAAO;IACb0Q,KAAA,CAAK1Q,OAAO,CAACoS,YAAY,GAAG,WAAW;IAAC,OAAA1B,KAAA;EAC1C;EAAClB,uBAAA,CAAA2E,cAAA,EAAAC,UAAA;EAAA,OAAAhH,0BAAA,CAAA+G,cAAA;AAAA,EAT0BvB,SAAS;AAYvBuB,iEAAc,E;;;;;;;;;;;;;;;ACdD;AAKX;AAAA,IAEXE,qBAAU,0BAAAxB,MAAA;EACd;AACF;AACA;AACA;AACA;AACA;EACE,SAAAwB,WAAYrU,OAAO,EAAE;IAAA,IAAA0Q,KAAA;IAAAzD,yBAAA,OAAAoH,UAAA;IACnB3D,KAAA,GAAAnB,oBAAA,OAAA8E,UAAA,GAAMrU,OAAO;IACb,IAAI4E,gCAAQ,CAAC5E,OAAO,CAAC,EAAE;MACrB0Q,KAAA,CAAK1Q,OAAO,CAAC+H,GAAG,GAAG/H,OAAO;IAC5B,CAAC,MAAM,IAAIA,OAAO,IAAI,IAAI,GAAGA,OAAO,CAAC+H,GAAG,GAAG,KAAK,CAAC,EAAE;MACjD2I,KAAA,CAAK1Q,OAAO,CAAC+H,GAAG,GAAG/H,OAAO,CAAC+H,GAAG;IAChC;IAAC,OAAA2I,KAAA;EACH;EAAClB,mBAAA,CAAA6E,UAAA,EAAAxB,MAAA;EAAA,OAAAzF,sBAAA,CAAAiH,UAAA;IAAA3W,GAAA;IAAAZ,KAAA,EAED,SAAAiL,IAAIA,IAAG,EAAE;MACP,IAAI,CAAC/H,OAAO,CAAC+H,GAAG,GAAGA,IAAG;MACtB,OAAO,IAAI;IACb;;IAEA;AACF;AACA;AACA;AACA;EAJE;IAAArK,GAAA;IAAAZ,KAAA,EAKA,SAAAa,SAAA,EAAW;MACT,gBAAAiC,MAAA,CAAgBkI,eAAe,CAAC,IAAI,CAAC9H,OAAO,CAAC+H,GAAG,CAAC;IACnD;EAAC;AAAA,EA5BsBmK,WAAK;AAgCfmC,oEAAU,E;;;;;;;;;;;;;;;;;ACvCa;AACQ;AAW9B;AAEkB;AACQ;AACU;AACR;;AAE5C;AACA;AACA;AACA;AAHA,IAIMC,gBAAK;EACT;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAAA,MAAYlL,IAAI,EAAEmL,SAAS,EAAsB;IAAA,IAApBxD,OAAO,GAAAhS,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGyV,gCAAQ;IAAAvH,yBAAA,OAAAqH,KAAA;IAC7C;AACJ;AACA;AACA;IACI,IAAI,CAAClL,IAAI,GAAGA,IAAI;IAChB;AACJ;AACA;AACA;IACI,IAAI,CAACmL,SAAS,GAAGA,SAAS;IAC1B;AACJ;AACA;AACA;IACI,IAAI,CAACxD,OAAO,GAAGA,OAAO;EACxB;;EAEA;AACF;AACA;AACA;AACA;AACA;EALE,OAAA3D,sBAAA,CAAAkH,KAAA;IAAA5W,GAAA;IAAAZ,KAAA,EAMA,SAAAoT,IAAIuE,SAAS,EAAE;MACb,IAAI,CAACA,SAAS,GAAGA,SAAS;MAC1B,OAAO,IAAI;IACb;;IAEA;AACF;AACA;AACA;AACA;EAJE;IAAA/W,GAAA;IAAAZ,KAAA,EAKA,SAAAuQ,UAAA,EAAY;MACV,IAAIlB,GAAG,EAAEuI,KAAK;MACdvI,GAAG,GAAG,IAAI,CAACrP,KAAK,CAAC,CAAC;MAClB4X,KAAK,GAAGC,8BAAO,CAACxI,GAAG,CAAC,IAAIsF,0CAAa,CAACtF,GAAG,CAAC,IAAIvH,gCAAQ,CAACuH,GAAG,CAAC,GAAG,CAAC7E,OAAO,CAAC6E,GAAG,CAAC,GAAGA,GAAG,IAAI,IAAI;MACzF,IAAK,IAAI,CAACoI,SAAS,IAAI,IAAI,IAAKG,KAAK,EAAE;QACrC,UAAA9U,MAAA,CAAU,IAAI,CAAC2U,SAAS,OAAA3U,MAAA,CAAIuM,GAAG;MACjC,CAAC,MAAM;QACL,OAAO,EAAE;MACX;IACF;;IAEA;AACF;AACA;AACA;EAHE;IAAAzO,GAAA;IAAAZ,KAAA,EAIA,SAAAA,MAAA,EAAQ;MACN,OAAO,IAAI,CAACiU,OAAO,CAAC,IAAI,CAAC0D,SAAS,CAAC;IACrC;EAAC;IAAA/W,GAAA;IAAAZ,KAAA,EAED,SAAA8X,WAAkB9X,KAAK,EAAE;MACvB,OAAOA,KAAK,IAAI,IAAI,GAAGA,KAAK,CAACwI,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,KAAK,CAAC;IAC7D;EAAC;IAAA5H,GAAA;IAAAZ,KAAA,EAED,SAAA+X,YAAmBC,GAAG,EAAE;MACtB,IAAGA,GAAG,IAAI,IAAI,EAAE;QACd,OAAO,EAAE;MACX,CAAC,MAAM,IAAIH,8BAAO,CAACG,GAAG,CAAC,EAAE;QACvB,OAAOA,GAAG;MACZ,CAAC,MAAM;QACL,OAAO,CAACA,GAAG,CAAC;MACd;IACF;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAXE;IAAApX,GAAA;IAAAZ,KAAA,EAYA,SAAAiY,qBAA4BC,KAAK,EAAE;MACjC,IAAIC,KAAK;MACT,QAAQD,KAAK,CAACrC,WAAW;QACvB,KAAKtO,MAAM;UACT4Q,KAAK,GAAG,EAAE;UACV,IAAI,OAAO,IAAID,KAAK,EAAE;YACpBC,KAAK,GAAGD,KAAK,CAACE,KAAK;YACnB,IAAI,SAAS,IAAIF,KAAK,EAAE;cACtBC,KAAK,IAAI,GAAG,GAAGD,KAAK,CAACG,OAAO;cAC5B,IAAI,OAAO,IAAIH,KAAK,EAAE;gBACpBC,KAAK,IAAI,GAAG,GAAGD,KAAK,CAACI,KAAK;gBAC1B,IAAI,UAAU,IAAIJ,KAAK,IAAIA,KAAK,CAACK,QAAQ,KAAK,KAAK,EAAE;kBACnDJ,KAAK,IAAI,aAAa;gBACxB;cACF;YACF;UACF;UACA,OAAOA,KAAK;QACd,KAAKhZ,MAAM;UACT,OAAO+Y,KAAK;QACd;UACE,OAAO,IAAI;MACf;IACF;EAAC;AAAA;AAAA,IAGGM,qBAAU,0BAAAC,MAAA;EACd;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAAD,WAAYlM,IAAI,EAAEmL,SAAS,EAAkC;IAAA,IAAA7D,KAAA;IAAA,IAAhC8E,GAAG,GAAAzW,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,GAAG;IAAA,IAAEgS,OAAO,GAAAhS,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGC,SAAS;IAAAiO,yBAAA,OAAAqI,UAAA;IACzD5E,KAAA,GAAAnB,oBAAA,OAAA+F,UAAA,GAAMlM,IAAI,EAAEmL,SAAS,EAAExD,OAAO;IAC9BL,KAAA,CAAK8E,GAAG,GAAGA,GAAG;IAAC,OAAA9E,KAAA;EACjB;EAAClB,mBAAA,CAAA8F,UAAA,EAAAC,MAAA;EAAA,OAAAnI,sBAAA,CAAAkI,UAAA;IAAA5X,GAAA;IAAAZ,KAAA,EAED,SAAAuQ,UAAA,EAAY;MACV,IAAI,IAAI,CAACkH,SAAS,IAAI,IAAI,EAAE;QAC1B,IAAIkB,UAAU,GAAG,IAAI,CAAC3Y,KAAK,CAAC,CAAC;QAC7B,IAAIwK,OAAO,CAACmO,UAAU,CAAC,EAAE;UACvB,OAAO,EAAE;QACX,CAAC,MAAM,IAAI7Q,gCAAQ,CAAC6Q,UAAU,CAAC,EAAE;UAC/B,UAAA7V,MAAA,CAAU,IAAI,CAAC2U,SAAS,OAAA3U,MAAA,CAAI6V,UAAU;QACxC,CAAC,MAAM;UACL,IAAIC,IAAI,GAAGD,UAAU,CAACnY,GAAG,CAAC,UAAAqY,CAAC;YAAA,OAAErP,oCAAU,CAACqP,CAAC,CAACtI,SAAS,CAAC,GAAGsI,CAAC,CAACtI,SAAS,CAAC,CAAC,GAAGsI,CAAC;UAAA,EAAC,CAACzX,IAAI,CAAC,IAAI,CAACsX,GAAG,CAAC;UACxF,UAAA5V,MAAA,CAAU,IAAI,CAAC2U,SAAS,OAAA3U,MAAA,CAAI8V,IAAI;QAClC;MACF,CAAC,MAAM;QACL,OAAO,EAAE;MACX;IACF;EAAC;IAAAhY,GAAA;IAAAZ,KAAA,EAED,SAAAA,MAAA,EAAQ;MAAA,IAAA8Y,MAAA;MACN,IAAIjB,8BAAO,CAAC,IAAI,CAACF,SAAS,CAAC,EAAE;QAC3B,OAAO,IAAI,CAACA,SAAS,CAACnX,GAAG,CAAC,UAAA0H,CAAC;UAAA,OAAE4Q,MAAI,CAAC7E,OAAO,CAAC/L,CAAC,CAAC;QAAA,EAAC;MAC/C,CAAC,MAAM;QACL,OAAO,IAAI,CAAC+L,OAAO,CAAC,IAAI,CAAC0D,SAAS,CAAC;MACrC;IACF;EAAC;IAAA/W,GAAA;IAAAZ,KAAA,EAED,SAAAoT,IAAIuE,SAAS,EAAE;MACb,IAAKA,SAAS,IAAI,IAAI,IAAKE,8BAAO,CAACF,SAAS,CAAC,EAAE;QAC7C,OAAAoB,IAAA,CAAAC,yBAAA,CAAAR,UAAA,CAAA1U,SAAA,gBAAA2F,IAAA,OAAiBkO,SAAS;MAC5B,CAAC,MAAM;QACL,OAAAoB,IAAA,CAAAC,yBAAA,CAAAR,UAAA,CAAA1U,SAAA,gBAAA2F,IAAA,OAAiB,CAACkO,SAAS,CAAC;MAC9B;IACF;EAAC;AAAA,EA/CsBH,gBAAK;AAAA,IAkDxByB,8BAAmB,0BAAAC,OAAA;EACvB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAAD,oBAAY3M,IAAI,EAAmD;IAAA,IAAA6M,MAAA;IAAA,IAAjD1B,SAAS,GAAAxV,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,GAAG;IAAA,IAAEyW,GAAG,GAAAzW,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,GAAG;IAAA,IAAEgS,OAAO,GAAAhS,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGC,SAAS;IAAAiO,yBAAA,OAAA8I,mBAAA;IAC/DE,MAAA,GAAA1G,oBAAA,OAAAwG,mBAAA,GAAM3M,IAAI,EAAEmL,SAAS,EAAExD,OAAO;IAC9BkF,MAAA,CAAKT,GAAG,GAAGA,GAAG;IAAC,OAAAS,MAAA;EACjB;;EAEA;AACF;AACA;AACA;EAHEzG,mBAAA,CAAAuG,mBAAA,EAAAC,OAAA;EAAA,OAAA5I,sBAAA,CAAA2I,mBAAA;IAAArY,GAAA;IAAAZ,KAAA,EAIA,SAAAuQ,UAAA,EAAY;MAAA,IAAA6I,MAAA;MACV,IAAI7O,MAAM,GAAG,EAAE;MACf,IAAM8E,GAAG,GAAG,IAAI,CAACrP,KAAK,CAAC,CAAC;MAExB,IAAIwK,OAAO,CAAC6E,GAAG,CAAC,EAAE;QAChB,OAAO9E,MAAM;MACf;;MAEA;MACA,IAAI5C,mBAAU,CAAC0H,GAAG,CAAC,EAAE;QACnB,IAAMgK,MAAM,GAAGhK,GAAG,CAACjO,IAAI,CAAC,IAAI,CAACsX,GAAG,CAAC,CAAC,CAAE;QACpC,IAAI,CAAClO,OAAO,CAAC6O,MAAM,CAAC,EAAE;UACpB;UACA9O,MAAM,MAAAzH,MAAA,CAAM,IAAI,CAAC2U,SAAS,OAAA3U,MAAA,CAAIuW,MAAM,CAAE;QACxC;MACF,CAAC,MAAM;QAAE;QACP9O,MAAM,GAAG8E,GAAG,CAAC7O,GAAG,CAAC,UAAAqY,CAAC,EAAI;UACpB,IAAI/Q,gCAAQ,CAAC+Q,CAAC,CAAC,IAAI,CAACrO,OAAO,CAACqO,CAAC,CAAC,EAAE;YAC9B,UAAA/V,MAAA,CAAUsW,MAAI,CAAC3B,SAAS,OAAA3U,MAAA,CAAI+V,CAAC;UAC/B;UACA,IAAIrP,oCAAU,CAACqP,CAAC,CAACtI,SAAS,CAAC,EAAE;YAC3B,OAAOsI,CAAC,CAACtI,SAAS,CAAC,CAAC;UACtB;UACA,IAAIoE,0CAAa,CAACkE,CAAC,CAAC,IAAI,CAACrO,OAAO,CAACqO,CAAC,CAAC,EAAE;YACnC,OAAO,IAAIS,kBAAc,CAACT,CAAC,CAAC,CAACtI,SAAS,CAAC,CAAC;UAC1C;UACA,OAAOrO,SAAS;QAClB,CAAC,CAAC,CAACsF,MAAM,CAAC,UAAAqR,CAAC;UAAA,OAAEA,CAAC;QAAA,EAAC;MACjB;MACA,OAAOtO,MAAM;IACf;EAAC;IAAA3J,GAAA;IAAAZ,KAAA,EAED,SAAAoT,IAAImG,UAAU,EAAE;MACd,IAAI,CAAC5B,SAAS,GAAG4B,UAAU;MAC3B,IAAI1B,8BAAO,CAAC,IAAI,CAACF,SAAS,CAAC,EAAE;QAC3B,OAAAoB,IAAA,CAAAC,yBAAA,CAAAC,mBAAA,CAAAnV,SAAA,gBAAA2F,IAAA,OAAiB,IAAI,CAACkO,SAAS;MACjC,CAAC,MAAM;QACL,OAAAoB,IAAA,CAAAC,yBAAA,CAAAC,mBAAA,CAAAnV,SAAA,gBAAA2F,IAAA,OAAiB,CAAC,IAAI,CAACkO,SAAS,CAAC;MACnC;IACF;EAAC;AAAA,EA3D+BH,gBAAK;AA8DvC,IAAMgC,cAAc,GAAG,8BAA8B;AACrD,IAAMC,kBAAkB,GAAG,GAAG,GAAGD,cAAc,GAAG,WAAW;AAAC,IAExDE,qBAAU,0BAAAC,OAAA;EAEd;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAAD,WAAYpN,IAAI,EAAEmL,SAAS,EAAyC;IAAA,IAAvCxD,OAAO,GAAAhS,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGyX,UAAU,CAACE,gBAAgB;IAAAzJ,yBAAA,OAAAuJ,UAAA;IAAA,OAAAjH,oBAAA,OAAAiH,UAAA,GAC1DpN,IAAI,EAAEmL,SAAS,EAAExD,OAAO;EAChC;EAACvB,mBAAA,CAAAgH,UAAA,EAAAC,OAAA;EAAA,OAAArJ,sBAAA,CAAAoJ,UAAA;IAAA9Y,GAAA;IAAAZ,KAAA,EACD,SAAA4Z,iBAAwB5Z,KAAK,EAAE;MAE7B,IAAI6Z,MAAM,GAAG1a,MAAM,CAACa,KAAK,CAAC,CAAC4B,KAAK,CAAC,IAAIiI,MAAM,CAAC,GAAG,GAAG4P,kBAAkB,GAAG,GAAG,CAAC,CAAC;MAC5E,IAAII,MAAM,EAAE;QACV,IAAIC,QAAQ,GAAGD,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,GAAG,GAAG,GAAG,EAAE;QAC3C7Z,KAAK,GAAG,CAAC6Z,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,IAAIC,QAAQ;MAC7C;MACA,OAAO7J,UAAU,CAACI,SAAS,CAACrQ,KAAK,CAAC;IACpC;EAAC;AAAA,EAvBsBwX,gBAAK;AAAA,IA0BxBuC,mBAAQ,0BAAAC,OAAA;EACZ,SAAAD,SAAYzN,IAAI,EAAEmL,SAAS,EAAsB;IAAA,IAApBxD,OAAO,GAAAhS,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGyV,gCAAQ;IAAAvH,yBAAA,OAAA4J,QAAA;IAAA,OAAAtH,oBAAA,OAAAsH,QAAA,GACvCzN,IAAI,EAAEmL,SAAS,EAAExD,OAAO;EAChC;EAACvB,mBAAA,CAAAqH,QAAA,EAAAC,OAAA;EAAA,OAAA1J,sBAAA,CAAAyJ,QAAA;IAAAnZ,GAAA;IAAAZ,KAAA,EAED,SAAAuQ,UAAA,EAAY;MACV,OAAO,IAAI,CAACvQ,KAAK,CAAC,CAAC;IACrB;EAAC;AAAA,EAPoBwX,gBAAK;AAAA,IAWtByC,qBAAU,0BAAAC,OAAA;EAAA,SAAAD,WAAA;IAAA9J,yBAAA,OAAA8J,UAAA;IAAA,OAAAxH,oBAAA,OAAAwH,UAAA,EAAAhY,SAAA;EAAA;EAAAyQ,mBAAA,CAAAuH,UAAA,EAAAC,OAAA;EAAA,OAAA5J,sBAAA,CAAA2J,UAAA;IAAArZ,GAAA;IAAAZ,KAAA;IACd;IACA;IACA;IACA,SAAAA,MAAA,EAAQ;MACN,IAAI,IAAI,CAAC2X,SAAS,IAAI,IAAI,EAAE;QAC1B,OAAO,EAAE;MACX;MACA,IAAIpN,MAAM;MACV,IAAI,IAAI,CAACoN,SAAS,YAAYvC,WAAK,EAAE;QACnC7K,MAAM,GAAG,IAAI,CAACoN,SAAS;MACzB,CAAC,MAAM,IAAIhD,0CAAa,CAAC,IAAI,CAACgD,SAAS,CAAC,EAAE;QACxC,IAAIwC,YAAY,GAAG1P,iBAAiB,CAAC,IAAI,CAACkN,SAAS,CAAC;QACpD,IAAIwC,YAAY,CAAC7E,YAAY,KAAK,MAAM,IAAK6E,YAAY,CAACxD,IAAI,IAAI,IAAK,EAAE;UACvEpM,MAAM,GAAG,IAAIuL,SAAS,CAACqE,YAAY,CAAC;QACtC,CAAC,MAAM,IAAIA,YAAY,CAAC7E,YAAY,KAAK,WAAW,EAAE;UACpD/K,MAAM,GAAG,IAAI8M,cAAc,CAAC8C,YAAY,CAAC;QAC3C,CAAC,MAAM,IAAIA,YAAY,CAAC7E,YAAY,KAAK,OAAO,IAAK6E,YAAY,CAAClP,GAAG,IAAI,IAAK,EAAE;UAC9EV,MAAM,GAAG,IAAIgN,UAAU,CAAC4C,YAAY,CAAC;QACvC,CAAC,MAAM;UACL5P,MAAM,GAAG,IAAI6K,WAAK,CAAC+E,YAAY,CAAC;QAClC;MACF,CAAC,MAAM,IAAIrS,gCAAQ,CAAC,IAAI,CAAC6P,SAAS,CAAC,EAAE;QACnC,IAAI,WAAW,CAAC5L,IAAI,CAAC,IAAI,CAAC4L,SAAS,CAAC,EAAE;UACpCpN,MAAM,GAAG,IAAIgN,UAAU,CAAC,IAAI,CAACI,SAAS,CAAC7X,MAAM,CAAC,CAAC,CAAC,CAAC;QACnD,CAAC,MAAM;UACLyK,MAAM,GAAG,IAAI,CAACoN,SAAS;QACzB;MACF,CAAC,MAAM;QACLpN,MAAM,GAAG,EAAE;MACb;MACA,OAAOA,MAAM,CAAC1J,QAAQ,CAAC,CAAC;IAC1B;EAAC;IAAAD,GAAA;IAAAZ,KAAA,EAED,SAAA4W,UAAiBwD,KAAK,EAAE;MACtB,OAAQ,IAAItE,SAAS,CAACsE,KAAK,CAAC,CAAElD,mBAAmB,CAAC,CAAC;IACrD;EAAC;AAAA,EApCsBM,gBAAK;AAAA,IAuCxB6C,0BAAe,0BAAAC,OAAA;EAAA,SAAAD,gBAAA;IAAAlK,yBAAA,OAAAkK,eAAA;IAAA,OAAA5H,oBAAA,OAAA4H,eAAA,EAAApY,SAAA;EAAA;EAAAyQ,mBAAA,CAAA2H,eAAA,EAAAC,OAAA;EAAA,OAAAhK,sBAAA,CAAA+J,eAAA;IAAAzZ,GAAA;IAAAZ,KAAA,EACnB,SAAAuQ,UAAA,EAAY;MACV,OAAON,UAAU,CAACI,SAAS,CAAA0I,IAAA,CAAAC,yBAAA,CAAAqB,eAAA,CAAAvW,SAAA,sBAAA2F,IAAA,MAAkB,CAAC;IAChD;EAAC;AAAA,EAH2B+N,gBAAK;;;;;;;;;;;;;;;;;;;;;;ACzUG;AACF;AACQ;AACP;AAgBrB;AASM;;AAEtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+C,aAAaA,CAAChW,MAAM,EAAc;EAAA,SAAAsE,IAAA,GAAA5G,SAAA,CAAAhD,MAAA,EAAT6J,OAAO,OAAAC,KAAA,CAAAF,IAAA,OAAAA,IAAA,WAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;IAAPF,OAAO,CAAAE,IAAA,QAAA/G,SAAA,CAAA+G,IAAA;EAAA;EACvCF,OAAO,CAACpI,OAAO,CAAC,UAAAyI,MAAM,EAAI;IACxB5B,MAAM,CAACF,IAAI,CAAC8B,MAAM,CAAC,CAACzI,OAAO,CAAC,UAAAE,GAAG,EAAI;MACjC,IAAIuI,MAAM,CAACvI,GAAG,CAAC,IAAI,IAAI,EAAE;QACvB2D,MAAM,CAAC3D,GAAG,CAAC,GAAGuI,MAAM,CAACvI,GAAG,CAAC;MAC3B;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAO2D,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AAJA,IAMMiW,iCAAkB;EACtB;AACF;AACA;AACA;AACA;EACE,SAAAA,mBAAYtX,OAAO,EAAE;IAAAiN,6BAAA,OAAAqK,kBAAA;IACnB;IACA;IACA,IAAI/J,MAAM,EAAEgK,KAAK;IACjBhK,MAAM,GAAG,KAAK,CAAC;IACfgK,KAAK,GAAG,CAAC,CAAC;IACV;AACJ;AACA;AACA;AACA;IACI,IAAI,CAAC7F,SAAS,GAAG,UAAU8F,SAAS,EAAE;MACpC,IAAIC,GAAG,GAAG,CAAC,CAAC;MACZ,IAAGD,SAAS,IAAI,IAAI,EAAE;QACpBA,SAAS,GAAG,IAAI;MAClB;MACAnT,MAAM,CAACF,IAAI,CAACoT,KAAK,CAAC,CAAC/Z,OAAO,CAAC,UAAAE,GAAG;QAAA,OAAI+Z,GAAG,CAAC/Z,GAAG,CAAC,GAAG6Z,KAAK,CAAC7Z,GAAG,CAAC,CAAC+W,SAAS;MAAA,EAAC;MAClE4C,aAAa,CAACI,GAAG,EAAE,IAAI,CAACC,YAAY,CAAC;MACrC,IAAIF,SAAS,IAAI,CAAClQ,OAAO,CAAC,IAAI,CAACqQ,OAAO,CAAC,EAAE;QACvC,IAAIjT,IAAI,GAAG,IAAI,CAACiT,OAAO,CAACra,GAAG,CAAC,UAAAsa,EAAE;UAAA,OAAIA,EAAE,CAAClG,SAAS,CAAC,CAAC;QAAA,EAAC;QACjDhN,IAAI,CAACyF,IAAI,CAACsN,GAAG,CAAC;QACdA,GAAG,GAAG,CAAC,CAAC;QACRJ,aAAa,CAACI,GAAG,EAAE,IAAI,CAACC,YAAY,CAAC;QACrCD,GAAG,CAACrV,cAAc,GAAGsC,IAAI;MAC3B;MACA,OAAO+S,GAAG;IACZ,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACjK,SAAS,GAAG,UAAUqK,MAAM,EAAE;MACjCtK,MAAM,GAAGsK,MAAM;MACf,IAAIA,MAAM,IAAI,IAAI,EAAE;QAClB,IAAI,CAACC,WAAW,CAAC,OAAOD,MAAM,CAACnG,SAAS,KAAK,UAAU,GAAGmG,MAAM,CAACnG,SAAS,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;MACxF;MACA,OAAO,IAAI;IACb,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;IACI,IAAI,CAACpE,SAAS,GAAG,YAAY;MAC3B,OAAOC,MAAM;IACf,CAAC;;IAED;IACA;IACA;;IAEA;IACA,IAAI,CAACyH,KAAK,GAAG,UAAUlY,KAAK,EAAEsM,IAAI,EAAE2O,IAAI,EAAEC,YAAY,EAAEjH,OAAO,EAAE;MAC/D,IAAIA,OAAO,IAAI,IAAI,EAAE;QACnB,IAAIzK,oCAAU,CAAC0R,YAAY,CAAC,EAAE;UAC5BjH,OAAO,GAAGiH,YAAY;QACxB,CAAC,MAAM;UACLjH,OAAO,GAAGyD,gCAAQ;QACpB;MACF;MACA+C,KAAK,CAACnO,IAAI,CAAC,GAAG,IAAIkL,gBAAK,CAAClL,IAAI,EAAE2O,IAAI,EAAEhH,OAAO,CAAC,CAACb,GAAG,CAACpT,KAAK,CAAC;MACvD,OAAO,IAAI;IACb,CAAC;IACD;IACA,IAAI,CAACmb,QAAQ,GAAG,UAAUnb,KAAK,EAAEsM,IAAI,EAAE2O,IAAI,EAAEC,YAAY,EAAEjH,OAAO,EAAE;MAClEA,OAAO,GAAGmH,eAAe,CAACnZ,SAAS,CAAC;MACpCwY,KAAK,CAACnO,IAAI,CAAC,GAAG,IAAIyN,mBAAQ,CAACzN,IAAI,EAAE2O,IAAI,EAAEhH,OAAO,CAAC,CAACb,GAAG,CAACpT,KAAK,CAAC;MAC1D,OAAO,IAAI;IACb,CAAC;IACD;IACA,IAAI,CAACqb,UAAU,GAAG,UAAUrb,KAAK,EAAEsM,IAAI,EAAE2O,IAAI,EAAEC,YAAY,EAAEjH,OAAO,EAAE;MACpEA,OAAO,GAAGmH,eAAe,CAACnZ,SAAS,CAAC;MACpCwY,KAAK,CAACnO,IAAI,CAAC,GAAG,IAAIoN,qBAAU,CAACpN,IAAI,EAAE2O,IAAI,EAAEhH,OAAO,CAAC,CAACb,GAAG,CAACpT,KAAK,CAAC;MAC5D,OAAO,IAAI;IACb,CAAC;IACD;IACA,IAAI,CAACsb,UAAU,GAAG,UAAUtb,KAAK,EAAEsM,IAAI,EAAE2O,IAAI,EAAqD;MAAA,IAAnDvC,GAAG,GAAAzW,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,GAAG;MAAA,IAAEiZ,YAAY,GAAAjZ,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;MAAA,IAAEgS,OAAO,GAAAhS,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGC,SAAS;MAC9F+R,OAAO,GAAGmH,eAAe,CAACnZ,SAAS,CAAC;MACpCwY,KAAK,CAACnO,IAAI,CAAC,GAAG,IAAIkM,qBAAU,CAAClM,IAAI,EAAE2O,IAAI,EAAEvC,GAAG,EAAEzE,OAAO,CAAC,CAACb,GAAG,CAACpT,KAAK,CAAC;MACjE,OAAO,IAAI;IACb,CAAC;IACD;IACA,IAAI,CAACub,mBAAmB,GAAG,UAAUvb,KAAK,EAAEsM,IAAI,EAAE2O,IAAI,EAA4D;MAAA,IAA1DvC,GAAG,GAAAzW,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,GAAG;MAAA,IAAEiZ,YAAY,GAAAjZ,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGC,SAAS;MAAA,IAAE+R,OAAO,GAAAhS,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGC,SAAS;MAC9G+R,OAAO,GAAGmH,eAAe,CAACnZ,SAAS,CAAC;MACpCwY,KAAK,CAACnO,IAAI,CAAC,GAAG,IAAI2M,8BAAmB,CAAC3M,IAAI,EAAE2O,IAAI,EAAEvC,GAAG,EAAEzE,OAAO,CAAC,CAACb,GAAG,CAACpT,KAAK,CAAC;MAC1E,OAAO,IAAI;IACb,CAAC;IACD,IAAI,CAACwb,UAAU,GAAG,UAAUxb,KAAK,EAAEsM,IAAI,EAAE2O,IAAI,EAAE;MAC7CR,KAAK,CAACnO,IAAI,CAAC,GAAG,IAAI2N,qBAAU,CAAC3N,IAAI,EAAE2O,IAAI,CAAC,CAAC7H,GAAG,CAACpT,KAAK,CAAC;MACnD,OAAO,IAAI;IACb,CAAC;;IAED;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACyb,QAAQ,GAAG,UAAUnP,IAAI,EAAE;MAC9B,IAAItM,KAAK,GAAGya,KAAK,CAACnO,IAAI,CAAC,IAAImO,KAAK,CAACnO,IAAI,CAAC,CAACtM,KAAK,CAAC,CAAC;MAC9C,OAAOA,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAG,IAAI,CAAC4a,YAAY,CAACtO,IAAI,CAAC;IACxD,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;IACI,IAAI,CAAC+G,GAAG,GAAG,UAAU/G,IAAI,EAAE;MACzB,OAAOmO,KAAK,CAACnO,IAAI,CAAC;IACpB,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACoP,MAAM,GAAG,UAAUpP,IAAI,EAAE;MAC5B,IAAIqP,IAAI;MACR,QAAQ,KAAK;QACX,KAAKlB,KAAK,CAACnO,IAAI,CAAC,IAAI,IAAI;UACtBqP,IAAI,GAAGlB,KAAK,CAACnO,IAAI,CAAC;UAClB,OAAOmO,KAAK,CAACnO,IAAI,CAAC;UAClB,OAAOqP,IAAI,CAAChE,SAAS;QACvB,KAAK,IAAI,CAACiD,YAAY,CAACtO,IAAI,CAAC,IAAI,IAAI;UAClCqP,IAAI,GAAG,IAAI,CAACf,YAAY,CAACtO,IAAI,CAAC;UAC9B,OAAO,IAAI,CAACsO,YAAY,CAACtO,IAAI,CAAC;UAC9B,OAAOqP,IAAI;QACb;UACE,OAAO,IAAI;MACf;IACF,CAAC;IACD;AACJ;AACA;AACA;IACI,IAAI,CAACtU,IAAI,GAAG,YAAY;MACtB,IAAIzG,GAAG;MACP,OAAS,YAAY;QACnB,IAAIwM,OAAO;QACXA,OAAO,GAAG,EAAE;QACZ,KAAKxM,GAAG,IAAI6Z,KAAK,EAAE;UACjB,IAAI7Z,GAAG,IAAI,IAAI,EAAE;YACfwM,OAAO,CAACC,IAAI,CAACzM,GAAG,CAACgB,KAAK,CAACga,WAAW,CAAC,GAAGhb,GAAG,GAAGwJ,SAAS,CAACxJ,GAAG,CAAC,CAAC;UAC7D;QACF;QACA,OAAOwM,OAAO;MAChB,CAAC,CAAE,CAAC,CAAEyO,IAAI,CAAC,CAAC;IACd,CAAC;IACD;AACJ;AACA;AACA;AACA;IACI,IAAI,CAACC,aAAa,GAAG,YAAY;MAC/B,IAAIC,IAAI,EAAEnb,GAAG,EAAEgH,IAAI;MACnBmU,IAAI,GAAG,CAAC,CAAC;MACT,KAAKnb,GAAG,IAAI6Z,KAAK,EAAE;QACjBsB,IAAI,CAACnb,GAAG,CAAC,GAAG6Z,KAAK,CAAC7Z,GAAG,CAAC,CAACZ,KAAK,CAAC,CAAC;QAC9B,IAAI2U,0CAAa,CAACoH,IAAI,CAACnb,GAAG,CAAC,CAAC,EAAE;UAC5Bmb,IAAI,CAACnb,GAAG,CAAC,GAAGmS,kCAAS,CAACgJ,IAAI,CAACnb,GAAG,CAAC,CAAC;QAClC;MACF;MACA,IAAI,CAAC4J,OAAO,CAAC,IAAI,CAACqQ,OAAO,CAAC,EAAE;QAC1BjT,IAAI,GAAG,IAAI,CAACiT,OAAO,CAACra,GAAG,CAAC,UAAAsa,EAAE;UAAA,OAAIA,EAAE,CAACgB,aAAa,CAAC,CAAC;QAAA,EAAC;QACjDlU,IAAI,CAACyF,IAAI,CAAC0O,IAAI,CAAC;QACfA,IAAI,GAAG;UACLzW,cAAc,EAAEsC;QAClB,CAAC;MACH;MACA,OAAOmU,IAAI;IACb,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACC,KAAK,GAAG,YAAY;MACvB,IAAIC,KAAK,EAAEnB,EAAE;MACbmB,KAAK,GAAG1U,MAAM,CAAC2U,mBAAmB,CAACzB,KAAK,CAAC;MACzC,IAAIwB,KAAK,CAAChd,MAAM,KAAK,CAAC,EAAE;QACtB6b,EAAE,GAAG,IAAI,IAAI,CAACjF,WAAW,CAAC,IAAI,CAACjB,SAAS,CAAC,KAAK,CAAC,CAAC;QAChD,IAAI,CAACuH,oBAAoB,CAAC,CAAC;QAC3B,IAAI,CAACtB,OAAO,CAACxN,IAAI,CAACyN,EAAE,CAAC;MACvB;MACA,OAAO,IAAI;IACb,CAAC;IACD,IAAI,CAACqB,oBAAoB,GAAG,YAAY;MACtC1B,KAAK,GAAG,CAAC,CAAC;MACV,OAAO,IAAI;IACb,CAAC;IACD,IAAI,CAACG,YAAY,GAAG,CAAC,CAAC;IACtB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB,IAAI,CAACG,WAAW,CAAC9X,OAAO,CAAC;EAC3B;;EAEA;AACF;AACA;AACA;AACA;EAJE,OAAAoN,0BAAA,CAAAkK,kBAAA;IAAA5Z,GAAA;IAAAZ,KAAA,EAKA,SAAAgb,YAAA,EAA0B;MAAA,IAAd9X,OAAO,GAAAjB,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;MACtB,IAAIiB,OAAO,YAAYsX,kBAAkB,EAAE;QACzC,IAAI,CAAC4B,kBAAkB,CAAClZ,OAAO,CAAC;MAClC,CAAC,MAAM;QACL,IAAI4E,gCAAQ,CAAC5E,OAAO,CAAC,IAAI2U,8BAAO,CAAC3U,OAAO,CAAC,EAAE;UACzCA,OAAO,GAAG;YACRoC,cAAc,EAAEpC;UAClB,CAAC;QACH;QACAA,OAAO,GAAG6P,kCAAS,CAAC7P,OAAO,EAAE,UAAUlD,KAAK,EAAE;UAC5C,IAAIA,KAAK,YAAYwa,kBAAkB,IAAIxa,KAAK,YAAYoV,KAAK,EAAE;YACjE,OAAO,IAAIpV,KAAK,CAAC4V,KAAK,CAAC,CAAC;UAC1B;QACF,CAAC,CAAC;QACF;QACA,IAAI1S,OAAO,CAAC,IAAI,CAAC,EAAE;UACjB,IAAI,CAACkQ,GAAG,CAAC,IAAI,EAAElQ,OAAO,CAAC,IAAI,CAAC,CAAC;UAC7B,OAAOA,OAAO,CAAC,IAAI,CAAC;QACtB;QACA,KAAK,IAAItC,GAAG,IAAIsC,OAAO,EAAE;UACvB,IAAIyX,GAAG,GAAGzX,OAAO,CAACtC,GAAG,CAAC;UACtB,IAAG+Z,GAAG,IAAI,IAAI,EAAE;YACd,IAAI/Z,GAAG,CAACgB,KAAK,CAACga,WAAW,CAAC,EAAE;cAC1B,IAAIhb,GAAG,KAAK,OAAO,EAAE;gBACnB,IAAI,CAACwS,GAAG,CAAC,UAAU,EAAExS,GAAG,EAAE+Z,GAAG,CAAC;cAChC;YACF,CAAC,MAAM;cACL,IAAI,CAACvH,GAAG,CAACxS,GAAG,EAAE+Z,GAAG,CAAC;YACpB;UACF;QACF;MACF;MACA,OAAO,IAAI;IACb;EAAC;IAAA/Z,GAAA;IAAAZ,KAAA,EAED,SAAAoc,mBAAmBC,KAAK,EAAE;MAAA,IAAAzI,KAAA;MACxB,IAAIyI,KAAK,YAAY7B,kBAAkB,EAAE;QACvC6B,KAAK,CAAChV,IAAI,CAAC,CAAC,CAAC3G,OAAO,CAAC,UAAAE,GAAG;UAAA,OACtBgT,KAAI,CAACR,GAAG,CAACxS,GAAG,EAAEyb,KAAK,CAAChJ,GAAG,CAACzS,GAAG,CAAC,CAAC+W,SAAS,CAAC;QAAA,CACzC,CAAC;MACH;MACA,OAAO,IAAI;IACb;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;EANE;IAAA/W,GAAA;IAAAZ,KAAA,EAOA,SAAAoT,IAAIxS,GAAG,EAAa;MAClB,IAAI0b,QAAQ;MACZA,QAAQ,GAAGxS,SAAS,CAAClJ,GAAG,CAAC;MAAC,SAAA2b,KAAA,GAAAta,SAAA,CAAAhD,MAAA,EAFhBud,MAAM,OAAAzT,KAAA,CAAAwT,KAAA,OAAAA,KAAA,WAAAE,KAAA,MAAAA,KAAA,GAAAF,KAAA,EAAAE,KAAA;QAAND,MAAM,CAAAC,KAAA,QAAAxa,SAAA,CAAAwa,KAAA;MAAA;MAGhB,IAAIhV,gCAAQ,CAAC6R,6BAAc,CAACoD,OAAO,EAAEJ,QAAQ,CAAC,EAAE;QAC9C,IAAI,CAACA,QAAQ,CAAC,CAACK,KAAK,CAAC,IAAI,EAAEH,MAAM,CAAC;MACpC,CAAC,MAAM;QACL,IAAI,CAAC5B,YAAY,CAACha,GAAG,CAAC,GAAG4b,MAAM,CAAC,CAAC,CAAC;MACpC;MACA,OAAO,IAAI;IACb;EAAC;IAAA5b,GAAA;IAAAZ,KAAA,EAED,SAAA4c,SAAA,EAAW;MACT,OAAO,IAAI,CAACnB,QAAQ,CAAC,SAAS,CAAC,IAAI,IAAI,CAACA,QAAQ,CAAC,UAAU,CAAC;IAC9D;;IAEA;AACF;AACA;AACA;AACA;EAJE;IAAA7a,GAAA;IAAAZ,KAAA,EAKA,SAAAuQ,UAAA,EAAY;MACV,IAAIsM,OAAO,EAAEC,CAAC,EAAErN,GAAG,EAAEsN,SAAS,EAAE1H,GAAG,EAAE2H,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,WAAW,EAAEvE,CAAC,EAAEwE,kBAAkB,EAC7FC,oBAAoB,EAAExX,eAAe,EAAE9F,KAAK,EAAE4G,SAAS,EAAE2W,IAAI;MAC/DH,WAAW,GAAG,IAAI,CAACvC,OAAO,CAACra,GAAG,CAAC,UAAAsa,EAAE;QAAA,OAAIA,EAAE,CAACvK,SAAS,CAAC,CAAC;MAAA,EAAC;MACpDwM,SAAS,GAAG,IAAI,CAAC1V,IAAI,CAAC,CAAC;MACvBvB,eAAe,GAAG,CAACuP,GAAG,GAAG,IAAI,CAAChC,GAAG,CAAC,gBAAgB,CAAC,KAAK,IAAI,GAAGgC,GAAG,CAAC9E,SAAS,CAAC,CAAC,GAAG,KAAK,CAAC;MACvFsM,OAAO,GAAG,CAACG,IAAI,GAAG,IAAI,CAAC3J,GAAG,CAAC,IAAI,CAAC,KAAK,IAAI,GAAG2J,IAAI,CAACzM,SAAS,CAAC,CAAC,GAAG,KAAK,CAAC;MACrE3J,SAAS,GAAG4W,UAAU,CAAC,CAACP,IAAI,GAAG,IAAI,CAAC5J,GAAG,CAAC,WAAW,CAAC,KAAK,IAAI,GAAG4J,IAAI,CAACjd,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;MACtF+c,SAAS,GAAGU,oCAAU,CAACV,SAAS,EAAE,CAAC,gBAAgB,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;MACxEQ,IAAI,GAAG,EAAE;MACTF,kBAAkB,GAAG,EAAE;MACvB,KAAKP,CAAC,GAAG,CAAC,EAAErN,GAAG,GAAGsN,SAAS,CAAC9d,MAAM,EAAE6d,CAAC,GAAGrN,GAAG,EAAEqN,CAAC,EAAE,EAAE;QAChDjE,CAAC,GAAGkE,SAAS,CAACD,CAAC,CAAC;QAChB,IAAIjE,CAAC,CAACjX,KAAK,CAACga,WAAW,CAAC,EAAE;UACxB2B,IAAI,CAAClQ,IAAI,CAACwL,CAAC,GAAG,GAAG,GAAG5I,UAAU,CAACI,SAAS,CAAC,CAAC6M,IAAI,GAAG,IAAI,CAAC7J,GAAG,CAACwF,CAAC,CAAC,KAAK,IAAI,GAAGqE,IAAI,CAACld,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QACjG,CAAC,MAAM;UACLqd,kBAAkB,CAAChQ,IAAI,CAAC,CAAC8P,IAAI,GAAG,IAAI,CAAC9J,GAAG,CAACwF,CAAC,CAAC,KAAK,IAAI,GAAGsE,IAAI,CAAC5M,SAAS,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QACnF;MACF;MACA,QAAQ,KAAK;QACX,KAAK,CAACzI,gCAAQ,CAAChC,eAAe,CAAC;UAC7BuX,kBAAkB,CAAChQ,IAAI,CAACvH,eAAe,CAAC;UACxC;QACF,KAAK,CAAC+R,8BAAO,CAAC/R,eAAe,CAAC;UAC5BsX,WAAW,GAAGA,WAAW,CAACta,MAAM,CAACgD,eAAe,CAAC;MACrD;MACAuX,kBAAkB,GAAI,YAAY;QAChC,IAAI7I,CAAC,EAAEkJ,IAAI,EAAEtQ,OAAO;QACpBA,OAAO,GAAG,EAAE;QACZ,KAAKoH,CAAC,GAAG,CAAC,EAAEkJ,IAAI,GAAGL,kBAAkB,CAACpe,MAAM,EAAEuV,CAAC,GAAGkJ,IAAI,EAAElJ,CAAC,EAAE,EAAE;UAC3DxU,KAAK,GAAGqd,kBAAkB,CAAC7I,CAAC,CAAC;UAC7B,IAAIqD,8BAAO,CAAC7X,KAAK,CAAC,IAAI,CAACwK,OAAO,CAACxK,KAAK,CAAC,IAAI,CAAC6X,8BAAO,CAAC7X,KAAK,CAAC,IAAIA,KAAK,EAAE;YACjEoN,OAAO,CAACC,IAAI,CAACrN,KAAK,CAAC;UACrB;QACF;QACA,OAAOoN,OAAO;MAChB,CAAC,CAAE,CAAC;MACJiQ,kBAAkB,GAAGE,IAAI,CAAC1B,IAAI,CAAC,CAAC,CAAC/Y,MAAM,CAAC8D,SAAS,CAAC,CAAC9D,MAAM,CAACua,kBAAkB,CAACxB,IAAI,CAAC,CAAC,CAAC;MACpF,IAAIgB,OAAO,KAAK,QAAQ,EAAE;QACxBQ,kBAAkB,CAAChQ,IAAI,CAACwP,OAAO,CAAC;MAClC,CAAC,MAAM,IAAI,CAACrS,OAAO,CAACqS,OAAO,CAAC,EAAE;QAC5BQ,kBAAkB,CAACjG,OAAO,CAACyF,OAAO,CAAC;MACrC;MACAS,oBAAoB,GAAG3H,8BAAO,CAAC0H,kBAAkB,CAAC,CAACjc,IAAI,CAAC,IAAI,CAACuc,eAAe,CAAC;MAC7E,IAAI,CAACnT,OAAO,CAAC8S,oBAAoB,CAAC,EAAE;QAClCF,WAAW,CAAC/P,IAAI,CAACiQ,oBAAoB,CAAC;MACxC;MACA,OAAO3H,8BAAO,CAACyH,WAAW,CAAC,CAAChc,IAAI,CAAC,IAAI,CAACwc,eAAe,CAAC;IACxD;;IAEA;AACF;AACA;AACA;AACA;AACA;EALE;IAAAhd,GAAA;IAAAZ,KAAA;IAUA;AACF;AACA;AACA;AACA;IACE,SAAA6d,iBAAA,EAAmB;MAAA,IAAA/E,MAAA;MACjB,IAAIgF,QAAQ,EAAEpX,MAAM,EAAExD,OAAO,EAAE+Z,IAAI,EAAEC,IAAI,EAAEld,KAAK,EAAEsG,KAAK;MACvDpD,OAAO,GAAG,CAAC,CAAC;MACZ,IAAI6a,YAAY;MAChBxW,MAAM,CAACF,IAAI,CAAC,IAAI,CAACuT,YAAY,CAAC,CAACla,OAAO,CAAC,UAAAE,GAAG,EAAE;QAC1CZ,KAAK,GAAG8Y,MAAI,CAAC8B,YAAY,CAACha,GAAG,CAAC;QAC9Bmd,YAAY,GAAG3T,SAAS,CAACxJ,GAAG,CAAC;QAC7B,IAAI,CAAC6G,gCAAQ,CAAC6R,6BAAc,CAAC0E,WAAW,EAAED,YAAY,CAAC,IAAI,CAACtW,gCAAQ,CAACP,QAAQ,EAAE6W,YAAY,CAAC,EAAE;UAC5FD,QAAQ,GAAG,QAAQ,CAAC/R,IAAI,CAACnL,GAAG,CAAC,GAAGA,GAAG,CAACvB,KAAK,CAAC,CAAC,CAAC,GAAGuB,GAAG;UAClDsC,OAAO,CAAC4a,QAAQ,CAAC,GAAG9d,KAAK;QAC3B;MACF,CAAC,CAAC;MACF;MACA,IAAI,CAACqH,IAAI,CAAC,CAAC,CAAC3G,OAAO,CAAC,UAAAE,GAAG,EAAI;QACzB,IAAI,QAAQ,CAACmL,IAAI,CAACnL,GAAG,CAAC,EAAE;UACtBsC,OAAO,CAAC4G,SAAS,CAAClJ,GAAG,CAACvB,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGyZ,MAAI,CAAC2C,QAAQ,CAAC7a,GAAG,CAAC;QACvD;MACF,CAAC,CAAC;MACF,IAAI,EAAE,IAAI,CAACgc,QAAQ,CAAC,CAAC,IAAI,IAAI,CAACnB,QAAQ,CAAC,OAAO,CAAC,IAAIhU,gCAAQ,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,IAAI,CAACgU,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;QAC9GnV,KAAK,GAAG,CAAC2W,IAAI,GAAG,IAAI,CAAC5J,GAAG,CAAC,OAAO,CAAC,KAAK,IAAI,GAAG4J,IAAI,CAACtF,SAAS,GAAG,KAAK,CAAC;QACpEjR,MAAM,GAAG,CAACwW,IAAI,GAAG,IAAI,CAAC7J,GAAG,CAAC,QAAQ,CAAC,KAAK,IAAI,GAAG6J,IAAI,CAACvF,SAAS,GAAG,KAAK,CAAC;QACtE,IAAItP,UAAU,CAAC/B,KAAK,CAAC,IAAI,GAAG,EAAE;UAC5B,IAAIpD,OAAO,CAACoD,KAAK,IAAI,IAAI,EAAE;YACzBpD,OAAO,CAACoD,KAAK,GAAGA,KAAK;UACvB;QACF;QACA,IAAI+B,UAAU,CAAC3B,MAAM,CAAC,IAAI,GAAG,EAAE;UAC7B,IAAIxD,OAAO,CAACwD,MAAM,IAAI,IAAI,EAAE;YAC1BxD,OAAO,CAACwD,MAAM,GAAGA,MAAM;UACzB;QACF;MACF;MACA,OAAOxD,OAAO;IAChB;EAAC;IAAAtC,GAAA;IAAAZ,KAAA;IAMD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACE,SAAAie,OAAA,EAAS;MACP,IAAI5I,GAAG;MACP,OAAO,CAACA,GAAG,GAAG,IAAI,CAAC7E,SAAS,CAAC,CAAC,KAAK,IAAI,GAAG,OAAO6E,GAAG,CAAC4I,MAAM,KAAK,UAAU,GAAG5I,GAAG,CAAC4I,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC;IAC7G;EAAC;IAAArd,GAAA;IAAAZ,KAAA,EAED,SAAAa,SAAA,EAAW;MACT,OAAO,IAAI,CAAC0P,SAAS,CAAC,CAAC;IACzB;EAAC;IAAA3P,GAAA;IAAAZ,KAAA,EAED,SAAA4V,MAAA,EAAQ;MACN,OAAO,IAAI,IAAI,CAACC,WAAW,CAAC,IAAI,CAACjB,SAAS,CAAC,IAAI,CAAC,CAAC;IACnD;EAAC;IAAAhU,GAAA;IAAAZ,KAAA,EAvED,SAAAke,UAAA,EAAmB;MACjB,OAAO5E,6BAAc,CAACoD,OAAO;IAC/B;EAAC;IAAA9b,GAAA;IAAAZ,KAAA,EA0CD,SAAAme,iBAAwB7R,IAAI,EAAE;MAC5B,OAAOgN,6BAAc,CAACoD,OAAO,CAAC0B,OAAO,CAACtU,SAAS,CAACwC,IAAI,CAAC,CAAC,IAAI,CAAC;IAC7D;EAAC;AAAA;AA4BH,IAAMsP,WAAW,GAAG,kBAAkB;AAEtCpB,iCAAkB,CAAC1W,SAAS,CAAC8Z,eAAe,GAAG,GAAG;AAElDpD,iCAAkB,CAAC1W,SAAS,CAAC6Z,eAAe,GAAG,GAAG;AAGlD,SAASvC,eAAeA,CAACiD,IAAI,EAAE;EAC7B,IAAIC,QAAQ;EACZA,QAAQ,GAAGD,IAAI,IAAI,IAAI,GAAGA,IAAI,CAACA,IAAI,CAACpf,MAAM,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;EACxD,IAAIuK,oCAAU,CAAC8U,QAAQ,CAAC,EAAE;IACxB,OAAOA,QAAQ;EACjB,CAAC,MAAM;IACL,OAAO,KAAK,CAAC;EACf;AACF;AAEA,SAASd,UAAUA,CAACe,QAAQ,EAAE;EAC5B,IAAIzB,CAAC,EAAErN,GAAG,EAAEnD,IAAI,EAAEc,OAAO,EAAElF,CAAC;EAC5B,IAAI2P,8BAAO,CAAC0G,QAAQ,CAAC,EAAE;IACrBnR,OAAO,GAAG,EAAE;IACZ,KAAK0P,CAAC,GAAG,CAAC,EAAErN,GAAG,GAAG8O,QAAQ,CAACtf,MAAM,EAAE6d,CAAC,GAAGrN,GAAG,EAAEqN,CAAC,EAAE,EAAE;MAAA,IAAA0B,WAAA,GAAAjK,4BAAA,CACnCgK,QAAQ,CAACzB,CAAC,CAAC;MAAtBxQ,IAAI,GAAAkS,WAAA;MAAEtW,CAAC,GAAAsW,WAAA;MACRpR,OAAO,CAACC,IAAI,IAAAvK,MAAA,CAAIwJ,IAAI,OAAAxJ,MAAA,CAAImN,UAAU,CAACI,SAAS,CAACnI,CAAC,CAAC,CAAE,CAAC;IACpD;IACA,OAAOkF,OAAO;EAChB,CAAC,MAAM;IACL,OAAOmR,QAAQ;EACjB;AACF;AAEA,SAASE,qBAAqBA,CAAAC,IAAA,EAA0B;EAAA,IAAxBC,aAAa,GAAAD,IAAA,CAAbC,aAAa;IAAExV,MAAM,GAAAuV,IAAA,CAANvV,MAAM;EACnD,IAAIwV,aAAa,KAAK,QAAQ,EAAE;IAC9B,OAAO,CAACA,aAAa,EAAE/T,IAAI,CAACzB,MAAM,CAAC,CAAC,CAAC/H,IAAI,CAAC,GAAG,CAAC;EAChD,CAAC,MAAM,IAAIud,aAAa,KAAK,MAAM,EAAE;IACnC,OAAO,CAACA,aAAa,EAAExV,MAAM,CAAC,CAAC/H,IAAI,CAAC,GAAG,CAAC;EAC1C;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,IASMkY,6BAAc,0BAAAsF,mBAAA;EAClB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAAtF,eAAYpW,OAAO,EAAE;IAAAiN,6BAAA,OAAAmJ,cAAA;IAAA,OAAA7G,wBAAA,OAAA6G,cAAA,GACbpW,OAAO;EACf;;EAEA;AACF;AACA;AACA;AACA;AACA;EALEwP,uBAAA,CAAA4G,cAAA,EAAAsF,mBAAA;EAAA,OAAAtO,0BAAA,CAAAgJ,cAAA;IAAA1Y,GAAA;IAAAZ,KAAA;IAUA;AACF;AACA;IACE,SAAA6e,MAAM7e,KAAK,EAAE;MACX,OAAO,IAAI,CAACsb,UAAU,CAACtb,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,EAAEiQ,UAAU,CAACI,SAAS,CAAC;IACxE;EAAC;IAAAzP,GAAA;IAAAZ,KAAA,EAED,SAAA8e,WAAW9e,KAAK,EAAE;MAChB,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,aAAa,EAAE,IAAI,CAAC;IAC/C;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAA+e,eAAe/e,KAAK,EAAE;MACpB,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,iBAAiB,EAAE,IAAI,CAAC;IACnD;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAAiR,YAAYjR,KAAK,EAAE;MACjB,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,cAAc,EAAE,IAAI,EAAEiQ,UAAU,CAACI,SAAS,CAAC;IACtE;EAAC;IAAAzP,GAAA;IAAAZ,KAAA,EAED,SAAAyG,WAAWzG,KAAK,EAAE;MAChB,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,YAAY,EAAE,GAAG,EAAEwX,gBAAK,CAACM,UAAU,CAAC;IAC/D;EAAC;IAAAlX,GAAA;IAAAZ,KAAA,EAED,SAAAgf,QAAQhf,KAAK,EAAE;MACb,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC;IAC5C;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAAif,OAAOjf,KAAK,EAAE;MACZ,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,UAAUif,MAAM,EAAE;QACzD,IAAItK,0CAAa,CAACsK,MAAM,CAAC,EAAE;UACzBA,MAAM,GAAGzL,4BAAM,CAAC,CAAC,CAAC,EAAE;YAClB0L,KAAK,EAAE,OAAO;YACd5Y,KAAK,EAAE;UACT,CAAC,EAAE2Y,MAAM,CAAC;UACV,UAAAnc,MAAA,CAAUmc,MAAM,CAAC3Y,KAAK,eAAAxD,MAAA,CAAY0U,gBAAK,CAACM,UAAU,CAACmH,MAAM,CAACC,KAAK,CAAC;QAClE,CAAC,MAAM;UACL,OAAOD,MAAM;QACf;MACF,CAAC,CAAC;IACJ;EAAC;IAAAre,GAAA;IAAAZ,KAAA,EAED,SAAAkf,MAAMlf,KAAK,EAAE;MACX,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,OAAO,EAAE,IAAI,EAAEwX,gBAAK,CAACM,UAAU,CAAC;IAC3D;EAAC;IAAAlX,GAAA;IAAAZ,KAAA,EAED,SAAAmf,WAAWnf,KAAK,EAAE;MAChB,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,aAAa,EAAE,IAAI,CAAC;IAC/C;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAAwG,KAAKxG,KAAK,EAAE;MACV,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC;IACvC;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAAof,eAAepf,KAAK,EAAE;MACpB,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,iBAAiB,EAAE,IAAI,EAAE,YAAM;QACtD,OAAOye,qBAAqB,CAACze,KAAK,CAAC;MACrC,CAAC,CAAC;IACJ;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAAqf,kBAAkBrf,KAAK,EAAE;MACvB,IAAI,IAAI,CAACqT,GAAG,CAAC,iBAAiB,CAAC,EAAE;QAC/B;MACF;MACA,OAAO,IAAI,CAAC8H,QAAQ,CAACnb,KAAK,EAAE,iBAAiB,EAAE,EAAE,EAAE,YAAM;QACvDA,KAAK,GAAGye,qBAAqB,CAACze,KAAK,CAAC;QACpC,OAAOA,KAAK,aAAA8C,MAAA,CAAa9C,KAAK,IAAKA,KAAK;MAC1C,CAAC,CAAC;IACJ;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAAsf,aAAatf,KAAK,EAAE;MAClB,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,eAAe,EAAE,GAAG,CAAC;IAChD;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAAuf,MAAMvf,KAAK,EAAE;MACX,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC;IACzC;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAAwf,QAAQxf,KAAK,EAAE;MACb,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,SAAS,EAAE,IAAI,CAAC;IAC3C;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAA2S,SAAS3S,KAAK,EAAE;MACd,OAAO,IAAI,CAACqb,UAAU,CAACrb,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC;IACjD;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAAyf,IAAIzf,KAAK,EAAE;MACT,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,UAACyf,GAAG,EAAK;QAC9CA,GAAG,GAAGA,GAAG,CAAC5e,QAAQ,CAAC,CAAC;QACpB,IAAI4e,GAAG,IAAI,IAAI,GAAGA,GAAG,CAAC7d,KAAK,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC,EAAE;UAC7C,OAAO6d,GAAG,GAAG,IAAI;QACnB,CAAC,MAAM;UACL,OAAOxP,UAAU,CAACI,SAAS,CAACoP,GAAG,CAAC;QAClC;MACF,CAAC,CAAC;IACJ;EAAC;IAAA7e,GAAA;IAAAZ,KAAA,EAED,SAAAmG,OAAOnG,KAAK,EAAE;MACZ,OAAO,IAAI,CAACsb,UAAU,CAACtb,KAAK,EAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAEiQ,UAAU,CAACI,SAAS,CAAC;IACzE;EAAC;IAAAzP,GAAA;IAAAZ,KAAA,EAED,SAAA0f,MAAA,EAAO;MACL,OAAO,IAAI,MAAG,CAAC,MAAM,CAAC;IACxB;EAAC;IAAA9e,GAAA;IAAAZ,KAAA,EAED,SAAA2f,MAAA,EAAQ;MACN,OAAO,IAAI,MAAG,CAAC,KAAK,CAAC;IACvB;EAAC;IAAA/e,GAAA;IAAAZ,KAAA,EAED,SAAA4f,UAAU5f,KAAK,EAAE;MACf,OAAO,IAAI,CAACqb,UAAU,CAACrb,KAAK,EAAE,YAAY,EAAE,IAAI,CAAC;IACnD;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAA6f,gBAAgB7f,KAAK,EAAE;MACrB,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,kBAAkB,CAAC;IAC9C;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAA8f,YAAY9f,KAAK,EAAE;MACjB,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,cAAc,EAAE,GAAG,CAAC;IAC/C;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAAiF,OAAOjF,KAAK,EAAE;MACZ,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,QAAQ,CAAC;IACpC;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAA+f,MAAM/f,KAAK,EAAE;MACX,OAAO,IAAI,CAACsb,UAAU,CAACtb,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,CAAC;IACnD;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAA2G,QAAQ3G,KAAK,EAAE;MACb,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,SAAS,EAAE,GAAG,CAAC;IAC1C;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAAggB,IAAIhgB,KAAK,EAAE;MACT,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,UAACggB,GAAG,EAAK;QAC9C,IAAIlY,gCAAQ,CAACkY,GAAG,CAAC,EAAE;UACjB,OAAOA,GAAG;QACZ,CAAC,MAAM,IAAInI,8BAAO,CAACmI,GAAG,CAAC,EAAE;UACvB,OAAOA,GAAG,CAAC5e,IAAI,CAAC,GAAG,CAAC;QACtB,CAAC,MAAM;UACL,OAAO4e,GAAG;QACZ;MACF,CAAC,CAAC;IACJ;EAAC;IAAApf,GAAA;IAAAZ,KAAA,EAED,SAAA0G,OAAO1G,KAAK,EAAE;MAAA,IAAAmZ,MAAA;MACZ,OAAO,IAAI,CAACjB,KAAK,CAAClY,KAAK,EAAE,QAAQ,EAAE,GAAG,EAAE,YAAM;QAC5C,IAAImZ,MAAI,CAACsC,QAAQ,CAAC,MAAM,CAAC,IAAItC,MAAI,CAACsC,QAAQ,CAAC,SAAS,CAAC,IAAItC,MAAI,CAACsC,QAAQ,CAAC,UAAU,CAAC,EAAE;UAClF,OAAOxL,UAAU,CAACI,SAAS,CAACrQ,KAAK,CAAC;QACpC,CAAC,MAAM;UACL,OAAO,IAAI;QACb;MACF,CAAC,CAAC;IACJ;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAAigB,WAAWjgB,KAAK,EAAE;MAChB,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,aAAa,CAAC;IACzC;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAAkgB,UAAUlgB,KAAK,EAAE;MACf,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,YAAY,CAAC;IACxC;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAAmgB,IAAA,EAAe;MAAA,IAAZngB,KAAK,GAAAiC,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;MACX,IAAIxC,CAAC,EAAE2gB,KAAK,EAAEtD,CAAC,EAAEzH,GAAG,EAAEgL,IAAI,EAAEC,MAAM;MAClC,QAAQtgB,KAAK;QACX,KAAK,MAAM;UACT,IAAI,CAACgc,KAAK,CAAC,CAAC;UACZ,OAAO,IAAI,CAAC9D,KAAK,CAAClY,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;QACtC,KAAK,KAAK;UACR,IAAI,CAACgc,KAAK,CAAC,CAAC;UACZ,KAAKvc,CAAC,GAAGqd,CAAC,GAAGzH,GAAG,GAAG,IAAI,CAACwF,OAAO,CAAC5b,MAAM,GAAG,CAAC,EAAE6d,CAAC,IAAI,CAAC,EAAErd,CAAC,GAAGqd,CAAC,IAAI,CAAC,CAAC,EAAE;YAC/DsD,KAAK,GAAG,IAAI,CAACvF,OAAO,CAACpb,CAAC,CAAC,CAACgc,QAAQ,CAAC,IAAI,CAAC;YACtC,IAAI2E,KAAK,KAAK,KAAK,EAAE;cACnB;YACF,CAAC,MAAM,IAAIA,KAAK,IAAI,IAAI,EAAE;cACxBC,IAAI,GAAG/G,cAAc,OAAI,CAAC,CAAC,MAAG,CAAC8G,KAAK,CAAC;cACrC,IAAI,CAACvF,OAAO,CAACpb,CAAC,CAAC,CAACic,MAAM,CAAC,IAAI,CAAC;cAC5B4E,MAAM,GAAG,IAAI,CAACzF,OAAO,CAACpb,CAAC,CAAC;cACxB,IAAI,CAACob,OAAO,CAACpb,CAAC,CAAC,GAAG6Z,cAAc,OAAI,CAAC,CAAC,CAAChU,cAAc,CAAC,CAAC+a,IAAI,EAAEC,MAAM,CAAC,CAAC;cACrE,IAAIF,KAAK,KAAK,MAAM,EAAE;gBACpB;cACF;YACF;UACF;UACA,OAAO,IAAI,CAAClI,KAAK,CAAClY,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;QACtC,KAAK,EAAE;UACL,OAAOsS,SAAS,OAAI,CAAC,CAAC,CAAC5B,SAAS,CAAC,IAAI,CAAC;QACxC;UACE,OAAO,IAAI,CAACwH,KAAK,CAAClY,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,UAAUA,KAAK,EAAE;YACpD,OAAOsS,SAAS,OAAI,CAACtS,KAAK,CAAC,CAACa,QAAQ,CAAC,CAAC;UACxC,CAAC,CAAC;MACN;IACF;EAAC;IAAAD,GAAA;IAAAZ,KAAA,EAED,SAAAugB,iBAAiBvgB,KAAK,EAAE;MACtB,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,mBAAmB,EAAE,IAAI,CAAC;IACrD;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAAwgB,IAAIxgB,KAAK,EAAE;MACT,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACxC;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAA6Z,OAAO7Z,KAAK,EAAE;MACZ,IAAIygB,KAAK,EAAEC,OAAO;MAAC,IAAAC,KAAA,GACCnX,oCAAU,CAACxJ,KAAK,IAAI,IAAI,GAAGA,KAAK,CAACgB,KAAK,GAAG,KAAK,CAAC,CAAC,GAAIhB,KAAK,CAACgB,KAAK,CAAC,IAAI,CAAC,GAAG6W,8BAAO,CAAC7X,KAAK,CAAC,GAAGA,KAAK,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;MAAA,IAAA4gB,KAAA,GAAArM,4BAAA,CAAAoM,KAAA;MAAhID,OAAO,GAAAE,KAAA;MAAEH,KAAK,GAAAG,KAAA;MACf,IAAIF,OAAO,IAAI,IAAI,EAAE;QACnB,IAAI,CAACG,WAAW,CAACH,OAAO,CAAC;MAC3B;MACA,IAAID,KAAK,IAAI,IAAI,EAAE;QACjB,OAAO,IAAI,CAACb,SAAS,CAACa,KAAK,CAAC;MAC9B;IACF;EAAC;IAAA7f,GAAA;IAAAZ,KAAA,EAED,SAAA8gB,QAAQ9gB,KAAK,EAAE;MACb,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,SAAS,EAAE,GAAG,EAAEiQ,UAAU,CAACI,SAAS,CAAC;IAChE;EAAC;IAAAzP,GAAA;IAAAZ,KAAA,EAED,SAAA+gB,QAAQ/gB,KAAK,EAAE;MACb,OAAO,IAAI,CAACwb,UAAU,CAACxb,KAAK,EAAE,SAAS,EAAE,GAAG,CAAC;IAC/C;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAAghB,KAAKhhB,KAAK,EAAE;MACV,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC;IACxC;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAAihB,OAAOjhB,KAAK,EAAE;MACZ,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,QAAQ,CAAC;IACpC;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAAkhB,OAAOlhB,KAAK,EAAE;MACZ,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAC;IACzC;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAAoG,QAAQpG,KAAK,EAAE;MACb,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,SAAS,EAAE,GAAG,EAAEiQ,UAAU,CAACI,SAAS,CAAC;IAChE;EAAC;IAAAzP,GAAA;IAAAZ,KAAA,EAED,SAAAmhB,OAAOnhB,KAAK,EAAE;MACZ,OAAO,IAAI,CAACsb,UAAU,CAACtb,KAAK,EAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAEiQ,UAAU,CAACI,SAAS,CAAC;IACzE;EAAC;IAAAzP,GAAA;IAAAZ,KAAA,EAED,SAAAohB,kBAAkBphB,KAAK,EAAE;MACvB,OAAO,IAAI,CAACmb,QAAQ,CAACnb,KAAK,EAAE,oBAAoB,CAAC;IACnD;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAAyL,KAAKzL,KAAK,EAAE;MACV,IAAI0G,MAAM,EAAEJ,KAAK;MACjB,IAAIkD,oCAAU,CAACxJ,KAAK,IAAI,IAAI,GAAGA,KAAK,CAACgB,KAAK,GAAG,KAAK,CAAC,CAAC,EAAE;QAAA,IAAAqT,YAAA,GAClCrU,KAAK,CAACgB,KAAK,CAAC,GAAG,CAAC;QAAA,IAAAsT,aAAA,GAAAC,4BAAA,CAAAF,YAAA;QAAjC/N,KAAK,GAAAgO,aAAA;QAAE5N,MAAM,GAAA4N,aAAA;QACd,IAAI,CAAChO,KAAK,CAACA,KAAK,CAAC;QACjB,OAAO,IAAI,CAACI,MAAM,CAACA,MAAM,CAAC;MAC5B;IACF;EAAC;IAAA9F,GAAA;IAAAZ,KAAA,EAED,SAAAqhB,YAAYrhB,KAAK,EAAE;MACjB,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,cAAc,CAAC;IAC1C;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAAshB,qBAAqBthB,KAAK,EAAE;MAC1B,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,uBAAuB,CAAC;IACnD;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAA6gB,YAAY7gB,KAAK,EAAE;MACjB,OAAO,IAAI,CAACqb,UAAU,CAACrb,KAAK,EAAE,cAAc,EAAE,IAAI,CAAC;IACrD;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAAuhB,iBAAiBvhB,KAAK,EAAE;MACtB,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,mBAAmB,EAAE,IAAI,CAAC;IACrD;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAAsF,eAAetF,KAAK,EAAE;MACpB,OAAO,IAAI,CAACub,mBAAmB,CAACvb,KAAK,EAAE,gBAAgB,EAAE,GAAG,CAAC;IAC/D;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAAwhB,SAASxhB,KAAK,EAAE;MACd,OAAO,IAAI,CAACwb,UAAU,CAACxb,KAAK,EAAE,UAAU,EAAE,GAAG,CAAC;IAChD;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAA6R,SAASvF,IAAI,EAAEtM,KAAK,EAAE;MACpB,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAEsM,IAAI,EAAEA,IAAI,CAAC;IACtC;EAAC;IAAA1L,GAAA;IAAAZ,KAAA,EAED,SAAA4G,UAAU4V,MAAM,EAAE;MAChB,OAAO,IAAI,CAAClB,UAAU,CAACkB,MAAM,EAAE,WAAW,CAAC;IAC7C;EAAC;IAAA5b,GAAA;IAAAZ,KAAA,EAED,SAAAyhB,WAAWzhB,KAAK,EAAE;MAChB,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,aAAa,EAAE,IAAI,EAAEwX,gBAAK,CAACS,oBAAoB,CAAC;IAC3E;EAAC;IAAArX,GAAA;IAAAZ,KAAA,EAED,SAAA0hB,cAAc1hB,KAAK,EAAE;MACnB,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,gBAAgB,EAAE,IAAI,CAAC;IAClD;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAAsG,MAAMtG,KAAK,EAAE;MAAA,IAAAoZ,MAAA;MACX,OAAO,IAAI,CAAClB,KAAK,CAAClY,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,YAAM;QAC3C,IAAIoZ,MAAI,CAACqC,QAAQ,CAAC,MAAM,CAAC,IAAIrC,MAAI,CAACqC,QAAQ,CAAC,SAAS,CAAC,IAAIrC,MAAI,CAACqC,QAAQ,CAAC,UAAU,CAAC,EAAE;UAClF,OAAOxL,UAAU,CAACI,SAAS,CAACrQ,KAAK,CAAC;QACpC,CAAC,MAAM;UACL,OAAO,IAAI;QACb;MACF,CAAC,CAAC;IACJ;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAAJ,EAAEI,KAAK,EAAE;MACP,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,GAAG,EAAE,GAAG,EAAEiQ,UAAU,CAACI,SAAS,CAAC;IAC1D;EAAC;IAAAzP,GAAA;IAAAZ,KAAA,EAED,SAAAH,EAAEG,KAAK,EAAE;MACP,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,GAAG,EAAE,GAAG,EAAEiQ,UAAU,CAACI,SAAS,CAAC;IAC1D;EAAC;IAAAzP,GAAA;IAAAZ,KAAA,EAED,SAAA2hB,KAAK3hB,KAAK,EAAE;MACV,OAAO,IAAI,CAACkY,KAAK,CAAClY,KAAK,EAAE,MAAM,EAAE,GAAG,EAAEiQ,UAAU,CAACI,SAAS,CAAC;IAC7D;EAAC;IAAAzP,GAAA;IAAAZ,KAAA,EA9TD,SAAAoR,KAAWlO,OAAO,EAAE;MAClB,OAAO,IAAIoW,cAAc,CAACpW,OAAO,CAAC;IACpC;EAAC;AAAA,EA5B0BsX,iCAAkB;AA4V/C;AACA;AACA;AACA;AACA;AACAlB,6BAAc,CAACoD,OAAO,GAAG,CACvB,OAAO,EACP,YAAY,EACZ,gBAAgB,EAChB,aAAa,EACb,YAAY,EACZ,SAAS,EACT,QAAQ,EACR,OAAO,EACP,YAAY,EACZ,MAAM,EACN,gBAAgB,EAChB,mBAAmB,EACnB,cAAc,EACd,OAAO,EACP,SAAS,EACT,UAAU,EACV,KAAK,EACL,QAAQ,EACR,MAAM,EACN,OAAO,EACP,WAAW,EACX,iBAAiB,EACjB,aAAa,EACb,QAAQ,EACR,OAAO,EACP,SAAS,EACT,KAAK,EACL,QAAQ,EACR,YAAY,EACZ,WAAW,EACX,IAAI,EACJ,kBAAkB,EAClB,KAAK,EACL,QAAQ,EACR,SAAS,EACT,SAAS,EACT,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,mBAAmB,EACnB,MAAM,EACN,aAAa,EACb,sBAAsB,EACtB,aAAa,EACb,kBAAkB,EAClB,gBAAgB,EAChB,UAAU,EACV,UAAU,EACV,WAAW,EACX,YAAY,EACZ,eAAe,EACf,OAAO,EACP,GAAG,EACH,GAAG,EACH,MAAM,CACP;;AAED;AACA;AACA;AACA;AACA;AACApD,6BAAc,CAAC0E,WAAW,GAAG1E,6BAAc,CAACoD,OAAO,CAAClc,GAAG,CAAC4J,SAAS,CAAC,CAACtH,MAAM,CAAC+P,iBAAa,CAACsC,aAAa,CAAC;AAEvFmE,oFAAc,E;;;;;;;;ACx7B7B;AACA;AACA;AACA;;AASiB;AAE8B;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,IAQMsI,eAAO;EACX,SAAAA,QAAYtV,IAAI,EAAEiJ,QAAQ,EAAErS,OAAO,EAAE;IAAAiN,sBAAA,OAAAyR,OAAA;IACnC,IAAItc,cAAc;IAClB,IAAI,CAACgH,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACiJ,QAAQ,GAAGA,QAAQ;IACxB,IAAIrS,OAAO,IAAI,IAAI,EAAE;MACnB,IAAIyR,0CAAa,CAACY,QAAQ,CAAC,EAAE;QAC3BrS,OAAO,GAAGqS,QAAQ;QAClB,IAAI,CAACA,QAAQ,GAAG,KAAK,CAAC;MACxB,CAAC,MAAM;QACLrS,OAAO,GAAG,CAAC,CAAC;MACd;IACF;IACAoC,cAAc,GAAG,IAAIgU,kBAAc,CAACpW,OAAO,CAAC;IAC5CoC,cAAc,CAACoL,SAAS,CAAC,IAAI,CAAC;IAC9B,IAAI,CAACpL,cAAc,GAAG,YAAY;MAChC,OAAOA,cAAc;IACvB,CAAC;EACH;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EATE,OAAAgL,mBAAA,CAAAsR,OAAA;IAAAhhB,GAAA;IAAAZ,KAAA;IAcA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;IACE,SAAA6hB,UAAUC,KAAK,EAAE;MACf,IAAIlhB,GAAG,EAAEmhB,KAAK,EAAE/hB,KAAK;MACrB,OAAO+hB,KAAK,GAAK,YAAY;QAC3B,IAAI3U,OAAO;QACXA,OAAO,GAAG,EAAE;QACZ,KAAKxM,GAAG,IAAIkhB,KAAK,EAAE;UACjB9hB,KAAK,GAAGgiB,YAAY,CAACF,KAAK,CAAClhB,GAAG,CAAC,CAAC;UAChC,IAAIZ,KAAK,EAAE;YACToN,OAAO,CAACC,IAAI,CAAC4U,mBAAW,CAACrhB,GAAG,EAAEZ,KAAK,CAAC,CAAC;UACvC;QACF;QACA,OAAOoN,OAAO;MAChB,CAAC,CAAE,CAAC,CAAEyO,IAAI,CAAC,CAAC,CAACza,IAAI,CAAC,GAAG,CAAC;IACxB;;IAEA;AACF;AACA;AACA;AACA;AACA;EALE;IAAAR,GAAA;IAAAZ,KAAA,EAMA,SAAAkiB,WAAA,EAAa;MACX,OAAO,IAAI,CAAC5c,cAAc,CAAC,CAAC,CAACsP,SAAS,CAAC,CAAC;IAC1C;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;EANE;IAAAhU,GAAA;IAAAZ,KAAA,EAOA,SAAAmiB,UAAU7V,IAAI,EAAE;MACd,OAAO,IAAI,CAAChH,cAAc,CAAC,CAAC,CAACmW,QAAQ,CAACnP,IAAI,CAAC;IAC7C;;IAEA;AACF;AACA;AACA;AACA;EAJE;IAAA1L,GAAA;IAAAZ,KAAA,EAKA,SAAAmN,WAAA,EAAa;MACX;MACA,IAAIiV,cAAc,GAAG,IAAI,CAAC9c,cAAc,CAAC,CAAC,CAACuY,gBAAgB,CAAC,CAAC;MAC7DtW,MAAM,CAACF,IAAI,CAAC+a,cAAe,CAAC,CAAC1hB,OAAO,CAAC,UAAAE,GAAG,EAAI;QAC1C,IAAG+T,0CAAa,CAACyN,cAAc,CAACxhB,GAAG,CAAC,CAAC,EAAC;UACpC,OAAOwhB,cAAc,CAACxhB,GAAG,CAAC;QAC5B;MACF,CAAC,CAAC;MACF,IAAIwhB,cAAc,CAACjV,UAAU,EAAE;QAC7B;QACAmG,0BAAK,CAAC8O,cAAc,EAAEA,cAAc,CAACjV,UAAU,CAAC;QAChD,OAAOiV,cAAc,CAACjV,UAAU;MAClC;MAEA,OAAOiV,cAAc;IACvB;;IAEA;AACF;AACA;AACA;AACA;AACA;EALE;IAAAxhB,GAAA;IAAAZ,KAAA,EAMA,SAAA+M,QAAQT,IAAI,EAAEtM,KAAK,EAAE;MACnB,IAAI,CAACsF,cAAc,CAAC,CAAC,CAAC8N,GAAG,SAAAtQ,MAAA,CAASwJ,IAAI,GAAItM,KAAK,CAAC;MAChD,OAAO,IAAI;IACb;;IAEA;AACF;AACA;AACA;AACA;AACA;EALE;IAAAY,GAAA;IAAAZ,KAAA,EAMA,SAAAwM,QAAQF,IAAI,EAAE;MACZ,OAAO,IAAI,CAACa,UAAU,CAAC,CAAC,SAAArK,MAAA,CAASwJ,IAAI,EAAG,IAAI,IAAI,CAACa,UAAU,CAAC,CAAC,CAACb,IAAI,CAAC;IACrE;;IAEA;AACF;AACA;AACA;AACA;AACA;EALE;IAAA1L,GAAA;IAAAZ,KAAA,EAMA,SAAAqiB,WAAW/V,IAAI,EAAE;MACf,IAAI+I,GAAG;MACP,OAAO,CAACA,GAAG,GAAG,IAAI,CAAC/P,cAAc,CAAC,CAAC,CAACoW,MAAM,SAAA5Y,MAAA,CAASwJ,IAAI,CAAE,CAAC,KAAK,IAAI,GAAG+I,GAAG,GAAG,IAAI,CAAC/P,cAAc,CAAC,CAAC,CAACoW,MAAM,CAACpP,IAAI,CAAC;IAChH;;IAEA;AACF;AACA;AACA;AACA;EAJE;IAAA1L,GAAA;IAAAZ,KAAA,EAKA,SAAAsiB,QAAA,EAAU;MACR,OAAO,EAAE;IACX;;IAEA;AACF;AACA;AACA;AACA;EAJE;IAAA1hB,GAAA;IAAAZ,KAAA,EAKA,SAAAuiB,QAAA,EAAU;MACR,IAAIC,GAAG,GAAG,GAAG,GAAG,IAAI,CAAClW,IAAI;MACzB,IAAIuV,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC,IAAI,CAAC1U,UAAU,CAAC,CAAC,CAAC;MACjD,IAAG0U,SAAS,IAAIA,SAAS,CAAC5iB,MAAM,GAAG,CAAC,EAAE;QACpCujB,GAAG,IAAI,GAAG,GAAGX,SAAS;MACxB;MACA,OAAOW,GAAG,GAAG,GAAG;IAClB;;IAEA;AACF;AACA;AACA;AACA;EAJE;IAAA5hB,GAAA;IAAAZ,KAAA,EAKA,SAAAyiB,SAAA,EAAW;MACT,YAAA3f,MAAA,CAAY,IAAI,CAACwJ,IAAI;IACvB;;IAEA;AACF;AACA;AACA;AACA;EAJE;IAAA1L,GAAA;IAAAZ,KAAA,EAKA,SAAAie,OAAA,EAAS;MACP,OAAO,IAAI,CAACsE,OAAO,CAAC,CAAC,GAAG,IAAI,CAACD,OAAO,CAAC,CAAC,GAAG,IAAI,CAACG,QAAQ,CAAC,CAAC;IAC1D;;IAEA;AACF;AACA;AACA;AACA;EAJE;IAAA7hB,GAAA;IAAAZ,KAAA,EAKA,SAAA0iB,MAAA,EAAQ;MACN,IAAIrW,OAAO,EAAEC,IAAI,EAAE+I,GAAG,EAAErV,KAAK;MAC7B,IAAI,CAACwJ,oCAAU,CAAC,OAAOkK,QAAQ,KAAK,WAAW,IAAIA,QAAQ,KAAK,IAAI,GAAGA,QAAQ,CAACiP,aAAa,GAAG,KAAK,CAAC,CAAC,EAAE;QACvG,MAAM,8CAA8C;MACtD;MACAtW,OAAO,GAAGqH,QAAQ,CAACiP,aAAa,CAAC,IAAI,CAACrW,IAAI,CAAC;MAC3C+I,GAAG,GAAG,IAAI,CAAClI,UAAU,CAAC,CAAC;MACvB,KAAKb,IAAI,IAAI+I,GAAG,EAAE;QAChBrV,KAAK,GAAGqV,GAAG,CAAC/I,IAAI,CAAC;QACjBD,OAAO,CAACS,YAAY,CAACR,IAAI,EAAEtM,KAAK,CAAC;MACnC;MACA,OAAOqM,OAAO;IAChB;EAAC;IAAAzL,GAAA;IAAAZ,KAAA,EAhKD,SAAAoR,KAAW9E,IAAI,EAAEiJ,QAAQ,EAAErS,OAAO,EAAE;MAClC,OAAO,IAAI,IAAI,CAACoJ,IAAI,EAAEiJ,QAAQ,EAAErS,OAAO,CAAC;IAC1C;EAAC;IAAAtC,GAAA;IAAAZ,KAAA,EAgKD,SAAA4iB,aAAoBJ,GAAG,EAAEK,eAAe,EAAE;MACxC,IAAIC,OAAO;MACXA,OAAO,GAAG1W,cAAO,CAACoW,GAAG,EAAE,WAAW,CAAC,IAAIpW,cAAO,CAACoW,GAAG,EAAE,KAAK,CAAC;MAC1D,OAAOlV,eAAQ,CAACkV,GAAG,EAAEK,eAAe,CAAC,IAAI,YAAY,CAACzO,IAAI,CAAC0O,OAAO,CAAC;IACrE;EAAC;AAAA;AAEF;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASb,mBAAWA,CAACrhB,GAAG,EAAEZ,KAAK,EAAE;EAC/B,IAAI,CAACA,KAAK,EAAE;IACV,OAAO,KAAK,CAAC;EACf,CAAC,MAAM,IAAIA,KAAK,KAAK,IAAI,EAAE;IACzB,OAAOY,GAAG;EACZ,CAAC,MAAM;IACL,UAAAkC,MAAA,CAAUlC,GAAG,SAAAkC,MAAA,CAAK9C,KAAK;EACzB;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASgiB,YAAYA,CAAChiB,KAAK,EAAE;EAC3B,OAAO8H,gCAAQ,CAAC9H,KAAK,CAAC,GAAGA,KAAK,CAACwI,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,GAAGxI,KAAK;AACpF;AAEe4hB,2DAAO,E;;;;;AC5PwB;AASzB;AAML;AAEY;AACmD;AACV;;AAGrE;AACA;AACA;AACA;AACA;AACA,SAASmB,OAAOA,CAACxjB,GAAG,EAAE;EAClB,IAAI2hB,MAAM,GAAGxN,QAAQ,CAACuB,QAAQ,CAACC,QAAQ,GAAG,IAAI,GAAGxB,QAAQ,CAACuB,QAAQ,CAAC+N,IAAI;EACvE,IAAIzjB,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAClB2hB,MAAM,IAAIxN,QAAQ,CAACuB,QAAQ,CAACgO,QAAQ;EACtC,CAAC,MAAM,IAAI1jB,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACzB2hB,MAAM,IAAIxN,QAAQ,CAACuB,QAAQ,CAACgO,QAAQ,CAACza,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC;EAChE;EACA,OAAO0Y,MAAM,GAAG3hB,GAAG;AACvB;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS2jB,KAAKA,CAAC3jB,GAAG,EAAC;EACjB,OAAOA,GAAG,GAAG,CAAC,CAACA,GAAG,CAACqC,KAAK,CAAC,YAAY,CAAC,GAAG,KAAK;AAChD;;AAEA;AACA,SAASuhB,kBAAkBA,CAAC5N,QAAQ,EAAE;EACpC,OAAOjW,SAAK,CAACiW,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC;AAChC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS6N,eAAeA,CAAClgB,OAAO,EAAE;EAChC,IAAOmgB,SAAS,GAAIngB,OAAO,CAApBmgB,SAAS;EAChB,IAAMC,WAAW,GAAG,CAACD,SAAS,IAAKA,SAAS,CAACjF,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAIiF,SAAS,CAACvjB,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,IAAK;EACnG,OAAOoD,OAAO,CAACmgB,SAAS;EAExB,OAAOC,WAAW,GAAGD,SAAS,SAAAvgB,MAAA,CAASugB,SAAS,OAAI;AACtD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,YAAYA,CAAChO,QAAQ,EAAErS,OAAO,EAAE;EACvC,IAAIA,OAAO,CAACsgB,UAAU,IAAItgB,OAAO,CAACsgB,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACvD,OAAO,MAAM,GAAGtgB,OAAO,CAACsgB,UAAU;EACpC;EACA;EACA,IAAItO,QAAQ,GAAG,SAAS;EACxB,IAAIuO,OAAO,GAAG,EAAE;EAChB,IAAIC,SAAS,GAAG,KAAK;EACrB,IAAIV,IAAI,GAAG,iBAAiB;EAC5B,IAAIW,IAAI,GAAG,GAAG,GAAGzgB,OAAO,CAACsgB,UAAU;EACnC;EACA,IAAItgB,OAAO,CAACgS,QAAQ,EAAE;IACpBA,QAAQ,GAAGhS,OAAO,CAACgS,QAAQ,GAAG,IAAI;EACpC;EACA,IAAIhS,OAAO,CAAC0gB,WAAW,EAAE;IACvBH,OAAO,GAAGvgB,OAAO,CAACsgB,UAAU,GAAG,GAAG;IAClCG,IAAI,GAAG,EAAE;EACX;EACA,IAAIzgB,OAAO,CAAC2gB,aAAa,EAAE;IACzBH,SAAS,GAAG,MAAM,GAAGP,kBAAkB,CAAC5N,QAAQ,CAAC;EACnD;EACA,IAAIrS,OAAO,CAAC8R,MAAM,EAAE;IAClBE,QAAQ,GAAG,UAAU;IACrB,IAAIhS,OAAO,CAAC4gB,oBAAoB,KAAK,KAAK,EAAE;MAC1CJ,SAAS,GAAG,KAAK;IACnB;IACA,IAAKxgB,OAAO,CAAC6gB,mBAAmB,IAAI,IAAI,IAAK7gB,OAAO,CAAC6gB,mBAAmB,KAAKnf,qBAAqB,IAAI1B,OAAO,CAAC6gB,mBAAmB,KAAKjf,UAAU,EAAE;MAChJ2e,OAAO,GAAG,EAAE;MACZC,SAAS,GAAG,EAAE;MACdV,IAAI,GAAG9f,OAAO,CAAC6gB,mBAAmB;IACpC;EACF,CAAC,MAAM,IAAI7gB,OAAO,CAAC8gB,KAAK,EAAE;IACxB9O,QAAQ,GAAG,SAAS;IACpBuO,OAAO,GAAG,EAAE;IACZC,SAAS,GAAGxgB,OAAO,CAAC2gB,aAAa,GAAG,GAAG,IAAKvkB,SAAK,CAACiW,QAAQ,CAAC,GAAG,CAAC,GAAI,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE;IAChFyN,IAAI,GAAG9f,OAAO,CAAC8gB,KAAK;EACtB;EACA,OAAO,CAAC9O,QAAQ,EAAEuO,OAAO,EAAEC,SAAS,EAAEV,IAAI,EAAEW,IAAI,CAAC,CAACviB,IAAI,CAAC,EAAE,CAAC;AAC5D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS6iB,kBAAkBA,CAAAvF,IAAA,EAAiF;EAAA,IAAAwF,kBAAA,GAAAxF,IAAA,CAA/ExZ,aAAa;IAAbA,aAAa,GAAAgf,kBAAA,cAAG,OAAO,GAAAA,kBAAA;IAAAC,SAAA,GAAAzF,IAAA,CAAEnZ,IAAI;IAAJA,IAAI,GAAA4e,SAAA,cAAG,QAAQ,GAAAA,SAAA;IAAEC,UAAU,GAAA1F,IAAA,CAAV0F,UAAU;IAAEC,aAAa,GAAA3F,IAAA,CAAb2F,aAAa;IAAEC,OAAO,GAAA5F,IAAA,CAAP4F,OAAO;EACvG,IAAIphB,OAAO;IAAEoS,YAAY,GAAGpQ,aAAa;EAEzC,IAAIyP,0CAAa,CAACW,YAAY,CAAC,EAAE;IAC/BpS,OAAO,GAAGoS,YAAY;IACtBA,YAAY,GAAGpS,OAAO,CAACgC,aAAa;IACpCK,IAAI,GAAGrC,OAAO,CAACqC,IAAI;IACnB+e,OAAO,GAAGphB,OAAO,CAACohB,OAAO;EAC3B;EACA,IAAI/e,IAAI,IAAI,IAAI,EAAE;IAChBA,IAAI,GAAG,QAAQ;EACjB;EACA,IAAI6e,UAAU,IAAI,IAAI,EAAE;IACtB9O,YAAY,GAAGlQ,SAAS,IAAAtC,MAAA,CAAIwS,YAAY,OAAAxS,MAAA,CAAIyC,IAAI,EAAG;IACnDA,IAAI,GAAG,IAAI;IACX,IAAI+P,YAAY,IAAI,IAAI,EAAE;MACxB,MAAM,IAAIrU,KAAK,kCAAA6B,MAAA,CAAkCyE,MAAM,CAACF,IAAI,CAACjC,SAAS,CAAC,CAAChE,IAAI,CAAC,IAAI,CAAC,CAAE,CAAC;IACvF;EACF;EACA,IAAIijB,aAAa,EAAE;IACjB,IAAI/O,YAAY,KAAK,OAAO,IAAI/P,IAAI,KAAK,QAAQ,IAAI+P,YAAY,KAAK,QAAQ,EAAE;MAC9EA,YAAY,GAAG,IAAI;MACnB/P,IAAI,GAAG,IAAI;IACb,CAAC,MAAM;MACL,MAAM,IAAItE,KAAK,CAAC,2CAA2C,CAAC;IAC9D;EACF;EACA,IAAIqjB,OAAO,IAAIhP,YAAY,KAAK,OAAO,IAAI/P,IAAI,KAAK,QAAQ,EAAE;IAC5D+P,YAAY,GAAG,IAAI;IACnB/P,IAAI,GAAG,IAAI;EACb;EACA,OAAO,CAAC+P,YAAY,EAAE/P,IAAI,CAAC,CAACnE,IAAI,CAAC,GAAG,CAAC;AACvC;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASmjB,cAAcA,CAAChP,QAAQ,EAAE;EAChC,OAAOiP,kBAAkB,CAACjP,QAAQ,CAAC,CAAC/M,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;AAC/E;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASic,cAAcA,CAAClP,QAAQ,EAAErS,OAAO,EAAE;EACzC,IAAIggB,KAAK,CAAC3N,QAAQ,CAAC,EAAC;IAClBA,QAAQ,GAAGgP,cAAc,CAAChP,QAAQ,CAAC;EACrC,CAAC,MAAM;IACL,IAAI;MACF;MACAA,QAAQ,GAAGmP,kBAAkB,CAACnP,QAAQ,CAAC;IACzC,CAAC,CAAC,OAAOoP,KAAK,EAAE,CAAC;IAEjBpP,QAAQ,GAAGgP,cAAc,CAAChP,QAAQ,CAAC;IAEnC,IAAIrS,OAAO,CAACkhB,UAAU,EAAE;MACtB7O,QAAQ,GAAGA,QAAQ,GAAG,GAAG,GAAGrS,OAAO,CAACkhB,UAAU;IAChD;IACA,IAAIlhB,OAAO,CAAC+B,MAAM,EAAE;MAClB,IAAI,CAAC/B,OAAO,CAAC0hB,eAAe,EAAE;QAC5BrP,QAAQ,GAAGA,QAAQ,CAAC/M,OAAO,CAAC,uBAAuB,EAAE,EAAE,CAAC;MAC1D;MACA+M,QAAQ,GAAGA,QAAQ,GAAG,GAAG,GAAGrS,OAAO,CAAC+B,MAAM;IAC5C;EACF;EACA,OAAOsQ,QAAQ;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASsP,QAAQA,CAAC3hB,OAAO,EAAE;EACzB,IAAOsgB,UAAU,GAAgBtgB,OAAO,CAAjCsgB,UAAU;IAAEY,UAAU,GAAIlhB,OAAO,CAArBkhB,UAAU;EAE7B,IAAI,CAACZ,UAAU,EAAE;IACf,OAAO,oBAAoB;EAC7B;EAEA,IAAIY,UAAU,IAAIA,UAAU,CAACxiB,KAAK,CAAC,QAAQ,CAAC,EAAE;IAC5C,OAAO,sCAAsC;EAC/C;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkjB,aAAaA,CAACvP,QAAQ,EAAErS,OAAO,EAAE;EACxC;EACA,IAAM6hB,cAAc,GAAI7hB,OAAO,CAAC8hB,aAAa,IAAI,OAAO9hB,OAAO,CAAC8hB,aAAa,KAAK,WAAY;;EAE9F;EACA,IAAMC,cAAc,GAAI1P,QAAQ,CAAC6I,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI7I,QAAQ,CAAC3T,KAAK,CAAC,UAAU,CAAC,IAAIshB,KAAK,CAAC3N,QAAQ,CAAC,IAAKrS,OAAO,CAACgiB,OAAO;EAEtH,IAAIH,cAAc,IAAI,CAACE,cAAc,EAAE;IACrC/hB,OAAO,CAACgiB,OAAO,GAAG,CAAC;EACrB;EAEA,OAAOhiB,OAAO,CAACgiB,OAAO,OAAApiB,MAAA,CAAOI,OAAO,CAACgiB,OAAO,IAAK,EAAE;AACrD;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAACjiB,OAAO,EAAE;EACrC,IAAAyd,KAAA,GAAoDzd,OAAO,IAAI,CAAC,CAAC;IAA5DK,WAAW,GAAAod,KAAA,CAAXpd,WAAW;IAAEH,aAAa,GAAAud,KAAA,CAAbvd,aAAa;IAAKwX,YAAY,GAAAwK,wBAAA,CAAAzE,KAAA,EAAA0E,SAAA;EAChD,IAAM9a,MAAM,GAAG,IAAI+O,kBAAc,CAACsB,YAAY,CAAC;;EAE/C;EACA,IAAIxX,aAAa,IAAIyD,mBAAmB,CAACzD,aAAa,CAAC,EAAE;IACvDmH,MAAM,CAACyR,KAAK,CAAC,CAAC,CAAC7V,MAAM,CAACU,mBAAmB,CAACzD,aAAa,CAAC,CAAC;EAC3D;;EAEA;EACA,IAAIG,WAAW,EAAE;IACf,IAAIA,WAAW,KAAK,mBAAmB,IAAIgH,MAAM,CAACkR,QAAQ,CAAC,OAAO,CAAC,IAAIlR,MAAM,CAACkR,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAChGlY,WAAW,IAAI,QAAQ;IACzB;IACA,IAAM+hB,0BAA0B,GAAGpf,uBAAuB,CAAC3C,WAAW,CAAC,IAAI2C,uBAAuB,CAACqf,IAAI;IACvGD,0BAA0B,CAAC5kB,OAAO,CAAC,UAAAmY,CAAC;MAAA,OAAItO,MAAM,CAACyR,KAAK,CAAC,CAAC,CAAC1W,cAAc,CAACuT,CAAC,CAAC;IAAA,EAAC;EAC3E;EAEA,OAAOtO,MAAM,CAACgG,SAAS,CAAC,CAAC;AAC3B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiV,eAAeA,CAACjQ,QAAQ,EAAAqL,KAAA,EAAS;EAAA,IAANrb,IAAI,GAAAqb,KAAA,CAAJrb,IAAI;EACtC,OAAQ,CAAC2d,KAAK,CAAC3N,QAAQ,CAAC,IAAIhQ,IAAI,KAAK,OAAO,GAAIwd,OAAO,CAACxN,QAAQ,CAAC,GAAGA,QAAQ;AAC9E;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkQ,SAASA,CAAClQ,QAAQ,EAAErS,OAAO,EAAE;EACpC,IAAIggB,KAAK,CAAC3N,QAAQ,CAAC,KAAKrS,OAAO,CAACqC,IAAI,KAAK,QAAQ,IAAIrC,OAAO,CAACqC,IAAI,KAAK,OAAO,CAAC,EAAE;IAC9E,OAAOgQ,QAAQ;EACjB;EAEA,IAAM2P,OAAO,GAAGJ,aAAa,CAACvP,QAAQ,EAAErS,OAAO,CAAC;EAChD,IAAMoa,oBAAoB,GAAG6H,oBAAoB,CAACjiB,OAAO,CAAC;EAC1D,IAAMge,MAAM,GAAGqC,YAAY,CAAChO,QAAQ,EAAErS,OAAO,CAAC;EAC9C,IAAMmgB,SAAS,GAAGD,eAAe,CAAClgB,OAAO,CAAC;EAC1C,IAAMoS,YAAY,GAAG2O,kBAAkB,CAAC/gB,OAAO,CAAC;EAEhDqS,QAAQ,GAAGkP,cAAc,CAAClP,QAAQ,EAAErS,OAAO,CAAC;EAE5C,OAAOyS,8BAAO,CAAC,CAACuL,MAAM,EAAE5L,YAAY,EAAE+N,SAAS,EAAE/F,oBAAoB,EAAE4H,OAAO,EAAE3P,QAAQ,CAAC,CAAC,CACvFnU,IAAI,CAAC,GAAG,CAAC,CACToH,OAAO,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;EAAA,CAC7BA,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC;AACxB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkd,cAAcA,CAACxiB,OAAO,EAAEqQ,MAAM,EAAE;EACvC,IAAIrQ,OAAO,YAAYoW,kBAAc,EAAE;IACrCpW,OAAO,GAAGA,OAAO,CAAC0R,SAAS,CAAC,CAAC;EAC/B;EAEA1R,OAAO,GAAGyF,QAAQ,CAAC,CAAC,CAAC,EAAEzF,OAAO,EAAEqQ,MAAM,EAAElO,oBAAoB,CAAC;EAE7D,IAAInC,OAAO,CAACqC,IAAI,KAAK,OAAO,EAAE;IAC5BrC,OAAO,CAACmD,YAAY,GAAGnD,OAAO,CAACmD,YAAY,IAAInD,OAAO,CAAC+B,MAAM;EAC/D;EAEA,OAAO/B,OAAO;AAChB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAAS+H,OAAGA,CAACsK,QAAQ,EAA6B;EAAA,IAA3BrS,OAAO,GAAAjB,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;EAAA,IAAEsR,MAAM,GAAAtR,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;EAC7D,IAAI,CAACsT,QAAQ,EAAE;IACb,OAAOA,QAAQ;EACjB;EACArS,OAAO,GAAGwiB,cAAc,CAACxiB,OAAO,EAAEqQ,MAAM,CAAC;EACzCgC,QAAQ,GAAGiQ,eAAe,CAACjQ,QAAQ,EAAErS,OAAO,CAAC;EAE7C,IAAMyhB,KAAK,GAAGE,QAAQ,CAAC3hB,OAAO,CAAC;EAE/B,IAAIyhB,KAAK,EAAE;IACT,MAAMA,KAAK;EACb;EACA,IAAIgB,SAAS,GAAGF,SAAS,CAAClQ,QAAQ,EAAErS,OAAO,CAAC;EAC5C,IAAGA,OAAO,CAACC,YAAY,EAAE;IACvB,IAAInB,gBAAgB,GAAGiB,mBAAmB,CAACC,OAAO,CAAC;IACnD,IAAI0iB,qBAAqB,GAAG7jB,wBAAwB,CAACC,gBAAgB,CAAC;IACtE;IACA,IAAI6jB,QAAQ,GAAG,GAAG;IAClB,IAAIF,SAAS,CAACvH,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;MAC/ByH,QAAQ,GAAG,GAAG;IAChB;IACAF,SAAS,MAAA7iB,MAAA,CAAM6iB,SAAS,EAAA7iB,MAAA,CAAG+iB,QAAQ,SAAA/iB,MAAA,CAAM8iB,qBAAqB,CAAE;EAClE;EACA,IAAI1iB,OAAO,CAAC4iB,UAAU,EAAE;IACtB,IAAID,SAAQ,GAAGF,SAAS,CAACvH,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG;IACtDuH,SAAS,MAAA7iB,MAAA,CAAM6iB,SAAS,EAAA7iB,MAAA,CAAG+iB,SAAQ,oBAAA/iB,MAAA,CAAiBI,OAAO,CAAC4iB,UAAU,CAAE;EAC1E;EACA,OAAOH,SAAS;AAClB;AAAC,C;;;;;;;;AC/XD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASI,mBAAmBA,CAACC,MAAM,EAAE;EAClD,IAAIC,WAAW,GAAGD,MAAM,CAACC,WAAW,IAAI,EAAE;EAC1C,IAAIA,WAAW,CAAChnB,MAAM,EAAE;IACtB,OAAOgnB,WAAW;EACpB;EACA,IAAAC,IAAA,GAAyC,CAACF,MAAM,CAACG,SAAS,EAAEH,MAAM,CAACI,SAAS,EAAEJ,MAAM,CAACK,UAAU,CAAC,CAAC7lB,GAAG,CAAC8lB,MAAM,CAAC;IAAAC,KAAA,GAAAhS,iCAAA,CAAA2R,IAAA;IAAvGC,SAAS,GAAAI,KAAA;IAAEH,SAAS,GAAAG,KAAA;IAAEF,UAAU,GAAAE,KAAA;EACrC,IAAI,CAACJ,SAAS,EAAEC,SAAS,EAAEC,UAAU,CAAC,CAACG,IAAI,CAACpe,KAAK,CAAC,EAAE;IAClD,MAAM,4CAA4C,GAClD,+DAA+D;EACjE;EAEA,IAAI+d,SAAS,GAAGC,SAAS,EAAE;IACzB,MAAM,uCAAuC;EAC/C;EAEA,IAAIC,UAAU,IAAI,CAAC,EAAE;IACnB,MAAM,uCAAuC;EAC/C,CAAC,MAAM,IAAIA,UAAU,KAAK,CAAC,EAAE;IAC3BF,SAAS,GAAGC,SAAS;EACvB;EAEA,IAAIK,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAAC,CAACP,SAAS,GAAGD,SAAS,IAAIO,IAAI,CAACE,GAAG,CAACP,UAAU,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;EAC/E,KAAK,IAAIQ,OAAO,GAAGV,SAAS,EAAEU,OAAO,GAAGT,SAAS,EAAES,OAAO,IAAIJ,QAAQ,EAAE;IACtER,WAAW,CAAC5Y,IAAI,CAACwZ,OAAO,CAAC;EAC3B;EACAZ,WAAW,CAAC5Y,IAAI,CAAC+Y,SAAS,CAAC;EAC3B,OAAOH,WAAW;AACpB,C;;ACtCiC;AAEjC,IAAMzb,mBAAO,GAAGsc,OAAa;AAC2B;AACT;AACtB;;AAEzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,SAASA,CAACC,SAAS,EAAE1gB,KAAK,EAAEhB,cAAc,EAAgB;EAAA,IAAdpC,OAAO,GAAAjB,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;EACtE,IAAIglB,YAAY,GAAGH,gBAAsB,CAAC5jB,OAAO,CAAC;EAClDoC,cAAc,GAAGA,cAAc,IAAIpC,OAAO;EAC1C+jB,YAAY,CAACC,kBAAkB,GAAG,IAAI5N,kBAAc,CAAC,CAACwN,0BAAW,CAAC,CAAC,CAAC,EAAExhB,cAAc,CAAC,EAAE;IACrFkB,IAAI,EAAE,OAAO;IACbF,KAAK,EAAEA;EACT,CAAC,CAAC,CAAC,CAACzF,QAAQ,CAAC,CAAC;EAEd,OAAOoK,OAAG,CAAC+b,SAAS,EAAEC,YAAY,CAAC;AACrC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASE,wBAAwBA,CAACH,SAAS,EAA6B;EAAA,IAA3BhB,MAAM,GAAA/jB,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;EAAA,IAAEiB,OAAO,GAAAjB,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;EAC3E,OAAO8jB,mBAAmB,CAACC,MAAM,CAAC;AACpC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASoB,uBAAuBA,CAACJ,SAAS,EAAEf,WAAW,EAAE3gB,cAAc,EAAEpC,OAAO,EAAE;EACvFA,OAAO,GAAG4jB,kCAAe,CAAC5jB,OAAO,CAAC;EAClC4jB,gBAAsB,CAAC5jB,OAAO,CAAC;EAC/B,OAAO+iB,WAAW,CAACzlB,GAAG,CAAC,UAAA8F,KAAK;IAAA,UAAAxD,MAAA,CAAOikB,SAAS,CAACC,SAAS,EAAE1gB,KAAK,EAAEhB,cAAc,EAAEpC,OAAO,CAAC,OAAAJ,MAAA,CAAIwD,KAAK;EAAA,CAAG,CAAC,CAAClF,IAAI,CAAC,IAAI,CAAC;AACjH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASimB,sBAAsBA,CAACpB,WAAW,EAAE;EAClD,IAAIA,WAAW,IAAI,IAAI,EAAE;IACvB,OAAO,EAAE;EACX;EACA,OAAOA,WAAW,CAACzlB,GAAG,CAAC,UAAA8F,KAAK;IAAA,sBAAAxD,MAAA,CAAmBwD,KAAK,UAAAxD,MAAA,CAAOwD,KAAK;EAAA,CAAI,CAAC,CAAClF,IAAI,CAAC,IAAI,CAAC;AAClF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASkmB,iCAAiCA,CAAC/R,QAAQ,EAAkD;EAAA,IAAhDpI,UAAU,GAAAlL,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;EAAA,IAAEslB,UAAU,GAAAtlB,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;EAAA,IAAEiB,OAAO,GAAAjB,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;EACxG;;EAEA,IAAIulB,oBAAoB,GAAG,CAAC,CAAC;EAC7B,IAAIhd,mBAAO,CAAC+c,UAAU,CAAC,EAAE;IACvB,OAAOC,oBAAoB;EAC7B;EAEA,IAAMC,aAAa,GAAI,CAACta,UAAU,CAACua,KAAK,IAAIH,UAAU,CAACG,KAAK,KAAK,IAAK;EAEtE,IAAMC,cAAc,GAAG,CAACxa,UAAU,CAAC6Y,MAAM;EACzC,IAAI2B,cAAc,IAAIF,aAAa,EAAE;IACnC,IAAIxB,WAAW,GAAGkB,wBAAwB,CAAC5R,QAAQ,EAAEgS,UAAU,EAAErkB,OAAO,CAAC;IAEzE,IAAIykB,cAAc,EAAE;MAClB,IAAIriB,cAAc,GAAGiiB,UAAU,CAACjiB,cAAc;MAC9C,IAAIsiB,UAAU,GAAGR,uBAAuB,CAAC7R,QAAQ,EAAE0Q,WAAW,EAAE3gB,cAAc,EAAEpC,OAAO,CAAC;MACxF,IAAI,CAACsH,mBAAO,CAACod,UAAU,CAAC,EAAE;QACxBJ,oBAAoB,CAACxB,MAAM,GAAG4B,UAAU;MAC1C;IACF;IAEA,IAAIH,aAAa,EAAE;MACjB,IAAII,SAAS,GAAGR,sBAAsB,CAACpB,WAAW,CAAC;MACnD,IAAI,CAACzb,mBAAO,CAACqd,SAAS,CAAC,EAAE;QACvBL,oBAAoB,CAACE,KAAK,GAAGG,SAAS;MACxC;IACF;EACF;EACA,OAAOL,oBAAoB;AAC7B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASM,iBAAiBA,CAAC5kB,OAAO,EAAE;EACzC,IAAI6kB,UAAU,GAAG,EAAE;EACnB,IAAI7kB,OAAO,IAAI,IAAI,EAAE;IACnB,IAAIA,OAAO,CAACijB,SAAS,IAAI,IAAI,EAAE;MAC7B4B,UAAU,CAAC1a,IAAI,gBAAAvK,MAAA,CAAgBI,OAAO,CAACijB,SAAS,QAAK,CAAC;IACxD;IACA,IAAIjjB,OAAO,CAACkjB,SAAS,IAAI,IAAI,EAAE;MAC7B2B,UAAU,CAAC1a,IAAI,gBAAAvK,MAAA,CAAgBI,OAAO,CAACkjB,SAAS,QAAK,CAAC;IACxD;EACF;EACA,OAAO2B,UAAU,CAAC3mB,IAAI,CAAC,OAAO,CAAC;AACjC;AAEO,IAAM4mB,SAAS,GAAGjB,SAAS,C;;;;;;;;;;;;;;;;;ACpJlC;AACA;AACA;AACA;;AAEgC;AAEP;AACwB;AACqB;;AAEtE;AACA;AACA;AACA;AACA;AACA;AACA;AANA,IAOMkB,iBAAQ,0BAAAC,QAAA;EACZ,SAAAD,SAAY1S,QAAQ,EAAgB;IAAA,IAAdrS,OAAO,GAAAjB,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;IAAAkO,uBAAA,OAAA8X,QAAA;IAAA,OAAAxV,kBAAA,OAAAwV,QAAA,GAC1B,KAAK,EAAE1S,QAAQ,EAAErS,OAAO;EAChC;;EAEA;EAAAwP,iBAAA,CAAAuV,QAAA,EAAAC,QAAA;EAAA,OAAA5X,oBAAA,CAAA2X,QAAA;IAAArnB,GAAA;IAAAZ,KAAA,EACA,SAAAyiB,SAAA,EAAW;MACT,OAAO,EAAE;IACX;;IAEA;EAAA;IAAA7hB,GAAA;IAAAZ,KAAA,EACA,SAAAmN,WAAA,EAAa;MACX,IAAIH,IAAI,EAAE9J,OAAO,EAAEilB,YAAY;MAC/Bnb,IAAI,GAAG+L,YAAA,CAAAC,uBAAA,CAAAiP,QAAA,CAAAnkB,SAAA,uBAAA2F,IAAA,UAAsB,CAAC,CAAC;MAC/BvG,OAAO,GAAG,IAAI,CAACgf,UAAU,CAAC,CAAC;MAC3B,IAAI/U,UAAU,GAAG,IAAI,CAACgV,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;MACnD,IAAIiG,WAAW,GAAG,IAAI,CAACjG,SAAS,CAAC,QAAQ,CAAC,IAAGhV,UAAU,CAAC6Y,MAAM;MAE9D,IAAIwB,oBAAoB,GAAG,CAAC,CAAC;MAC7B,IAAI1f,gCAAQ,CAACsgB,WAAW,CAAC,EAAE;QACzBZ,oBAAoB,CAACxB,MAAM,GAAGoC,WAAW;MAC3C,CAAC,MAAM;QACLZ,oBAAoB,GAAGF,iCAAiC,CAAC,IAAI,CAAC/R,QAAQ,EAAEpI,UAAU,EAAEib,WAAW,EAAEllB,OAAO,CAAC;MAC3G;MACA,IAAG,CAACsH,OAAO,CAACgd,oBAAoB,CAAC,EAAE;QACjC,OAAOxa,IAAI,CAAC1G,KAAK;QACjB,OAAO0G,IAAI,CAACtG,MAAM;MACpB;MAEA4M,0BAAK,CAACtG,IAAI,EAAEwa,oBAAoB,CAAC;MACjCW,YAAY,GAAGjlB,OAAO,CAACI,UAAU,IAAI,CAACJ,OAAO,CAACmlB,YAAY,GAAG,UAAU,GAAG,KAAK;MAC/E,IAAIrb,IAAI,CAACmb,YAAY,CAAC,IAAI,IAAI,EAAE;QAC9Bnb,IAAI,CAACmb,YAAY,CAAC,GAAGld,OAAG,CAAC,IAAI,CAACsK,QAAQ,EAAE,IAAI,CAAC2M,UAAU,CAAC,CAAC,CAAC;MAC5D;MACA,OAAOlV,IAAI;IACb;EAAC;AAAA,EAnCoB4U,OAAO;AAqC7B;AAEcqG,8DAAQ,E;;;;;;;;;;;;;;;;;ACzDvB;AACA;AACA;AACA;AACyF;AAC3D;AACL;AACO;;AAEhC;AACA;AACA;AACA;AACA;AACA;AACA;AANA,IAOMK,mBAAS,0BAAAJ,QAAA;EACb,SAAAI,UAAY/S,QAAQ,EAAgB;IAAA,IAAdrS,OAAO,GAAAjB,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;IAAAkO,wBAAA,OAAAmY,SAAA;IAAA,OAAA7V,mBAAA,OAAA6V,SAAA,GAC1B,QAAQ,EAAE/S,QAAQ,EAAErS,OAAO;EACnC;;EAEA;EAAAwP,kBAAA,CAAA4V,SAAA,EAAAJ,QAAA;EAAA,OAAA5X,qBAAA,CAAAgY,SAAA;IAAA1nB,GAAA;IAAAZ,KAAA,EACA,SAAAyiB,SAAA,EAAW;MACT,OAAO,EAAE;IACX;;IAEA;EAAA;IAAA7hB,GAAA;IAAAZ,KAAA,EACA,SAAAmN,WAAA,EAAa;MACX,IAAIib,WAAW,GAAG,IAAI,CAACjG,SAAS,CAAC,QAAQ,CAAC;MAC1C,IAAInV,IAAI,GAAG+L,aAAA,CAAAC,wBAAA,CAAAsP,SAAA,CAAAxkB,SAAA,uBAAA2F,IAAA,UAAsB,CAAC,CAAC;MACnC,IAAIvG,OAAO,GAAG,IAAI,CAACgf,UAAU,CAAC,CAAC;MAC/B5O,0BAAK,CAACtG,IAAI,EAAEsa,iCAAiC,CAAC,IAAI,CAAC/R,QAAQ,EAAEvI,IAAI,EAAEob,WAAW,EAAEllB,OAAO,CAAC,CAAC;MACzF,IAAG,CAAC8J,IAAI,CAACgZ,MAAM,EAAC;QACdhZ,IAAI,CAACgZ,MAAM,GAAG/a,OAAG,CAAC,IAAI,CAACsK,QAAQ,EAAErS,OAAO,CAAC;MAC3C;MACA,IAAG,CAAC8J,IAAI,CAACub,KAAK,IAAIrlB,OAAO,CAACqlB,KAAK,EAAC;QAC9Bvb,IAAI,CAACub,KAAK,GAAGT,iBAAiB,CAAC5kB,OAAO,CAACqlB,KAAK,CAAC;MAC/C;MAEA,OAAOvb,IAAI;IACb;EAAC;AAAA,EAxBqB4U,OAAO;AA0B9B;AAEc0G,iEAAS,E;;;;;;;;;;;;;;;;;AC5CQ;AACE;AACa;AACX;AACK;AAAA,IAEnCE,qBAAU,0BAAAN,QAAA;EACd,SAAAM,WAAYjT,QAAQ,EAA8B;IAAA,IAAA3B,KAAA;IAAA,IAA5B1Q,OAAO,GAAAjB,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;IAAA,IAAE6G,OAAO,GAAA7G,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,EAAE;IAAAkO,yBAAA,OAAAqY,UAAA;IAC9C5U,KAAA,GAAAnB,oBAAA,OAAA+V,UAAA,GAAM,SAAS,EAAEjT,QAAQ,EAAErS,OAAO;IAClC0Q,KAAA,CAAK6U,SAAS,GAAG3f,OAAO;IAAC,OAAA8K,KAAA;EAC3B;;EAEA;EAAAlB,mBAAA,CAAA8V,UAAA,EAAAN,QAAA;EAAA,OAAA5X,sBAAA,CAAAkY,UAAA;IAAA5nB,GAAA;IAAAZ,KAAA,EACA,SAAAsiB,QAAA,EAAU;MAAA,IAAAxJ,MAAA;MACR,OAAO,IAAI,CAAC2P,SAAS,CAACjoB,GAAG,CAAC,UAAAke,IAAA,EAA4C;QAAA,IAA1CyH,SAAS,GAAAzH,IAAA,CAATyH,SAAS;UAAEC,SAAS,GAAA1H,IAAA,CAAT0H,SAAS;UAAE9gB,cAAc,GAAAoZ,IAAA,CAAdpZ,cAAc;QAC9D,IAAIpC,OAAO,GAAG4V,MAAI,CAACoJ,UAAU,CAAC,CAAC;QAC/B,IAAIZ,oBAAoB,GAAG,IAAIhI,kBAAc,CAACpW,OAAO,CAAC;QACtDoe,oBAAoB,CAACtF,KAAK,CAAC,CAAC,CAAChB,WAAW,CAAC,OAAO1V,cAAc,KAAK,QAAQ,GAAG;UAC5E4hB,kBAAkB,EAAE5hB;QACtB,CAAC,GAAGA,cAAc,CAAC;QACnBpC,OAAO,GAAGkI,gBAAgB,CAAClI,OAAO,CAAC;QACnCA,OAAO,CAACqlB,KAAK,GAAG;UAACpC,SAAS,EAATA,SAAS;UAAEC,SAAS,EAATA;QAAS,CAAC;QACtCljB,OAAO,CAACoC,cAAc,GAAGgc,oBAAoB;QAC7C,OAAO,IAAIgH,SAAS,CAACxP,MAAI,CAACvD,QAAQ,EAAErS,OAAO,CAAC,CAAC+a,MAAM,CAAC,CAAC;MACvD,CAAC,CAAC,CAAC7c,IAAI,CAAC,EAAE,CAAC,GACT,IAAI6mB,QAAQ,CAAC,IAAI,CAAC1S,QAAQ,EAAE,IAAI,CAAC2M,UAAU,CAAC,CAAC,CAAC,CAACjE,MAAM,CAAC,CAAC;IAC3D;;IAEA;EAAA;IAAArd,GAAA;IAAAZ,KAAA,EACA,SAAAmN,WAAA,EAAa;MAEX,IAAIH,IAAI,GAAA+L,cAAA,CAAAC,yBAAA,CAAAwP,UAAA,CAAA1kB,SAAA,uBAAA2F,IAAA,MAAqB;MAC7B,OAAOuD,IAAI,CAAC1G,KAAK;MACjB,OAAO0G,IAAI,CAACtG,MAAM;MAClB,OAAOsG,IAAI;IACb;;IAEA;EAAA;IAAApM,GAAA;IAAAZ,KAAA,EACA,SAAAyiB,SAAA,EAAW;MACT,OAAO,IAAI,GAAG,IAAI,CAACnW,IAAI,GAAG,GAAG;IAC/B;EAAC;AAAA,EAlCsBsV,OAAO;AAoC/B;AAEc4G,oEAAU,E;;;;;;;;;;;;;;;;;AC5CzB;AACA;AACA;AACA;;AAKsB;AAEG;AAQR;AAEe;AAGhC,IAAME,gBAAgB,GAAG,CAAC,cAAc,EAAE,uBAAuB,EAAE,kBAAkB,EAAE,QAAQ,EAAE,SAAS,CAAC;AAE3G,IAAMvjB,mCAA0B,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC;AAEzD,IAAMH,+BAAsB,GAAG;EAC7BC,MAAM,EAAE,KAAK;EACbC,aAAa,EAAE;AACjB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AANA,IAOMyjB,iBAAQ,0BAAAT,QAAA;EACZ,SAAAS,SAAYpT,QAAQ,EAAgB;IAAA,IAAdrS,OAAO,GAAAjB,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;IAAAkO,uBAAA,OAAAwY,QAAA;IAChCzlB,OAAO,GAAGyF,QAAQ,CAAC,CAAC,CAAC,EAAEzF,OAAO,EAAEsC,oBAAoB,CAAC;IAAC,OAAAiN,kBAAA,OAAAkW,QAAA,GAChD,OAAO,EAAEpT,QAAQ,CAAC/M,OAAO,CAAC,mBAAmB,EAAE,EAAE,CAAC,EAAEtF,OAAO;EACnE;;EAEA;AACF;AACA;AACA;AACA;AACA;EALEwP,iBAAA,CAAAiW,QAAA,EAAAT,QAAA;EAAA,OAAA5X,oBAAA,CAAAqY,QAAA;IAAA/nB,GAAA;IAAAZ,KAAA,EAMA,SAAA4oB,wBAAwB5oB,KAAK,EAAE;MAC7B,IAAI,CAACsF,cAAc,CAAC,CAAC,CAACgc,oBAAoB,CAACthB,KAAK,CAAC;MACjD,OAAO,IAAI;IACb;;IAEA;AACF;AACA;AACA;AACA;AACA;EALE;IAAAY,GAAA;IAAAZ,KAAA,EAMA,SAAA6oB,eAAe7oB,KAAK,EAAE;MACpB,IAAI,CAACsF,cAAc,CAAC,CAAC,CAAC+b,WAAW,CAACrhB,KAAK,CAAC;MACxC,OAAO,IAAI;IACb;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EAPE;IAAAY,GAAA;IAAAZ,KAAA,EAQA,SAAA8oB,UAAU9oB,KAAK,EAAE;MACf,IAAI,CAACsF,cAAc,CAAC,CAAC,CAAC2b,MAAM,CAACjhB,KAAK,CAAC;MACnC,OAAO,IAAI;IACb;;IAEA;AACF;AACA;AACA;AACA;AACA;EALE;IAAAY,GAAA;IAAAZ,KAAA,EAMA,SAAA+oB,mBAAmB/oB,KAAK,EAAE;MACxB,IAAI,CAACsF,cAAc,CAAC,CAAC,CAACua,eAAe,CAAC7f,KAAK,CAAC;MAC5C,OAAO,IAAI;IACb;EAAC;IAAAY,GAAA;IAAAZ,KAAA,EAED,SAAAsiB,QAAA,EAAU;MAAA,IAAA1O,KAAA;MACR,IAAIyN,WAAW,GAAG,IAAI,CAAC/b,cAAc,CAAC,CAAC,CAACmW,QAAQ,CAAC,cAAc,CAAC;MAChE,IAAM6F,oBAAoB,GAAG,IAAI,CAAChc,cAAc,CAAC,CAAC,CAACmW,QAAQ,CAAC,uBAAuB,CAAC;MACpF,IAAMuN,QAAQ,GAAG,IAAI,CAAC1jB,cAAc,CAAC,CAAC,CAACmW,QAAQ,CAAC,kBAAkB,CAAC;MACnE,IAAI3S,OAAO,GAAG,IAAI,CAACqZ,SAAS,CAAC,SAAS,CAAC;MACvC,IAAI8G,SAAS,GAAG,EAAE;MAClB,IAAIpR,8BAAO,CAAC/O,OAAO,CAAC,IAAI,CAAC0B,OAAO,CAAC1B,OAAO,CAAC,EAAE;QACzCmgB,SAAS,GAAGngB,OAAO,CAACtI,GAAG,CAAC,UAAA2I,MAAM,EAAI;UAChC,IAAI+f,GAAG,GAAGje,OAAG,CAAC2I,KAAI,CAAC2B,QAAQ,EAAE5M,QAAQ,CACjC,CAAC,CAAC,EACFQ,MAAM,CAACrD,eAAe,IAAI,CAAC,CAAC,EAC5B;YAACZ,aAAa,EAAE,OAAO;YAAED,MAAM,EAAEkE,MAAM,CAAC5D;UAAI,CAC5C,CAAC,EAAEqO,KAAI,CAACsO,UAAU,CAAC,CAAC,CAAC;UACzB,OAAQtO,KAAI,CAACuV,eAAe,CAACD,GAAG,EAAE/f,MAAM,CAAC5D,IAAI,EAAE4D,MAAM,CAACtD,MAAM,CAAC;QAC/D,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAI2E,OAAO,CAAC6W,WAAW,CAAC,EAAE;UACxBA,WAAW,GAAGlc,mCAA0B;QAC1C;QACA,IAAI0S,8BAAO,CAACwJ,WAAW,CAAC,EAAE;UACxB4H,SAAS,GAAG5H,WAAW,CAAC7gB,GAAG,CAAC,UAAA4oB,OAAO,EAAI;YACrC,IAAIF,GAAG,GAAGje,OAAG,CAAC2I,KAAI,CAAC2B,QAAQ,EAAE5M,QAAQ,CACjC,CAAC,CAAC,EACF2Y,oBAAoB,CAAC8H,OAAO,CAAC,IAAI,CAAC,CAAC,EACnC;cAAClkB,aAAa,EAAE,OAAO;cAAED,MAAM,EAAEmkB;YAAO,CAC5C,CAAC,EAAExV,KAAI,CAACsO,UAAU,CAAC,CAAC,CAAC;YACrB,OAAQtO,KAAI,CAACuV,eAAe,CAACD,GAAG,EAAEE,OAAO,CAAC;UAC5C,CAAC,CAAC;QACJ;MACF;MACA,OAAOH,SAAS,CAAC7nB,IAAI,CAAC,EAAE,CAAC,GAAG4nB,QAAQ;IACtC;EAAC;IAAApoB,GAAA;IAAAZ,KAAA,EAED,SAAAmN,WAAA,EAAa;MACX,IAAIkU,WAAW,GAAG,IAAI,CAACc,SAAS,CAAC,cAAc,CAAC;MAChD,IAAIlB,MAAM,GAAG,IAAI,CAACkB,SAAS,CAAC,QAAQ,CAAC;MACrC,IAAIlB,MAAM,KAAK/e,SAAS,EAAE;QACxB+e,MAAM,GAAG,CAAC,CAAC;MACb;MACA,IAAItM,0CAAa,CAACsM,MAAM,CAAC,EAAE;QACzB,IAAIoI,cAAc,GAAGpI,MAAM,CAAC+F,SAAS,IAAI,IAAI,GAAG3hB,oBAAoB,GAAGL,+BAAsB;QAC7Fic,MAAM,GAAGhW,OAAG,CAACgW,MAAM,CAAC+F,SAAS,IAAI,IAAI,CAACzR,QAAQ,EAAE5M,QAAQ,CAAC,CAAC,CAAC,EAAEsY,MAAM,EAAEoI,cAAc,EAAE,IAAI,CAACnH,UAAU,CAAC,CAAC,CAAC,CAAC;MAC1G;MACA,IAAIlV,IAAI,GAAG+L,YAAA,CAAAC,uBAAA,CAAA2P,QAAA,CAAA7kB,SAAA,uBAAA2F,IAAA,UAAsB,CAAC,CAAC;MACnCuD,IAAI,GAAG7F,IAAI,CAAC6F,IAAI,EAAE0b,gBAAgB,CAAC;MACnC,IAAM5f,OAAO,GAAG,IAAI,CAACqZ,SAAS,CAAC,SAAS,CAAC;MACzC;MACA,IAAMmH,aAAa,GAAG,CAAC9e,OAAO,CAAC1B,OAAO,CAAC,IAAI0B,OAAO,CAAC6W,WAAW,CAAC,IAAIxJ,8BAAO,CAACwJ,WAAW,CAAC;MACvF,IAAI,CAACiI,aAAa,EAAE;QAClBtc,IAAI,CAAC,KAAK,CAAC,GAAG/B,OAAG,CAAC,IAAI,CAACsK,QAAQ,EAAE,IAAI,CAAC2M,UAAU,CAAC,CAAC,EAAE;UAClDhd,aAAa,EAAE,OAAO;UACtBD,MAAM,EAAEoc;QACV,CAAC,CAAC;MACJ;MACA,IAAIJ,MAAM,IAAI,IAAI,EAAE;QAClBjU,IAAI,CAAC,QAAQ,CAAC,GAAGiU,MAAM;MACzB;MACA,OAAOjU,IAAI;IACb;EAAC;IAAApM,GAAA;IAAAZ,KAAA,EAED,SAAAmpB,gBAAgBD,GAAG,EAAEK,UAAU,EAAiB;MAAA,IAAf1jB,MAAM,GAAA5D,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,IAAI;MAC5C,IAAIunB,QAAQ,GAAG,IAAI;MACnB,IAAI,CAAChf,OAAO,CAAC+e,UAAU,CAAC,EAAE;QACxB,IAAIE,SAAS,GAAGF,UAAU,KAAK,KAAK,GAAG,KAAK,GAAGA,UAAU;QACzDC,QAAQ,GAAG,QAAQ,GAAGC,SAAS;QAC/B,IAAI,CAACjf,OAAO,CAAC3E,MAAM,CAAC,EAAE;UACpB,IAAI6jB,SAAS,GAAG7R,8BAAO,CAAChS,MAAM,CAAC,GAAGA,MAAM,CAACzE,IAAI,CAAC,IAAI,CAAC,GAAGyE,MAAM;UAC5D2jB,QAAQ,IAAI,WAAW,GAAGE,SAAS;QACrC;MACF;MACA,OAAO,UAAU,GAAI,IAAI,CAAC7H,SAAS,CAAC;QAClCqH,GAAG,EAAEA,GAAG;QACR3jB,IAAI,EAAEikB;MACR,CAAC,CAAE,GAAG,GAAG;IACX;EAAC;AAAA,EA9HoB5H,OAAO;AAmIf+G,8DAAQ,E;;;;;;;;;;;;;;;AC1KvB;AACA;AACA;AACA;;AAEgC;AAIf;;AAEjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA,IAYMgB,qCAAkB,0BAAAzB,QAAA;EACtB,SAAAyB,mBAAYzmB,OAAO,EAAE;IAAAiN,iCAAA,OAAAwZ,kBAAA;IAAA,OAAAlX,4BAAA,OAAAkX,kBAAA,GACb,MAAM,EAAE,KAAK,CAAC,EAAEnW,4BAAM,CAAC;MAC3B,YAAY,EAAE,WAAW;MACzB8O,OAAO,EAAE;IACX,CAAC,EAAEpf,OAAO,CAAC;EACb;;EAEA;EAAAwP,2BAAA,CAAAiX,kBAAA,EAAAzB,QAAA;EAAA,OAAA5X,8BAAA,CAAAqZ,kBAAA;IAAA/oB,GAAA;IAAAZ,KAAA,EACA,SAAAyiB,SAAA,EAAW;MACT,OAAO,EAAE;IACX;EAAC;AAAA,EAX8Bb,OAAO;AAavC;AAEc+H,4FAAkB,E;;;;;;;;ACtCQ;;AAGzC;AACA;AACA;AACA;AACA;AACO,SAASC,gBAAgBA,CAACC,QAAQ,EAAE;EACzC,IAAIhS,8BAAO,CAACgS,QAAQ,CAAC,EAAE;IACrB,OAAOA,QAAQ;EACjB,CAAC,MAAM,IAAIA,QAAQ,CAAChU,WAAW,CAACvJ,IAAI,KAAK,UAAU,EAAE;IACnD,OAAA7L,kCAAA,CAAWopB,QAAQ,EAAE,CAAC;EACxB,CAAC,MAAM,IAAI/hB,gCAAQ,CAAC+hB,QAAQ,CAAC,EAAE;IAC7B,OAAO9gB,KAAK,CAACjF,SAAS,CAACzE,KAAK,CAACoK,IAAI,CAACiK,QAAQ,CAACC,gBAAgB,CAACkW,QAAQ,CAAC,EAAE,CAAC,CAAC;EAC3E,CAAC,MAAM;IACL,OAAO,CAACA,QAAQ,CAAC;EACnB;AACF,C;;AClBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,uBAAuBA,CAACC,eAAe,EAAEC,UAAU,EAAEzU,QAAQ,EAAErS,OAAO,EAAE;EAC/E,OAAO,IAAI+mB,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;IACtCJ,eAAe,CAACK,SAAS,GAAGJ,UAAU,CAACK,QAAQ,CAAC9U,QAAQ,EAAErS,OAAO,CAAC,CAAC+a,MAAM,CAAC,CAAC;;IAE3E;IACA,IAAIqM,sBAAsB,GAAGP,eAAe,CAACQ,aAAa,CAAC,wBAAwB,CAAC;IACpFD,sBAAsB,CAAC7b,KAAK,CAACnI,KAAK,GAAG,MAAM;IAC3C4jB,OAAO,CAACH,eAAe,CAAC;EAC1B,CAAC,CAAC;AACJ;AAEeD,oGAAuB,E;;AClBtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASU,gBAAgBA,CAACtnB,OAAO,EAAEunB,IAAI,EAAE;EACvC;EACA,IAAIvnB,OAAO,CAACoC,cAAc,EAAE;IAC1BpC,OAAO,CAACoC,cAAc,CAAC+H,IAAI,CAAC;MAC1B0S,KAAK,EAAE,CAAC0K,IAAI;IACd,CAAC,CAAC;EACJ,CAAC,MAAM;IACL;IACA;IACA,IAAI,CAACvnB,OAAO,CAAC6c,KAAK,EAAE;MAClB7c,OAAO,CAAC6c,KAAK,GAAG,EAAE;IACpB;IAEA,IAAI,OAAO7c,OAAO,CAAC6c,KAAK,KAAK,QAAQ,EAAE;MACrC7c,OAAO,CAAC6c,KAAK,GAAG,CAAC7c,OAAO,CAAC6c,KAAK,CAAC;IACjC;IAEA7c,OAAO,CAAC6c,KAAK,CAAC1S,IAAI,CAACod,IAAI,CAAC;EAC1B;AACF;AAEeD,4DAAgB,E;;AC/B8B;AACqB;;AAElF;AACA;AACA;AACA;AACA,SAASE,iCAAiCA,CAACxnB,OAAO,EAAE;EAClDA,OAAO,CAACynB,QAAQ,GAAG,IAAI;EACvBznB,OAAO,CAAC0nB,KAAK,GAAG,IAAI;EACpB1nB,OAAO,CAAC2nB,QAAQ,GAAG,KAAK;EACxB3nB,OAAO,CAAC4nB,cAAc,GAAG5nB,OAAO,CAAC4nB,cAAc,IAAI/lB,kBAAkB;EACrE7B,OAAO,SAAM,GAAGA,OAAO,SAAM,IAAI,EAAE;EACnCA,OAAO,SAAM,IAAI,wBAAwB;EACzCA,OAAO,CAAC6nB,iBAAiB,GAAG7nB,OAAO,CAAC6nB,iBAAiB,IAAI,CAAC,CAAC;EAE3D,IAAI,CAAC7nB,OAAO,CAAC6nB,iBAAiB,CAAC9kB,OAAO,EAAE;IACtC/C,OAAO,CAAC6nB,iBAAiB,CAAC9kB,OAAO,GAAGD,0BAA0B,CAACC,OAAO;EACxE;;EAEA;EACA;EACAukB,OAAgB,CAACtnB,OAAO,EAAE,OAAO,CAAC;AACpC;AAEewnB,wHAAiC,E;;ACzBhD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,UAAUA,CAACC,SAAS,EAAEH,cAAc,EAAEI,eAAe,EAAE;EAC9D,OAAO,IAAIjB,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;IACtC,IAAIe,eAAe,EAAE;MACnBhB,OAAO,CAAC,CAAC;IACX,CAAC,MAAM;MACL,IAAIiB,SAAS,GAAGzX,QAAQ,CAACiP,aAAa,CAAC,QAAQ,CAAC;MAChDwI,SAAS,CAACjC,GAAG,GAAG+B,SAAS;MAEzB,IAAIG,OAAO,GAAGC,UAAU,CAAC,YAAM;QAC7BlB,MAAM,CAAC;UACLmB,MAAM,EAAE,OAAO;UACfC,OAAO,4BAAAzoB,MAAA,CAA4BmoB,SAAS;QAC9C,CAAC,CAAC;MACJ,CAAC,EAAEH,cAAc,CAAC,CAAC,CAAC;;MAEpBK,SAAS,CAACK,OAAO,GAAG,YAAM;QACxBC,YAAY,CAACL,OAAO,CAAC,CAAC,CAAC;QACvBjB,MAAM,CAAC;UACLmB,MAAM,EAAE,OAAO;UACfC,OAAO,mBAAAzoB,MAAA,CAAmBmoB,SAAS;QACrC,CAAC,CAAC;MACJ,CAAC;MAEDE,SAAS,CAACO,MAAM,GAAG,YAAM;QACvBD,YAAY,CAACL,OAAO,CAAC,CAAC,CAAC;QACvBlB,OAAO,CAAC,CAAC;MACX,CAAC;MACDxW,QAAQ,CAACiY,IAAI,CAACC,WAAW,CAACT,SAAS,CAAC;IACtC;EACF,CAAC,CAAC;AACJ;AAEeH,6DAAU,E;;AC1CzB;AACA;AACA;AACA;AACA;AACA;AACA,SAASa,eAAeA,CAACC,YAAY,EAAE3B,MAAM,EAAE;EAC7C,OAAOkB,UAAU,CAAC,YAAM;IACtBlB,MAAM,CAAC;MACLmB,MAAM,EAAE,OAAO;MACfC,OAAO,EAAE;IACX,CAAC,CAAC;EACJ,CAAC,EAAEO,YAAY,CAAC;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,SAAS,EAAEF,YAAY,EAAE;EAC/C,OAAO,IAAI7B,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;IACtC,IAAMiB,OAAO,GAAGS,eAAe,CAACC,YAAY,EAAE3B,MAAM,CAAC;;IAErD;IACA;IACA,IAAM8B,SAAS,GAAI,OAAOC,KAAK,KAAK,WAAW,IAAIA,KAAK,GAAIC,iBAAiB,GAAGC,eAAe;IAE/FH,SAAS,CAACD,SAAS,CAAC,CAAChb,IAAI,CAAC,UAACqb,IAAI,EAAK;MAClCnC,OAAO,CAAC;QACNoB,MAAM,EAAE,SAAS;QACjBgB,OAAO,EAAE;UACPC,OAAO,EAAEC,GAAG,CAACC,eAAe,CAACJ,IAAI;QACnC;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,SAAM,CAAC,YAAM;MACblC,MAAM,CAAC;QACLmB,MAAM,EAAE,OAAO;QACfC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,CAAC,WAAQ,CAAC,YAAM;MACf;MACAE,YAAY,CAACL,OAAO,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASe,iBAAiBA,CAACH,SAAS,EAAE;EACpC,OAAO,IAAI/B,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;IACtC+B,KAAK,CAACF,SAAS,CAAC,CAAChb,IAAI,CAAC,UAAC0b,QAAQ,EAAK;MAClCA,QAAQ,CAACL,IAAI,CAAC,CAAC,CAACrb,IAAI,CAAC,UAACqb,IAAI,EAAK;QAC7BnC,OAAO,CAACmC,IAAI,CAAC;MACf,CAAC,CAAC;IACJ,CAAC,CAAC,SAAM,CAAC,YAAM;MACblC,MAAM,CAAC,OAAO,CAAC;IACjB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASiC,eAAeA,CAACJ,SAAS,EAAE;EAClC,OAAO,IAAI/B,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;IACtC,IAAMwC,GAAG,GAAG,IAAIC,cAAc,CAAC,CAAC;IAChCD,GAAG,CAACE,YAAY,GAAG,MAAM;IACzBF,GAAG,CAACjB,MAAM,GAAG,UAAUgB,QAAQ,EAAE;MAC/BxC,OAAO,CAACyC,GAAG,CAACD,QAAQ,CAAC;IACvB,CAAC;IAEDC,GAAG,CAACnB,OAAO,GAAG,YAAY;MACxBrB,MAAM,CAAC,OAAO,CAAC;IACjB,CAAC;IAEDwC,GAAG,CAACG,IAAI,CAAC,KAAK,EAAEd,SAAS,EAAE,IAAI,CAAC;IAChCW,GAAG,CAACI,IAAI,CAAC,CAAC;EACZ,CAAC,CAAC;AACJ;AAEehB,qEAAc,E;;AC7F7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiB,oBAAoBA,CAACC,YAAY,EAAE;EAC1C,IAAMtC,QAAQ,GAAyDsC,YAAY,CAA7EtC,QAAQ;IAAEuC,WAAW,GAA4CD,YAAY,CAAnEC,WAAW;IAAEC,IAAI,GAAsCF,YAAY,CAAtDE,IAAI;IAAEvC,KAAK,GAA+BqC,YAAY,CAAhDrC,KAAK;IAAE3J,MAAM,GAAuBgM,YAAY,CAAzChM,MAAM;IAAEsL,OAAO,GAAcU,YAAY,CAAjCV,OAAO;IAAEa,QAAQ,GAAIH,YAAY,CAAxBG,QAAQ;EAEnE,IAAIppB,EAAE,GAAG0P,QAAQ,CAACiP,aAAa,CAAC,OAAO,CAAC;EACxC3e,EAAE,CAACyK,KAAK,CAAC4e,UAAU,GAAG,QAAQ;EAC9BrpB,EAAE,CAACspB,QAAQ,GAAG,UAAU;EACxBtpB,EAAE,CAACpE,CAAC,GAAG,CAAC;EACRoE,EAAE,CAACnE,CAAC,GAAG,CAAC;EACRmE,EAAE,CAACklB,GAAG,GAAGqD,OAAO;EAChBvoB,EAAE,CAAC8I,YAAY,CAAC,gBAAgB,EAAEsgB,QAAQ,CAAC,CAAC,CAAC;;EAE7CzC,QAAQ,IAAI3mB,EAAE,CAAC8I,YAAY,CAAC,UAAU,EAAE6d,QAAQ,CAAC;EACjDuC,WAAW,IAAIlpB,EAAE,CAAC8I,YAAY,CAAC,aAAa,EAAEogB,WAAW,CAAC;EAC1DC,IAAI,IAAInpB,EAAE,CAAC8I,YAAY,CAAC,MAAM,EAAEqgB,IAAI,CAAC;EACrCvC,KAAK,IAAI5mB,EAAE,CAAC8I,YAAY,CAAC,OAAO,EAAE8d,KAAK,CAAC;EACxCA,KAAK,KAAK5mB,EAAE,CAAC4mB,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC;EAC7B3J,MAAM,IAAIjd,EAAE,CAAC8I,YAAY,CAAC,QAAQ,EAAEmU,MAAM,CAAC;;EAE3C;EACAjd,EAAE,CAAC0nB,MAAM,GAAG,YAAM;IAChBc,GAAG,CAACe,eAAe,CAAChB,OAAO,CAAC;EAC9B,CAAC;EAED,OAAOvoB,EAAE;AACX;AAEegpB,8FAAoB,E;;AClCnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASQ,kBAAkBA,CAACC,YAAY,EAAE3C,cAAc,EAAE4C,WAAW,EAAEC,QAAQ,EAAE;EAC/E,IAAAC,OAAA,GAA0CnqB,MAAM;IAA3CwC,OAAO,GAAA2nB,OAAA,CAAP3nB,OAAO;IAAEolB,UAAU,GAAAuC,OAAA,CAAVvC,UAAU;IAAEI,YAAY,GAAAmC,OAAA,CAAZnC,YAAY;EAEtC,OAAO,IAAIxB,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;IACtC,IAAIiB,OAAO,GAAGC,UAAU,CAAC,YAAM;MAC7BlB,MAAM,CAAC;QAACmB,MAAM,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAwC,CAAC,CAAC;IAC9E,CAAC,EAAET,cAAc,CAAC;IAElB,IAAI7kB,OAAO,EAAE;MACX,IAAI4nB,eAAe,GAAG5nB,OAAO,CAAC6nB,MAAM,CAACL,YAAY,CAAC,CAACM,KAAK,CAAC,YAAM;QAC7D;QACAtC,YAAY,CAACL,OAAO,CAAC;;QAErB;QACA,IAAI4C,aAAa,GAAGH,eAAe,CAACI,SAAS,CAAC,CAAC;QAC/CD,aAAa,CAACvf,KAAK,CAACnI,KAAK,GAAG,MAAM;QAClC0nB,aAAa,CAACzgB,SAAS,IAAI,GAAG,GAAGmgB,WAAW;;QAE5C;QACA,IAAIC,QAAQ,EAAE;UACZE,eAAe,CAACK,IAAI,CAAC,CAAC;QACxB;QAEAhE,OAAO,CAAC2D,eAAe,CAAC;MAC1B,CAAC,CAAC;IACJ,CAAC,MAAM;MACL1D,MAAM,CAAC;QAACmB,MAAM,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAsC,CAAC,CAAC;IAC5E;EACF,CAAC,CAAC;AACJ;AAEeiC,0FAAkB,E;;ACvCa;AACQ;AACI;AACJ;;AAEtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASW,qBAAqBA,CAACpE,eAAe,EAAEqD,QAAQ,EAAElqB,OAAO,EAAE;EACjE,IAAK+d,MAAM,GAAwC/d,OAAO,CAArD+d,MAAM;IAAE0J,QAAQ,GAA8BznB,OAAO,CAA7CynB,QAAQ;IAAEuC,WAAW,GAAiBhqB,OAAO,CAAnCgqB,WAAW;IAAEC,IAAI,GAAWjqB,OAAO,CAAtBiqB,IAAI;IAAEvC,KAAK,GAAI1nB,OAAO,CAAhB0nB,KAAK;EAC/CwC,QAAQ,GAAGA,QAAQ,GAAG,MAAM,CAAC,CAAC;EAC9B,OAAO,IAAInD,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;IACtCa,cAAU,CAAC9nB,OAAO,CAAC6nB,iBAAiB,CAAC9kB,OAAO,EAAE/C,OAAO,CAAC4nB,cAAc,EAAErnB,MAAM,CAACwC,OAAO,CAAC,CAAC+K,IAAI,CAAC,YAAM;MAC/F+a,kBAAc,CAACqB,QAAQ,EAAElqB,OAAO,CAAC4nB,cAAc,CAAC,CAAC9Z,IAAI,CAAC,UAAA0N,IAAA,EAAe;QAAA,IAAb4N,OAAO,GAAA5N,IAAA,CAAP4N,OAAO;QAC7D,IAAImB,YAAY,GAAGT,qCAAoB,CAAC;UACtCT,OAAO,EAAED,OAAO,CAACC,OAAO;UACxBa,QAAQ,EAAEA,QAAQ;UAAE;UACpBnM,MAAM,EAANA,MAAM;UACN0J,QAAQ,EAARA,QAAQ;UACRuC,WAAW,EAAXA,WAAW;UACXC,IAAI,EAAJA,IAAI;UACJvC,KAAK,EAALA;QACF,CAAC,CAAC;QAEFb,eAAe,CAAC6B,WAAW,CAAC6B,YAAY,CAAC;QAEzCD,mCAAkB,CAACC,YAAY,EAAEvqB,OAAO,CAAC4nB,cAAc,EAAE5nB,OAAO,SAAM,EAAEA,OAAO,CAACynB,QAAQ,CAAC,CACtF3Z,IAAI,CAAC,YAAM;UACVkZ,OAAO,CAACH,eAAe,CAAC;QAC1B,CAAC,CAAC,SACI,CAAC,UAACqE,GAAG,EAAK;UACdjE,MAAM,CAACiE,GAAG,CAAC;QACb,CAAC,CAAC;;QAEJ;MACF,CAAC,CAAC,SAAM,CAAC,UAAAzN,KAAA,EAAuB;QAAA,IAArB2K,MAAM,GAAA3K,KAAA,CAAN2K,MAAM;UAAEC,OAAO,GAAA5K,KAAA,CAAP4K,OAAO;QACxBpB,MAAM,CAAC;UAACmB,MAAM,EAANA,MAAM;UAAEC,OAAO,EAAPA;QAAO,CAAC,CAAC;MAC3B,CAAC,CAAC;MACF;IACF,CAAC,CAAC,SAAM,CAAC,UAAA3K,KAAA,EAAuB;MAAA,IAArB0K,MAAM,GAAA1K,KAAA,CAAN0K,MAAM;QAAEC,OAAO,GAAA3K,KAAA,CAAP2K,OAAO;MACxBpB,MAAM,CAAC;QAACmB,MAAM,EAANA,MAAM;QAAEC,OAAO,EAAPA;MAAO,CAAC,CAAC;IAC3B,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAGe4C,gGAAqB,E;;AClDpC;AACA;AACA;AACgC;AAEhC,SAASE,2BAA2BA,CAAA,EAAG;EACrC,OAAO,IAAIpE,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;IACtC;IACA;IACA;IACA,IAAIje,QAAQ,CAAC,CAAC,EAAC;MACbge,OAAO,CAAC,KAAK,CAAC;IAChB;IAEA,IAAM/R,KAAK,GAAGzE,QAAQ,CAACiP,aAAa,CAAC,OAAO,CAAC;IAC7C,IAAM2L,OAAO,GAAGnW,KAAK,CAACoW,WAAW,IAAIpW,KAAK,CAACoW,WAAW,CAAC,0BAA0B,CAAC;IAClFrE,OAAO,CAACoE,OAAO,KAAK,OAAO,IAAIA,OAAO,KAAK,UAAU,CAAC;EACxD,CAAC,CAAC;AACJ;AAEeD,4GAA2B,E;;;;;;;;ACpBqB;AAE/D,IAAIG,gBAAgB,EAAEC,YAAY,EAAEC,kBAAkB,EAAEC,6BAAkB,EAAE/f,mBAAQ,EAAEggB,SAAS;AAEnD;AACP;AACE;AACI;AACF;AACK;AACtB;AACe;AACE;AAezB;AAChB;;AAE+F;AACoB;AACxB;AACY;AAEvGF,kBAAkB,GAAG,SAAAA,mBAASpoB,KAAK,EAAe;EAAA,IAAbuoB,KAAK,GAAA5sB,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,GAAG;EAC9C,OAAO4sB,KAAK,GAAGnI,IAAI,CAACC,IAAI,CAACrgB,KAAK,GAAGuoB,KAAK,CAAC;AACzC,CAAC;AAEDJ,YAAY,GAAG,SAAAA,aAAS7mB,IAAI,EAAE5H,KAAK,EAAE;EACnC,IAAIP,CAAC;EACLA,CAAC,GAAGmI,IAAI,CAAC3I,MAAM,GAAG,CAAC;EACnB,OAAOQ,CAAC,IAAI,CAAC,IAAImI,IAAI,CAACnI,CAAC,CAAC,IAAIO,KAAK,EAAE;IACjCP,CAAC,EAAE;EACL;EACA,OAAOmI,IAAI,CAACnI,CAAC,GAAG,CAAC,CAAC;AACpB,CAAC;AAED+uB,gBAAgB,GAAG,SAAAA,iBAAShM,GAAG,EAAElc,KAAK,EAAEuoB,KAAK,EAAE3rB,OAAO,EAAE;EACtD,IAAImS,GAAG,EAAE2H,IAAI,EAAEC,IAAI,EAAEnI,0BAA0B;EAC/CA,0BAA0B,GAAG,CAACO,GAAG,GAAG,CAAC2H,IAAI,GAAG,CAACC,IAAI,GAAG/Z,OAAO,CAAC,4BAA4B,CAAC,KAAK,IAAI,GAAG+Z,IAAI,GAAG/Z,OAAO,CAAC,2BAA2B,CAAC,KAAK,IAAI,GAAG8Z,IAAI,GAAG,IAAI,CAACzJ,MAAM,CAAC,4BAA4B,CAAC,KAAK,IAAI,GAAG8B,GAAG,GAAG,IAAI,CAAC9B,MAAM,CAAC,2BAA2B,CAAC;EACtQ,IAAK,CAACuB,0BAA0B,IAAMA,0BAA0B,KAAK,QAAQ,IAAI,CAAC5R,OAAO,CAAC4rB,QAAS,EAAE;IACnG,OAAOxoB,KAAK;EACd,CAAC,MAAM;IACL,OAAO,IAAI,CAACyoB,eAAe,CAACvM,GAAG,EAAElc,KAAK,EAAEuoB,KAAK,CAAC;EAChD;AACF,CAAC;AAEDF,6BAAkB,GAAG,SAAAA,mBAAStiB,OAAO,EAAE;EACrC,IAAI2iB,cAAc,EAAEvgB,KAAK;EACzBugB,cAAc,GAAG,CAAC;EAClB,OAAQ,CAAC3iB,OAAO,GAAGA,OAAO,IAAI,IAAI,GAAGA,OAAO,CAACkC,UAAU,GAAG,KAAK,CAAC,aAAa0gB,OAAO,IAAK,CAACD,cAAc,EAAE;IACxGvgB,KAAK,GAAGhL,MAAM,CAACsK,gBAAgB,CAAC1B,OAAO,CAAC;IACxC,IAAI,CAAC,SAAS,CAACN,IAAI,CAAC0C,KAAK,CAACygB,OAAO,CAAC,EAAE;MAClCF,cAAc,GAAG1oB,YAAK,CAAC+F,OAAO,CAAC;IACjC;EACF;EACA,OAAO2iB,cAAc;AACvB,CAAC;AAEDJ,SAAS,GAAG,SAAAA,UAAS9L,OAAO,EAAEqM,QAAQ,EAAE;EACtC,OAAOrM,OAAO,CAACta,OAAO,CAAC,sBAAsB,EAAE,MAAM,GAAG,IAAI,CAAC4mB,kBAAkB,CAACD,QAAQ,CAAC,CAAC;AAC5F,CAAC;AAEDvgB,mBAAQ,GAAG,SAAAA,SAASygB,aAAa,EAAE7M,GAAG,EAAE;EACtC,IAAI8M,UAAU;EACdA,UAAU,GAAGljB,cAAO,CAACoW,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC;EACvC,IAAI6M,aAAa,GAAGC,UAAU,EAAE;IAC9BA,UAAU,GAAGD,aAAa;IAC1BxiB,cAAO,CAAC2V,GAAG,EAAE,OAAO,EAAE6M,aAAa,CAAC;EACtC;EACA,OAAOC,UAAU;AACnB,CAAC;AAAC,IAEIC,qBAAU;EACd;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAAA,WAAYrsB,OAAO,EAAE;IAAAiN,yBAAA,OAAAof,UAAA;IACnB,IAAIzc,aAAa;IACjB,IAAI,CAAC0c,qBAAqB,GAAG,CAAC,CAAC;IAC/B,IAAI,CAACC,gBAAgB,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,2BAA2B,GAAG,KAAK;IACxC5c,aAAa,GAAG,IAAID,iBAAa,CAAC3P,OAAO,CAAC;IAC1C;IACA,IAAI,CAACqQ,MAAM,GAAG,UAASoc,SAAS,EAAEC,QAAQ,EAAE;MAC1C,OAAO9c,aAAa,CAACS,MAAM,CAACoc,SAAS,EAAEC,QAAQ,CAAC;IAClD,CAAC;IACD;AACJ;AACA;AACA;IACI,IAAI,CAACzc,YAAY,GAAG,YAAW;MAC7BL,aAAa,CAACK,YAAY,CAAC,CAAC;MAC5B,OAAO,IAAI;IACb,CAAC;IACD;AACJ;AACA;AACA;IACI,IAAI,CAACD,eAAe,GAAG,YAAW;MAChCJ,aAAa,CAACI,eAAe,CAAC,CAAC;MAC/B,OAAO,IAAI;IACb,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACD,IAAI,GAAG,YAAW;MACrBH,aAAa,CAACG,IAAI,CAAC,CAAC;MACpB,OAAO,IAAI;IACb,CAAC;EACH;;EAEA;AACF;AACA;AACA;AACA;AACA;EALE,OAAA3C,sBAAA,CAAAif,UAAA;IAAA3uB,GAAA;IAAAZ,KAAA;IAUA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACE,SAAAiL,IAAIsK,QAAQ,EAAgB;MAAA,IAAdrS,OAAO,GAAAjB,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;MACxB,OAAOgJ,OAAG,CAACsK,QAAQ,EAAErS,OAAO,EAAE,IAAI,CAACqQ,MAAM,CAAC,CAAC,CAAC;IAC9C;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAZE;IAAA3S,GAAA;IAAAZ,KAAA,EAaA,SAAA6vB,UAAUta,QAAQ,EAAErS,OAAO,EAAE;MAC3BA,OAAO,GAAGsQ,4BAAM,CAAC;QACftO,aAAa,EAAE;MACjB,CAAC,EAAEhC,OAAO,CAAC;MACX,OAAO,IAAI,CAAC+H,GAAG,CAACsK,QAAQ,EAAErS,OAAO,CAAC;IACpC;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAhBE;IAAAtC,GAAA;IAAAZ,KAAA,EAiBA,SAAA8vB,oBAAoBva,QAAQ,EAAErS,OAAO,EAAE;MACrCA,OAAO,GAAGsQ,4BAAM,CAAC,CAAC,CAAC,EAAEuc,sBAAgC,EAAE7sB,OAAO,CAAC;MAC/D,OAAO,IAAI,CAAC+H,GAAG,CAACsK,QAAQ,EAAErS,OAAO,CAAC;IACpC;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EATE;IAAAtC,GAAA;IAAAZ,KAAA,EAUA,SAAAgwB,sBAAsB9sB,OAAO,EAAE;MAC7B,OAAO,IAAIoW,kBAAc,CAACpW,OAAO,CAAC,CAACqN,SAAS,CAAC,CAAC;IAChD;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAXE;IAAA3P,GAAA;IAAAZ,KAAA,EAYA,SAAAiwB,MAAM1a,QAAQ,EAAgB;MAAA,IAAdrS,OAAO,GAAAjB,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;MAC1B,IAAIomB,YAAY,EAAE6H,GAAG,EAAE7a,GAAG;MAC1B6a,GAAG,GAAG,IAAI,CAACC,QAAQ,CAAC5a,QAAQ,EAAErS,OAAO,CAAC;MACtCmlB,YAAY,GAAG,CAAChT,GAAG,GAAGnS,OAAO,CAACmlB,YAAY,IAAI,IAAI,GAAGnlB,OAAO,CAACmlB,YAAY,GAAG,IAAI,CAAC9U,MAAM,CAAC,cAAc,CAAC,KAAK,IAAI,GAAG8B,GAAG,GAAG,KAAK;MAC9H,IAAInS,OAAO,CAACgmB,GAAG,IAAI,IAAI,IAAI,CAACb,YAAY,EAAE;QACxC;QACA6H,GAAG,CAACnjB,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;MACxB;MACAmjB,GAAG,GAAGA,GAAG,CAACxN,KAAK,CAAC,CAAC;MACjB,IAAI,CAAC2F,YAAY,EAAE;QACjB;QACAxb,cAAO,CAACqjB,GAAG,EAAE,WAAW,EAAE,IAAI,CAACjlB,GAAG,CAACsK,QAAQ,EAAErS,OAAO,CAAC,CAAC;QACtD;QACA,IAAI,CAACktB,iBAAiB,CAACF,GAAG,EAAEhtB,OAAO,CAAC;MACtC;MACA,OAAOgtB,GAAG;IACZ;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAXE;IAAAtvB,GAAA;IAAAZ,KAAA,EAYA,SAAAmwB,SAAS5a,QAAQ,EAAErS,OAAO,EAAE;MAC1B,IAAIsf,GAAG;MACPA,GAAG,GAAG,IAAIyF,QAAQ,CAAC1S,QAAQ,EAAE,IAAI,CAAChC,MAAM,CAAC,CAAC,CAAC;MAC3CiP,GAAG,CAACld,cAAc,CAAC,CAAC,CAAC0V,WAAW,CAAC9X,OAAO,CAAC;MACzC,OAAOsf,GAAG;IACZ;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EAPE;IAAA5hB,GAAA;IAAAZ,KAAA,EAQA,SAAAqwB,WAAW9a,QAAQ,EAAErS,OAAO,EAAE4F,OAAO,EAAE;MACrC,IAAI0Z,GAAG;MACPA,GAAG,GAAG,IAAIgG,UAAU,CAACjT,QAAQ,EAAE,IAAI,CAAChC,MAAM,CAAC,CAAC,EAAEzK,OAAO,CAAC;MACtD0Z,GAAG,CAACld,cAAc,CAAC,CAAC,CAAC0V,WAAW,CAAC9X,OAAO,CAAC;MACzC,OAAOsf,GAAG;IACZ;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;EANE;IAAA5hB,GAAA;IAAAZ,KAAA,EAOA,SAAAswB,UAAU/a,QAAQ,EAAErS,OAAO,EAAE;MAC3B,IAAIsf,GAAG;MACPA,GAAG,GAAG,IAAI8F,SAAS,CAAC/S,QAAQ,EAAE,IAAI,CAAChC,MAAM,CAAC,CAAC,CAAC;MAC5CiP,GAAG,CAACld,cAAc,CAAC,CAAC,CAAC0V,WAAW,CAAC9X,OAAO,CAAC;MACzC,OAAOsf,GAAG;IACZ;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAbE;IAAA5hB,GAAA;IAAAZ,KAAA,EAcA,SAAAuwB,gBAAgBhb,QAAQ,EAAErS,OAAO,EAAE;MACjC,OAAO,IAAI,CAAC+sB,KAAK,CAAC1a,QAAQ,EAAEjC,0BAAK,CAAC,CAAC,CAAC,EAAEyc,sBAAgC,EAAE7sB,OAAO,CAAC,CAAC;IACnF;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAZE;IAAAtC,GAAA;IAAAZ,KAAA,EAaA,SAAAwwB,uBAAuBjb,QAAQ,EAAErS,OAAO,EAAE;MACxC,OAAO,IAAI,CAAC+sB,KAAK,CAAC1a,QAAQ,EAAE/B,4BAAM,CAAC;QACjCjO,IAAI,EAAE;MACR,CAAC,EAAErC,OAAO,CAAC,CAAC;IACd;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAZE;IAAAtC,GAAA;IAAAZ,KAAA,EAaA,SAAAywB,sBAAsBlb,QAAQ,EAAErS,OAAO,EAAE;MACvC,OAAO,IAAI,CAAC+sB,KAAK,CAAC1a,QAAQ,EAAE/B,4BAAM,CAAC;QACjCjO,IAAI,EAAE;MACR,CAAC,EAAErC,OAAO,CAAC,CAAC;IACd;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAZE;IAAAtC,GAAA;IAAAZ,KAAA,EAaA,SAAA0wB,2BAA2Bnb,QAAQ,EAAErS,OAAO,EAAE;MAC5C,OAAO,IAAI,CAAC+sB,KAAK,CAAC1a,QAAQ,EAAE/B,4BAAM,CAAC;QACjCjO,IAAI,EAAE;MACR,CAAC,EAAErC,OAAO,CAAC,CAAC;IACd;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAbE;IAAAtC,GAAA;IAAAZ,KAAA,EAcA,SAAA2wB,eAAepb,QAAQ,EAAErS,OAAO,EAAE;MAChC,OAAO,IAAI,CAAC+sB,KAAK,CAAC1a,QAAQ,EAAE/B,4BAAM,CAAC;QACjCjO,IAAI,EAAE;MACR,CAAC,EAAErC,OAAO,CAAC,CAAC;IACd;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAXE;IAAAtC,GAAA;IAAAZ,KAAA,EAYA,SAAA4wB,YAAYrb,QAAQ,EAAErS,OAAO,EAAE;MAC7B,OAAO,IAAI,CAAC+sB,KAAK,CAAC1a,QAAQ,EAAE/B,4BAAM,CAAC;QACjCjO,IAAI,EAAE;MACR,CAAC,EAAErC,OAAO,CAAC,CAAC;IACd;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAXE;IAAAtC,GAAA;IAAAZ,KAAA,EAYA,SAAAmY,MAAM5C,QAAQ,EAAgB;MAAA,IAAdrS,OAAO,GAAAjB,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;MAC1B,OAAO,IAAI,CAACooB,QAAQ,CAAC9U,QAAQ,EAAErS,OAAO,CAAC,CAAC+a,MAAM,CAAC,CAAC;IAClD;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAXE;IAAArd,GAAA;IAAAZ,KAAA,EAYA,SAAAqqB,SAAS9U,QAAQ,EAAErS,OAAO,EAAE;MAC1BA,OAAO,GAAGyF,QAAQ,CAAC,CAAC,CAAC,EAAEzF,OAAO,EAAE,IAAI,CAACqQ,MAAM,CAAC,CAAC,CAAC;MAC9C,OAAO,IAAIoV,QAAQ,CAACpT,QAAQ,EAAErS,OAAO,CAAC;IACxC;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAVE;IAAAtC,GAAA;IAAAZ,KAAA,EAWA,SAAA6wB,WAAWtb,QAAQ,EAAErS,OAAO,EAAE;MAC5BA,OAAO,GAAGsQ,4BAAM,CAAC;QACfjO,IAAI,EAAE;MACR,CAAC,EAAErC,OAAO,CAAC;MACX,IAAI,CAACqS,QAAQ,CAAC3T,KAAK,CAAC,OAAO,CAAC,EAAE;QAC5BsB,OAAO,CAAC+B,MAAM,GAAG,KAAK;MACxB;MACA,OAAO,IAAI,CAACgG,GAAG,CAACsK,QAAQ,EAAErS,OAAO,CAAC;IACpC;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAjBE;IAAAtC,GAAA;IAAAZ,KAAA,EAkBA,SAAAsD,WAAWJ,OAAO,EAAoB;MAAA,IAAA0Q,KAAA;MAAA,IAAlBkd,SAAS,GAAA7uB,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,IAAI;MAClC,IAAIoT,GAAG,EAAE2H,IAAI,EAAEC,IAAI,EAAE4F,eAAe,EAAEkO,gBAAgB,EAAEC,OAAO;MAC/D,IAAI,CAACvB,gBAAgB,GAAGnc,0BAAK,CAAC,IAAI,CAACmc,gBAAgB,IAAI,CAAC,CAAC,EAAEvsB,OAAO,CAAC;MACnE2f,eAAe,GAAG,CAACxN,GAAG,GAAG,IAAI,CAACoa,gBAAgB,CAAC5a,gBAAgB,KAAK,IAAI,GAAGQ,GAAG,GAAG,IAAI,CAAC9B,MAAM,CAAC,kBAAkB,CAAC;MAChH,IAAIud,SAAS,EAAE;QACb,IAAI,CAACV,iBAAiB,QAAAttB,MAAA,CAAQ+f,eAAe,sBAAmB,IAAI,CAAC4M,gBAAgB,CAAC;MACxF;MACAsB,gBAAgB,GAAG,CAAC/T,IAAI,GAAG,CAACC,IAAI,GAAG,IAAI,CAACwS,gBAAgB,CAACwB,iBAAiB,KAAK,IAAI,GAAGhU,IAAI,GAAG,IAAI,CAAC1J,MAAM,CAAC,mBAAmB,CAAC,KAAK,IAAI,GAAGyJ,IAAI,GAAG,IAAI;MACpJ,IAAI+T,gBAAgB,IAAI,CAAC,IAAI,CAACrB,2BAA2B,EAAE;QACzD,IAAI,CAACD,gBAAgB,CAACX,QAAQ,GAAG,IAAI,CAACY,2BAA2B,GAAG,IAAI;QACxEsB,OAAO,GAAG,IAAI;QACd,IAAME,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;UAC3B,IAAIC,QAAQ,EAAEjU,IAAI,EAAEC,IAAI,EAAEiU,KAAK,EAAEC,GAAG,EAAEC,IAAI,EAAEC,QAAQ;UACpDJ,QAAQ,GAAG,CAACjU,IAAI,GAAG,CAACC,IAAI,GAAGvJ,KAAI,CAAC6b,gBAAgB,CAAC+B,mBAAmB,KAAK,IAAI,GAAGrU,IAAI,GAAGvJ,KAAI,CAACL,MAAM,CAAC,qBAAqB,CAAC,KAAK,IAAI,GAAG2J,IAAI,GAAG,GAAG;UAC/IkU,KAAK,GAAG,SAAAA,MAAA,EAAW;YACjB,IAAIJ,OAAO,EAAE;cACXvF,YAAY,CAACuF,OAAO,CAAC;cACrBA,OAAO,GAAG,IAAI;YAChB;UACF,CAAC;UACDK,GAAG,GAAG,SAAAA,IAAA,EAAM;YACV,OAAOzd,KAAI,CAACwc,iBAAiB,QAAAttB,MAAA,CAAQ+f,eAAe,GAAIjP,KAAI,CAAC6b,gBAAgB,CAAC;UAChF,CAAC;UACD8B,QAAQ,GAAG,SAAAA,SAAA,EAAW;YACpBH,KAAK,CAAC,CAAC;YACP,OAAOC,GAAG,CAAC,CAAC;UACd,CAAC;UACDC,IAAI,GAAG,SAAAA,KAAA,EAAW;YAChBF,KAAK,CAAC,CAAC;YACPJ,OAAO,GAAG3F,UAAU,CAACkG,QAAQ,EAAEJ,QAAQ,CAAC;UAC1C,CAAC;UACD,IAAIA,QAAQ,EAAE;YACZ,OAAOG,IAAI,CAAC,CAAC;UACf,CAAC,MAAM;YACL,OAAOD,GAAG,CAAC,CAAC;UACd;QACF,CAAC;QACD5tB,MAAM,CAACguB,gBAAgB,CAAC,QAAQ,EAAEP,cAAc,CAAC;QACjD,OAAO;UAAA,OAAIztB,MAAM,CAACiuB,mBAAmB,CAAC,QAAQ,EAAER,cAAc,CAAC;QAAA;MACjE;IACF;;IAEA;AACF;AACA;AACA;AACA;EAJE;IAAAtwB,GAAA;IAAAZ,KAAA,EAKA,SAAA+uB,gBAAgB1iB,OAAO,EAAE/F,KAAK,EAAEuoB,KAAK,EAAE;MACrC,IAAI5I,WAAW,GAAG7Z,cAAO,CAACC,OAAO,EAAE,aAAa,CAAC,IAAID,cAAO,CAACC,OAAO,EAAE,YAAY,CAAC,IAAI,IAAI,CAACkH,MAAM,CAAC,aAAa,CAAC,IAAI,IAAI,CAACA,MAAM,CAAC,YAAY,CAAC,IAAImb,kBAAkB;MACpK,IAAIllB,oCAAU,CAACyc,WAAW,CAAC,EAAE;QAC3B,OAAOA,WAAW,CAAC3f,KAAK,EAAEuoB,KAAK,CAAC;MAClC,CAAC,MAAM;QACL,IAAI/mB,gCAAQ,CAACme,WAAW,CAAC,EAAE;UACzBA,WAAW,GAAGA,WAAW,CAACjlB,KAAK,CAAC,GAAG,CAAC,CAACR,GAAG,CAAC,UAAAmxB,KAAK;YAAA,OAAEjwB,QAAQ,CAACiwB,KAAK,CAAC;UAAA,EAAC,CAAC9V,IAAI,CAAC,UAAC5N,CAAC,EAAEC,CAAC;YAAA,OAAKD,CAAC,GAAGC,CAAC;UAAA,EAAC;QACxF;QACA,OAAOugB,YAAY,CAACxI,WAAW,EAAE3f,KAAK,CAAC;MACzC;IACF;;IAEA;AACF;AACA;AACA;AACA;AACA;EALE;IAAA1F,GAAA;IAAAZ,KAAA,EAMA,SAAA4xB,eAAevlB,OAAO,EAAE/F,KAAK,EAAEuoB,KAAK,EAAE;MACpC,OAAO,IAAI,CAACE,eAAe,CAAC1iB,OAAO,EAAE/F,KAAK,EAAEuoB,KAAK,CAAC;IACpD;;IAEA;AACF;AACA;AACA;EAHE;IAAAjuB,GAAA;IAAAZ,KAAA,EAIA,SAAAovB,mBAAmBD,QAAQ,EAAE;MAC3BA,QAAQ,GAAGA,QAAQ,IAAI,IAAI,GAAG,IAAI,GAAGA,QAAQ;MAC7C,IAAI1P,GAAG,GAAG,CAAC,OAAOhc,MAAM,KAAK,WAAW,IAAIA,MAAM,KAAK,IAAI,GAAGA,MAAM,CAACouB,gBAAgB,GAAG,KAAK,CAAC,KAAK,CAAC;MACpG,IAAI1C,QAAQ,EAAE;QACZ1P,GAAG,GAAGiH,IAAI,CAACC,IAAI,CAAClH,GAAG,CAAC;MACtB;MACA,IAAIA,GAAG,IAAI,CAAC,IAAIA,GAAG,KAAM,CAAC,GAAC,CAAE,EAAE;QAC7BA,GAAG,GAAG,CAAC;MACT;MACA,IAAIqS,SAAS,GAAGrS,GAAG,CAAC5e,QAAQ,CAAC,CAAC;MAC9B,IAAIixB,SAAS,CAAClwB,KAAK,CAAC,OAAO,CAAC,EAAE;QAC5BkwB,SAAS,IAAI,IAAI;MACnB;MACA,OAAOA,SAAS;IAClB;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EARE;IAAAlxB,GAAA;IAAAZ,KAAA,EASA,SAAA+xB,iBAAiBC,KAAK,EAAE9uB,OAAO,EAAE;MAC/B,IAAIsH,OAAO,CAACwnB,KAAK,CAAC,EAAE;QAClB;QACA,OAAO,IAAI;MACb;MAEA9uB,OAAO,GAAGyF,QAAQ,CAAC,CAAC,CAAC,EAAEzF,OAAO,IAAI,CAAC,CAAC,EAAE,IAAI,CAACqQ,MAAM,CAAC,CAAC,CAAC;MACpD,IAAI0e,MAAM,GAAGD,KAAK,CACfxqB,MAAM,CAAC,UAAA0qB,IAAI;QAAA,OAAE,QAAQ,CAACnmB,IAAI,CAACmmB,IAAI,CAACC,OAAO,CAAC;MAAA,EAAC,CACzC3xB,GAAG,CAAC,UAAS0xB,IAAI,EAAC;QACf,IAAIE,UAAU,GAAG5e,4BAAM,CAAC;UACtBlN,KAAK,EAAE4rB,IAAI,CAAC3lB,YAAY,CAAC,OAAO,CAAC;UACjC7F,MAAM,EAAEwrB,IAAI,CAAC3lB,YAAY,CAAC,QAAQ,CAAC;UACnC2c,GAAG,EAAEgJ,IAAI,CAAC3lB,YAAY,CAAC,KAAK;QAC9B,CAAC,EAAErJ,OAAO,CAAC;QACX,IAAIqS,QAAQ,GAAG6c,UAAU,CAAC,QAAQ,CAAC,IAAIA,UAAU,CAAC,KAAK,CAAC;QACxD,OAAOA,UAAU,CAAC,QAAQ,CAAC;QAC3B,OAAOA,UAAU,CAAC,KAAK,CAAC;QACxB,IAAIplB,IAAI,GAAG,IAAIsM,kBAAc,CAAC8Y,UAAU,CAAC,CAACvU,gBAAgB,CAAC,CAAC;QAC5DhR,cAAO,CAACqlB,IAAI,EAAE,WAAW,EAAEjnB,OAAG,CAACsK,QAAQ,EAAE6c,UAAU,CAAC,CAAC;QACrDF,IAAI,CAACplB,YAAY,CAAC,OAAO,EAAEE,IAAI,CAAC1G,KAAK,CAAC;QACtC4rB,IAAI,CAACplB,YAAY,CAAC,QAAQ,EAAEE,IAAI,CAACtG,MAAM,CAAC;QACxC,OAAOwrB,IAAI;MACf,CAAC,CAAC;MACJ,IAAI,CAAC9B,iBAAiB,CAAC6B,MAAM,EAAE/uB,OAAO,CAAC;MACvC,OAAO,IAAI;IACb;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAlBE;IAAAtC,GAAA;IAAAZ,KAAA,EAmBA,SAAAowB,kBAAkBvG,QAAQ,EAAE3mB,OAAO,EAAE;MAAA,IAAA4V,MAAA;MACnC,IAAIkW,cAAc,EAAElM,OAAO,EAAElhB,KAAK,EAAEub,IAAI,EAAEkS,aAAa;MACvD,IAAIxF,QAAQ,KAAK,IAAI,EAAE;QACrB,OAAO,IAAI;MACb;MACA,IAAG3mB,OAAO,IAAI,IAAI,EAAE;QAClBA,OAAO,GAAG,CAAC,CAAC;MACd;MACA,IAAMI,UAAU,GAAGJ,OAAO,CAACI,UAAU,IAAI,IAAI,GAAGJ,OAAO,CAACI,UAAU,GAAG,IAAI,CAACiQ,MAAM,CAAC,YAAY,CAAC;MAE9FsW,QAAQ,GAAGD,gBAAgB,CAACC,QAAQ,CAAC;MAErC,IAAIhH,eAAe;MACnB,IAAI,IAAI,CAAC4M,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAAC5a,gBAAgB,IAAI,IAAI,EAAE;QAC3EgO,eAAe,GAAG,IAAI,CAAC4M,gBAAgB,CAAC5a,gBAAgB;MAC1D,CAAC,MAAM,IAAI3R,OAAO,CAAC2R,gBAAgB,IAAI,IAAI,EAAE;QAC3CgO,eAAe,GAAG3f,OAAO,CAAC2R,gBAAgB;MAC5C,CAAC,MAAM;QACLgO,eAAe,GAAG,IAAI,CAACtP,MAAM,CAAC,kBAAkB,CAAC;MACnD;MAEA,IAAI4b,QAAQ,GAAGjsB,OAAO,CAAC6R,SAAS,IAAI,IAAI,GAAG7R,OAAO,CAAC6R,SAAS,GAAG,IAAI,CAACxB,MAAM,CAAC,WAAW,CAAC;MACvFsW,QAAQ,CAACnpB,OAAO,CAAC,UAAA8hB,GAAG,EAAI;QACtB,IAAI,MAAM,CAACzW,IAAI,CAACyW,GAAG,CAAC2P,OAAO,CAAC,EAAE;UAC5B,IAAIE,MAAM,GAAG,IAAI;UACjB,IAAI/uB,UAAU,EAAE;YACdkK,eAAQ,CAACgV,GAAG,EAAEK,eAAe,CAAC;UAChC;UACAC,OAAO,GAAG1W,cAAO,CAACoW,GAAG,EAAE,WAAW,CAAC,IAAIpW,cAAO,CAACoW,GAAG,EAAE,KAAK,CAAC;UAC1D,IAAI,CAAChY,OAAO,CAACsY,OAAO,CAAC,EAAE;YACrB;YACAA,OAAO,GAAG8L,SAAS,CAACnlB,IAAI,CAACqP,MAAI,EAAEgK,OAAO,EAAEqM,QAAQ,CAAC;YACjD,IAAIvN,OAAO,CAACgB,YAAY,CAACJ,GAAG,EAAEK,eAAe,CAAC,EAAE;cAC9CmM,cAAc,GAAGL,6BAAkB,CAACnM,GAAG,CAAC;cACxC,IAAIwM,cAAc,KAAK,CAAC,EAAE;gBACxB,IAAI,oBAAoB,CAACjjB,IAAI,CAAC+W,OAAO,CAAC,EAAE;kBACtCuM,aAAa,GAAGzgB,mBAAQ,CAACogB,cAAc,EAAExM,GAAG,CAAC;kBAC7C,IAAI6M,aAAa,EAAE;oBACjBvM,OAAO,GAAGA,OAAO,CAACta,OAAO,CAAC,uCAAuC,0BAAA1F,MAAA,CAA0BusB,aAAa,CAAE,CAAC;kBAC7G,CAAC,MAAM;oBACLgD,MAAM,GAAG,KAAK;kBAChB;gBACF,CAAC,MAAM;kBACLzwB,KAAK,GAAG,iBAAiB,CAACwS,IAAI,CAAC0O,OAAO,CAAC;kBACvC,IAAIlhB,KAAK,EAAE;oBACTytB,aAAa,GAAGb,gBAAgB,CAAC/kB,IAAI,CAACqP,MAAI,EAAE0J,GAAG,EAAEwM,cAAc,EAAEptB,KAAK,CAAC,CAAC,CAAC,EAAEsB,OAAO,CAAC;oBACnFmsB,aAAa,GAAGzgB,mBAAQ,CAACygB,aAAa,EAAE7M,GAAG,CAAC;oBAC5C,IAAI6M,aAAa,EAAE;sBACjBvM,OAAO,GAAGA,OAAO,CAACta,OAAO,CAAC,gBAAgB,OAAA1F,MAAA,CAAOusB,aAAa,CAAE,CAAC;oBACnE,CAAC,MAAM;sBACLgD,MAAM,GAAG,KAAK;oBAChB;kBACF;gBACF;gBACAplB,sBAAe,CAACuV,GAAG,EAAE,OAAO,CAAC;gBAC7B,IAAI,CAACtf,OAAO,CAACovB,0BAA0B,EAAE;kBACvCrlB,sBAAe,CAACuV,GAAG,EAAE,QAAQ,CAAC;gBAChC;cACF,CAAC,MAAM;gBACL;gBACA6P,MAAM,GAAG,KAAK;cAChB;YACF;YACA,IAAME,aAAa,GAAIrvB,OAAO,CAACG,OAAO,KAAK,MAAM,IAAI,CAACyV,MAAI,CAAClV,yBAAyB,CAAC,CAAC,IAAIkV,MAAI,CAAC0Z,mBAAmB,CAAC,CAAC,IAAI,CAAC3I,QAAQ,CAAC,CAAC,CAAC,CAACtd,YAAY,CAAC,KAAK,CAAE;YACzJ,IAAI8lB,MAAM,IAAIE,aAAa,EAAC;cAC1B;cACAzZ,MAAI,CAAC2Z,oBAAoB,CAAC5I,QAAQ,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,YAAY,CAAC;YAC/D;YAEA,IAAIwI,MAAM,IAAI,CAACE,aAAa,EAAE;cAC5BzlB,mBAAY,CAAC0V,GAAG,EAAE,KAAK,EAAEM,OAAO,CAAC;YACnC;UACF;QACF;MACF,CAAC,CAAC;MACF,OAAO,IAAI;IACb;;IAEA;AACF;AACA;AACA;AACA;AACA;EALE;IAAAliB,GAAA;IAAAZ,KAAA,EAMA,SAAAyyB,qBAAqBpmB,OAAO,EAAE4V,WAAW,EAAEyQ,aAAa,EAAC;MACvD,IAAMC,cAAc,GAAGtmB,OAAO,CAACE,YAAY,CAACmmB,aAAa,CAAC;MAC1D,IAAIC,cAAc,IAAI,IAAI,EAAE;QAC1B7lB,mBAAY,CAACT,OAAO,EAAE4V,WAAW,EAAE0Q,cAAc,CAAC;MACpD;IACF;;IAEA;AACF;AACA;AACA;EAHE;IAAA/xB,GAAA;IAAAZ,KAAA,EAIA,SAAAwyB,oBAAA,EAAsB;MACpB,OAAO/uB,MAAM,IAAI,sBAAsB,IAAIA,MAAM;IACnD;;IAEA;AACF;AACA;AACA;EAHE;IAAA7C,GAAA;IAAAZ,KAAA,EAIA,SAAA4D,0BAAA,EAA4B;MAC1B,OAAO,SAAS,IAAIC,gBAAgB,CAACC,SAAS;IAChD;;IAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAVE;IAAAlD,GAAA;IAAAZ,KAAA,EAWA,SAAAsF,eAAepC,OAAO,EAAE;MACtB,OAAOoW,kBAAc,OAAI,CAAC,IAAI,CAAC/F,MAAM,CAAC,CAAC,CAAC,CAACyH,WAAW,CAAC9X,OAAO,CAAC,CAACwN,SAAS,CAAC,IAAI,CAAC;IAC/E;;IAGA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAXE;IAAA9P,GAAA;IAAAZ,KAAA,EAYA,SAAA4yB,8BAA8B7I,eAAe,EAAExU,QAAQ,EAAgB;MAAA,IAAA4D,MAAA;MAAA,IAAdjW,OAAO,GAAAjB,SAAA,CAAAhD,MAAA,QAAAgD,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,CAAC,CAAC;MACnE,OAAO,IAAIgoB,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;QACtC,IAAI,CAACJ,eAAe,EAAE;UACpBI,MAAM,CAAC;YAACmB,MAAM,EAAE,OAAO;YAAEC,OAAO,EAAE;UAA6C,CAAC,CAAC;QACnF;QAEAb,kDAAiC,CAACxnB,OAAO,CAAC;QAE1C,IAAIkqB,QAAQ,GAAGjU,MAAI,CAAC0W,SAAS,CAACta,QAAQ,EAAErS,OAAO,CAAC;QAEhDmrB,4CAA2B,CAAC,CAAC,CAACrd,IAAI,CAAC,UAAC6hB,qBAAqB,EAAK;UAC5D,IAAIC,YAAY;UAEhB,IAAID,qBAAqB,EAAE;YACzBC,YAAY,GAAGhJ,wCAAuB,CAACC,eAAe,EAAE5Q,MAAI,EAAE5D,QAAQ,EAAErS,OAAO,CAAC;YAChFgnB,OAAO,CAACH,eAAe,CAAC;UAC1B,CAAC,MAAM;YACL+I,YAAY,GAAG3E,sCAAqB,CAACpE,eAAe,EAAEqD,QAAQ,EAAElqB,OAAO,CAAC;UAC1E;UAEA4vB,YAAY,CACT9hB,IAAI,CAAC,YAAM;YACZkZ,OAAO,CAACH,eAAe,CAAC;UAC1B,CAAC,CAAC,SAAM,CAAC,UAAArL,IAAA,EAAuB;YAAA,IAArB4M,MAAM,GAAA5M,IAAA,CAAN4M,MAAM;cAAEC,OAAO,GAAA7M,IAAA,CAAP6M,OAAO;YAAQpB,MAAM,CAAC;cAACmB,MAAM,EAANA,MAAM;cAAEC,OAAO,EAAPA;YAAO,CAAC,CAAC;UAAC,CAAC,CAAC;;UAE9D;QACF,CAAC,CAAC,SAAM,CAAC,UAAA5K,KAAA,EAAuB;UAAA,IAArB2K,MAAM,GAAA3K,KAAA,CAAN2K,MAAM;YAAEC,OAAO,GAAA5K,KAAA,CAAP4K,OAAO;UAAQpB,MAAM,CAAC;YAACmB,MAAM,EAANA,MAAM;YAAEC,OAAO,EAAPA;UAAO,CAAC,CAAC;QAAC,CAAC,CAAC;MAChE,CAAC,CAAC;IACJ;EAAC;IAAA3qB,GAAA;IAAAZ,KAAA,EA9oBD,SAAAoR,KAAWlO,OAAO,EAAE;MAClB,OAAO,IAAI,IAAI,CAACA,OAAO,CAAC;IAC1B;EAAC;AAAA;AA+oBHsQ,4BAAM,CAAC+b,qBAAU,EAAEQ,yBAAS,CAAC;AACdR,oEAAU,E;;AClyBzB;AACA;AACA;AACyC;AACZ;AACG;AACe;AACV;AACQ;AACN;AACD;AACE;AACI;AACJ;AACoB;AACzB;AACU;AACF;AACU;AACd;AAExB;EACb5F,kBAAkB,EAAlBA,kBAAkB;EAClB4F,UAAU,EAAVA,UAAU;EACVjd,SAAS,EAATA,SAAS;EACTO,aAAa,EAAbA,iBAAa;EACbvT,KAAK,EAALA,SAAK;EACL2Q,UAAU,EAAVA,UAAU;EACVsH,UAAU,EAAVA,UAAU;EACVqK,OAAO,EAAPA,OAAO;EACPqG,QAAQ,EAARA,QAAQ;EACR7S,KAAK,EAALA,WAAK;EACLoT,UAAU,EAAVA,UAAU;EACVnR,cAAc,EAAdA,cAAc;EACdvB,SAAS,EAATA,SAAS;EACTwD,cAAc,EAAdA,kBAAc;EACd/a,WAAW,EAAXA,eAAW;EACXw0B,IAAI,EAAJA,sBAAI;EACJpK,QAAQ,EAARA,QAAQA;AACV,CAAC,EAAC;;;;;;;;ACvCF,2D;;;;;;;ACAA,8D;;;;;;;ACAA,4D;;;;;;;ACAA,+D;;;;;;;ACAA,8D;;;;;;;ACAA,6D;;;;;;;ACAA,6D;;;;;;;ACAA,4D;;;;;;;ACAA,8D;;;;;;;ACAA,+D;;;;;;;ACAA,kE;;;;;;;ACAA,6D;;;;;;;ACAA,0D;;;;;;;ACAA,yD", "file": "./cloudinary-core.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"lodash/assign\"), require(\"lodash/cloneDeep\"), require(\"lodash/compact\"), require(\"lodash/difference\"), require(\"lodash/functions\"), require(\"lodash/identity\"), require(\"lodash/includes\"), require(\"lodash/isArray\"), require(\"lodash/isElement\"), require(\"lodash/isFunction\"), require(\"lodash/isPlainObject\"), require(\"lodash/isString\"), require(\"lodash/merge\"), require(\"lodash/trim\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([\"lodash/assign\", \"lodash/cloneDeep\", \"lodash/compact\", \"lodash/difference\", \"lodash/functions\", \"lodash/identity\", \"lodash/includes\", \"lodash/isArray\", \"lodash/isElement\", \"lodash/isFunction\", \"lodash/isPlainObject\", \"lodash/isString\", \"lodash/merge\", \"lodash/trim\"], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"cloudinary\"] = factory(require(\"lodash/assign\"), require(\"lodash/cloneDeep\"), require(\"lodash/compact\"), require(\"lodash/difference\"), require(\"lodash/functions\"), require(\"lodash/identity\"), require(\"lodash/includes\"), require(\"lodash/isArray\"), require(\"lodash/isElement\"), require(\"lodash/isFunction\"), require(\"lodash/isPlainObject\"), require(\"lodash/isString\"), require(\"lodash/merge\"), require(\"lodash/trim\"));\n\telse\n\t\troot[\"cloudinary\"] = factory(root[\"_\"][\"assign\"], root[\"_\"][\"cloneDeep\"], root[\"_\"][\"compact\"], root[\"_\"][\"difference\"], root[\"_\"][\"functions\"], root[\"_\"][\"identity\"], root[\"_\"][\"includes\"], root[\"_\"][\"isArray\"], root[\"_\"][\"isElement\"], root[\"_\"][\"isFunction\"], root[\"_\"][\"isPlainObject\"], root[\"_\"][\"isString\"], root[\"_\"][\"merge\"], root[\"_\"][\"trim\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE_lodash_assign__, __WEBPACK_EXTERNAL_MODULE_lodash_cloneDeep__, __WEBPACK_EXTERNAL_MODULE_lodash_compact__, __WEBPACK_EXTERNAL_MODULE_lodash_difference__, __WEBPACK_EXTERNAL_MODULE_lodash_functions__, __WEBPACK_EXTERNAL_MODULE_lodash_identity__, __WEBPACK_EXTERNAL_MODULE_lodash_includes__, __WEBPACK_EXTERNAL_MODULE_lodash_isArray__, __WEBPACK_EXTERNAL_MODULE_lodash_isElement__, __WEBPACK_EXTERNAL_MODULE_lodash_isFunction__, __WEBPACK_EXTERNAL_MODULE_lodash_isPlainObject__, __WEBPACK_EXTERNAL_MODULE_lodash_isString__, __WEBPACK_EXTERNAL_MODULE_lodash_merge__, __WEBPACK_EXTERNAL_MODULE_lodash_trim__) {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = \"./src/namespace/cloudinary-core.js\");\n", "/**\n * UTF8 encoder\n * @private\n */\nvar utf8_encode;\n\nexport default utf8_encode = function(argString) {\n  var c1, enc, end, n, start, string, stringl, utftext;\n  // http://kevin.vanzonneveld.net\n  // +   original by: Webtoolkit.info (http://www.webtoolkit.info/)\n  // +   improved by: <PERSON> (http://kevin.vanzonneveld.net)\n  // +   improved by: sowberry\n  // +    tweaked by: Jack\n  // +   bugfixed by: <PERSON><PERSON>\n  // +   improved by: <PERSON>\n  // +   bugfixed by: <PERSON><PERSON>\n  // +   bugfixed by: <PERSON>\n  // +   bugfixed by: <PERSON><PERSON><PERSON>\n  // +   improved by: kirilloid\n  // *     example 1: utf8_encode('<PERSON>');\n  // *     returns 1: '<PERSON>'\n  if (argString === null || typeof argString === 'undefined') {\n    return '';\n  }\n  string = argString + '';\n  // .replace(/\\r\\n/g, \"\\n\").replace(/\\r/g, \"\\n\");\n  utftext = '';\n  start = void 0;\n  end = void 0;\n  stringl = 0;\n  start = end = 0;\n  stringl = string.length;\n  n = 0;\n  while (n < stringl) {\n    c1 = string.charCodeAt(n);\n    enc = null;\n    if (c1 < 128) {\n      end++;\n    } else if (c1 > 127 && c1 < 2048) {\n      enc = String.fromCharCode(c1 >> 6 | 192, c1 & 63 | 128);\n    } else {\n      enc = String.fromCharCode(c1 >> 12 | 224, c1 >> 6 & 63 | 128, c1 & 63 | 128);\n    }\n    if (enc !== null) {\n      if (end > start) {\n        utftext += string.slice(start, end);\n      }\n      utftext += enc;\n      start = end = n + 1;\n    }\n    n++;\n  }\n  if (end > start) {\n    utftext += string.slice(start, stringl);\n  }\n  return utftext;\n};\n", "\n\nimport utf8_encode from './utf8_encode';\n\n/**\n * CRC32 calculator\n * Depends on 'utf8_encode'\n * @private\n * @param {string} str - The string to calculate the CRC32 for.\n * @return {number}\n */\nfunction crc32(str) {\n  var crc, i, iTop, table, x, y;\n  // http://kevin.vanzonneveld.net\n  // +   original by: Webtoolkit.info (http://www.webtoolkit.info/)\n  // +   improved by: T0bsn\n  // +   improved by: http://stackoverflow.com/questions/2647935/javascript-crc32-function-and-php-crc32-not-matching\n  // -    depends on: utf8_encode\n  // *     example 1: crc32('<PERSON>');\n  // *     returns 1: 1249991249\n  str = utf8_encode(str);\n  table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n  crc = 0;\n  x = 0;\n  y = 0;\n  crc = crc ^ -1;\n  i = 0;\n  iTop = str.length;\n  while (i < iTop) {\n    y = (crc ^ str.charCodeAt(i)) & 0xFF;\n    x = '0x' + table.substr(y * 9, 8);\n    crc = crc >>> 8 ^ x;\n    i++;\n  }\n  crc = crc ^ -1;\n  //convert to unsigned 32-bit int if needed\n  if (crc < 0) {\n    crc += 4294967296;\n  }\n  return crc;\n}\n\nexport default crc32;\n", "export default function stringPad(value, targetLength,padString) {\n  targetLength = targetLength>>0; //truncate if number or convert non-number to 0;\n  padString = String((typeof padString !== 'undefined' ? padString : ' '));\n  if (value.length > targetLength) {\n    return String(value);\n  }\n  else {\n    targetLength = targetLength-value.length;\n    if (targetLength > padString.length) {\n      padString += repeatStringNumTimes(padString, targetLength/padString.length);\n    }\n    return padString.slice(0,targetLength) + String(value);\n  }\n}\n\nfunction repeatStringNumTimes(string, times) {\n  var repeatedString = \"\";\n  while (times > 0) {\n    repeatedString += string;\n    times--;\n  }\n  return repeatedString;\n}\n", "import stringPad from \"./stringPad\";\nlet chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\nlet num = 0;\nlet map = {};\n\n[...chars].forEach((char) => {\n  let key = num.toString(2);\n  key = stringPad(key, 6, '0');\n  map[key] = char;\n  num++;\n});\n\n\n/**\n * Map of six-bit binary codes to Base64 characters\n */\nexport default map;\n", "import stringPad from \"./stringPad\";\n\n/**\n * @description A semVer like string, x.y.z or x.y is allowed\n *              Reverses the version positions, x.y.z turns to z.y.x\n *              Pads each segment with '0' so they have length of 2\n *              Example: 1.2.3 -> 03.02.01\n * @param {string} semVer Input can be either x.y.z or x.y\n * @return {string} in the form of zz.yy.xx (\n */\nexport default function reverseVersion(semVer) {\n  if (semVer.split('.').length < 2) {\n    throw new Error('invalid semVer, must have at least two segments');\n  }\n\n  // Split by '.', reverse, create new array with padded values and concat it together\n  return semVer.split('.').reverse().map((segment) => {\n    return stringPad(segment,2, '0');\n  }).join('.');\n}\n", "import base64Map from \"./base64Map\";\nimport reverseVersion from \"./reverseVersion\";\nimport stringPad from \"./stringPad\";\n\n/**\n * @description Encodes a semVer-like version string\n * @param {string} semVer Input can be either x.y.z or x.y\n * @return {string} A string built from 3 characters of the base64 table that encode the semVer\n */\nexport default function encodeVersion(semVer) {\n  let strResult = '';\n\n  // support x.y or x.y.z by using 'parts' as a variable\n  let parts = semVer.split('.').length;\n  let paddedStringLength = parts * 6; // we pad to either 12 or 18 characters\n\n  // reverse (but don't mirror) the version. 1.5.15 -> 15.5.1\n  // Pad to two spaces, 15.5.1 -> 15.05.01\n  let paddedReversedSemver = reverseVersion(semVer);\n\n  // turn 15.05.01 to a string '150501' then to a number 150501\n  let num = parseInt(paddedReversedSemver.split('.').join(''));\n\n  // Represent as binary, add left padding to 12 or 18 characters.\n  // 150,501 -> 100100101111100101\n\n  let paddedBinary = num.toString(2);\n  paddedBinary = stringPad(paddedBinary, paddedStringLength, '0');\n\n  // Stop in case an invalid version number was provided\n  // paddedBinary must be built from sections of 6 bits\n  if (paddedBinary.length % 6 !== 0) {\n    throw 'Version must be smaller than 43.21.26)';\n  }\n\n  // turn every 6 bits into a character using the base64Map\n  paddedBinary.match(/.{1,6}/g).forEach((bitString) => {\n    // console.log(bitString);\n    strResult += base64Map[bitString];\n  });\n\n  return strResult;\n}\n", "import encodeVersion from \"./encodeVersion.js\";\n\n/**\n * @description Gets the SDK signature by encoding the SDK version and tech version\n * @param {{\n *    [techVersion]:string,\n *    [sdkSemver]: string,\n *    [sdkCode]: string,\n *    [feature]: string\n * }} analyticsOptions\n * @return {string} sdkAnalyticsSignature\n */\nexport default function getSDKAnalyticsSignature(analyticsOptions={}) {\n  try {\n    let twoPartVersion = removePatchFromSemver(analyticsOptions.techVersion);\n    let encodedSDKVersion = encodeVersion(analyticsOptions.sdkSemver);\n    let encodedTechVersion = encodeVersion(twoPartVersion);\n    let featureCode = analyticsOptions.feature;\n    let SDKCode = analyticsOptions.sdkCode;\n    let algoVersion = 'A'; // The algo version is determined here, it should not be an argument\n\n    return `${algoVersion}${SDKCode}${encodedSDKVersion}${encodedTechVersion}${featureCode}`;\n  } catch (e) {\n    // Either SDK or Node versions were unparsable\n    return 'E';\n  }\n}\n\n/**\n * @description Removes patch version from the semver if it exists\n *              Turns x.y.z OR x.y into x.y\n * @param {'x.y.z' || 'x.y' || string} semVerStr\n */\nfunction removePatchFromSemver(semVerStr) {\n  let parts = semVerStr.split('.');\n\n  return `${parts[0]}.${parts[1]}`;\n}\n", "/**\n * @description Gets the analyticsOptions from options- should include sdkSemver, techVersion, sdkCode, and feature\n * @param options\n * @returns {{sdkSemver: (string), sdkCode, feature: string, techVersion: (string)} || {}}\n */\nexport default function getAnalyticsOptions(options) {\n  let analyticsOptions = {\n    sdkSemver: options.sdkSemver,\n    techVersion: options.techVersion,\n    sdkCode: options.sdkCode,\n    feature: '0'\n  };\n  if (options.urlAnalytics) {\n    if (options.accessibility) {\n      analyticsOptions.feature = 'D';\n    }\n    if (options.loading === 'lazy') {\n      analyticsOptions.feature = 'C';\n    }\n    if (options.responsive) {\n      analyticsOptions.feature = 'A';\n    }\n    if (options.placeholder) {\n      analyticsOptions.feature = 'B';\n    }\n    return analyticsOptions;\n  } else {\n    return {};\n  }\n}\n", "/*\n * Includes utility methods for lazy loading media\n */\n\n/**\n * Check if IntersectionObserver is supported\n * @return {boolean} true if window.IntersectionObserver is defined\n */\nexport function isIntersectionObserverSupported() {\n  // Check that 'IntersectionObserver' property is defined on window\n  return typeof window === \"object\" && window.IntersectionObserver;\n}\n\n/**\n * Check if native lazy loading is supported\n * @return {boolean} true if 'loading' property is defined for HTMLImageElement\n */\nexport function isNativeLazyLoadSupported() {\n  return typeof HTMLImageElement === \"object\" && HTMLImageElement.prototype.loading;\n}\n\n/**\n * Calls onIntersect() when intersection is detected, or when\n * no native lazy loading or when IntersectionObserver isn't supported.\n * @param {Element} el - the element to observe\n * @param {function} onIntersect - called when the given element is in view\n */\nexport function detectIntersection(el, onIntersect) {\n  try {\n    if (isNativeLazyLoadSupported() || !isIntersectionObserverSupported()) {\n      // Return if there's no need or possibility to detect intersection\n      onIntersect();\n      return;\n    }\n\n    // Detect intersection with given element using IntersectionObserver\n    const observer = new IntersectionObserver(\n      (entries) => {\n        entries.forEach(entry => {\n          if (entry.isIntersecting) {\n            onIntersect();\n            observer.unobserve(entry.target);\n          }\n        });\n      }, {threshold: [0, 0.01]});\n    observer.observe(el);\n  } catch (e) {\n    onIntersect();\n  }\n}", "export var VERSION = \"2.5.0\";\n\nexport var CF_SHARED_CDN = \"d3jpl91pxevbkh.cloudfront.net\";\n\nexport var OLD_AKAMAI_SHARED_CDN = \"cloudinary-a.akamaihd.net\";\n\nexport var AKAMAI_SHARED_CDN = \"res.cloudinary.com\";\n\nexport var SHARED_CDN = AKAMAI_SHARED_CDN;\n\nexport var DEFAULT_TIMEOUT_MS = 10000;\n\nexport var DEFAULT_POSTER_OPTIONS = {\n  format: 'jpg',\n  resource_type: 'video'\n};\n\nexport var DEFAULT_VIDEO_SOURCE_TYPES = ['webm', 'mp4', 'ogv'];\n\nexport var SEO_TYPES = {\n  \"image/upload\": \"images\",\n  \"image/private\": \"private_images\",\n  \"image/authenticated\": \"authenticated_images\",\n  \"raw/upload\": \"files\",\n  \"video/upload\": \"videos\"\n};\n\n/**\n* @const {Object} Cloudinary.DEFAULT_IMAGE_PARAMS\n* Defaults values for image parameters.\n*\n* (Previously defined using option_consume() )\n */\nexport var DEFAULT_IMAGE_PARAMS = {\n  resource_type: \"image\",\n  transformation: [],\n  type: 'upload'\n};\n\n/**\n* Defaults values for video parameters.\n* @const {Object} Cloudinary.DEFAULT_VIDEO_PARAMS\n* (Previously defined using option_consume() )\n */\nexport var DEFAULT_VIDEO_PARAMS = {\n  fallback_content: '',\n  resource_type: \"video\",\n  source_transformation: {},\n  source_types: DEFAULT_VIDEO_SOURCE_TYPES,\n  transformation: [],\n  type: 'upload'\n};\n\n/**\n * Recommended sources for video tag\n * @const {Object} Cloudinary.DEFAULT_VIDEO_SOURCES\n */\nexport const DEFAULT_VIDEO_SOURCES = [\n  {\n    type: \"mp4\",\n    codecs: \"hev1\",\n    transformations: {video_codec: \"h265\"}\n  },\n  {\n    type: \"webm\",\n    codecs: \"vp9\",\n    transformations: {video_codec: \"vp9\"}\n  },\n  {\n    type: \"mp4\",\n    transformations: {video_codec: \"auto\"}\n  },\n  {\n    type: \"webm\",\n    transformations: {video_codec: \"auto\"}\n  }\n];\n\nexport const DEFAULT_EXTERNAL_LIBRARIES = {\n  seeThru: 'https://unpkg.com/seethru@4/dist/seeThru.min.js'\n}\n\n/**\n * Predefined placeholder transformations\n * @const {Object} Cloudinary.PLACEHOLDER_IMAGE_MODES\n */\nexport const PLACEHOLDER_IMAGE_MODES = {\n  'blur': [{effect: 'blur:2000', quality: 1, fetch_format: 'auto'}], // Default\n  'pixelate': [{effect: 'pixelate', quality: 1, fetch_format: 'auto'}],\n  // Generates a pixel size image which color is the predominant color of the original image.\n  'predominant-color-pixel': [\n    {width: 'iw_div_2', aspect_ratio: 1, crop: 'pad', background: 'auto'},\n    {crop: 'crop', width: 1, height: 1, gravity: 'north_east'},\n    {fetch_format: 'auto', quality: 'auto'}\n  ],\n  // Generates an image which color is the predominant color of the original image.\n  'predominant-color': [\n    {variables: [['$currWidth', 'w'], ['$currHeight', 'h']]},\n    {width: 'iw_div_2', aspect_ratio: 1, crop: 'pad', background: 'auto'},\n    {crop: 'crop', width: 10, height: 10, gravity: 'north_east'},\n    {width: '$currWidth', height: '$currHeight', crop: 'fill'},\n    {fetch_format: 'auto', quality: 'auto'}\n  ],\n  'vectorize': [{effect: 'vectorize:3:0.1', fetch_format: 'svg'}]\n};\n\n/**\n * Predefined accessibility transformations\n * @const {Object} Cloudinary.ACCESSIBILITY_MODES\n */\nexport const ACCESSIBILITY_MODES = {\n  darkmode: 'tint:75:black',\n  brightmode: 'tint:50:white',\n  monochrome: 'grayscale',\n  colorblind: 'assist_colorblind'\n};\n\n/**\n * A list of keys used by the url() function.\n * @private\n */\nexport const URL_KEYS = [\n  'accessibility',\n  'api_secret',\n  'auth_token',\n  'cdn_subdomain',\n  'cloud_name',\n  'cname',\n  'format',\n  'placeholder',\n  'private_cdn',\n  'resource_type',\n  'secure',\n  'secure_cdn_subdomain',\n  'secure_distribution',\n  'shorten',\n  'sign_url',\n  'signature',\n  'ssl_detected',\n  'type',\n  'url_suffix',\n  'use_root_path',\n  'version'\n];\n\n\n/**\n * The resource storage type\n * @typedef type\n * @enum {string}\n * @property  {string} 'upload' A resource uploaded directly to Cloudinary\n * @property  {string} 'fetch' A resource fetched by Cloudinary from a 3rd party storage\n * @property  {string} 'private'\n * @property  {string} 'authenticated'\n * @property  {string} 'sprite'\n * @property  {string} 'facebook'\n * @property  {string} 'twitter'\n * @property  {string} 'youtube'\n * @property  {string} 'vimeo'\n *\n */\n\n/**\n * The resource type\n * @typedef resourceType\n * @enum {string}\n * @property {string} 'image' An image file\n * @property {string} 'video' A video file\n * @property {string} 'raw'   A raw file\n */\n", "/*\n * Includes common utility methods and shims\n */\nimport {contains, isString} from \"../util\";\nimport {URL_KEYS} from '../constants';\n\nexport function omit(obj, keys) {\n  obj = obj || {};\n  let srcKeys = Object.keys(obj).filter(key => !contains(keys, key));\n  let filtered = {};\n  srcKeys.forEach(key => filtered[key] = obj[key]);\n  return filtered;\n}\n\n/**\n * Return true if all items in list are strings\n * @function Util.allString\n * @param {Array} list - an array of items\n */\nexport var allStrings = function(list) {\n  return list.length && list.every(isString);\n};\n\n/**\n* Creates a new array without the given item.\n* @function Util.without\n* @param {Array} array - original array\n* @param {*} item - the item to exclude from the new array\n* @return {Array} a new array made of the original array's items except for `item`\n */\nexport var without = function(array, item) {\n  return array.filter(v=>v!==item);\n};\n\n/**\n* Return true is value is a number or a string representation of a number.\n* @function Util.isNumberLike\n* @param {*} value\n* @returns {boolean} true if value is a number\n* @example\n*    Util.isNumber(0) // true\n*    Util.isNumber(\"1.3\") // true\n*    Util.isNumber(\"\") // false\n*    Util.isNumber(undefined) // false\n */\nexport var isNumberLike = function(value) {\n  return (value != null) && !isNaN(parseFloat(value));\n};\n\n/**\n * Escape all characters matching unsafe in the given string\n * @function Util.smartEscape\n * @param {string} string - source string to escape\n * @param {RegExp} unsafe - characters that must be escaped\n * @return {string} escaped string\n */\nexport var smartEscape = function(string, unsafe = /([^a-zA-Z0-9_.\\-\\/:]+)/g) {\n  return string.replace(unsafe, function(match) {\n    return match.split(\"\").map(function(c) {\n      return \"%\" + c.charCodeAt(0).toString(16).toUpperCase();\n    }).join(\"\");\n  });\n};\n\n/**\n * Assign values from sources if they are not defined in the destination.\n * Once a value is set it does not change\n * @function Util.defaults\n * @param {Object} destination - the object to assign defaults to\n * @param {...Object} source - the source object(s) to assign defaults from\n * @return {Object} destination after it was modified\n */\nexport var defaults = function(destination, ...sources) {\n  return sources.reduce(function(dest, source) {\n    let key, value;\n    for (key in source) {\n      value = source[key];\n      if (dest[key] === void 0) {\n        dest[key] = value;\n      }\n    }\n    return dest;\n  }, destination);\n};\n\n/*********** lodash functions */\nexport var objectProto = Object.prototype;\n\n/**\n * Used to resolve the [`toStringTag`](http://ecma-international.org/ecma-262/6.0/#sec-object.prototype.tostring)\n * of values.\n */\nexport var objToString = objectProto.toString;\n\n/**\n * Checks if `value` is the [language type](https://es5.github.io/#x8) of `Object`.\n * (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n#isObject({});\n * // => true\n *\n#isObject([1, 2, 3]);\n * // => true\n *\n#isObject(1);\n * // => false\n */\nexport var isObject = function(value) {\n  var type;\n  // Avoid a V8 JIT bug in Chrome 19-20.\n  // See https://code.google.com/p/v8/issues/detail?id=2291 for more details.\n  type = typeof value;\n  return !!value && (type === 'object' || type === 'function');\n};\n\nexport var funcTag = '[object Function]';\n\n/**\n* Checks if `value` is classified as a `Function` object.\n* @function Util.isFunction\n* @param {*} value The value to check.\n* @returns {boolean} Returns `true` if `value` is correctly classified, else `false`.\n* @example\n*\n* function Foo(){};\n* isFunction(Foo);\n* // => true\n*\n* isFunction(/abc/);\n* // => false\n */\nexport var isFunction = function(value) {\n  // The use of `Object#toString` avoids issues with the `typeof` operator\n  // in older versions of Chrome and Safari which return 'function' for regexes\n  // and Safari 8 which returns 'object' for typed array constructors.\n  return isObject(value) && objToString.call(value) === funcTag;\n};\n\n/*********** lodash functions */\n/** Used to match words to create compound words. */\nexport var reWords = (function() {\n  var lower, upper;\n  upper = '[A-Z]';\n  lower = '[a-z]+';\n  return RegExp(upper + '+(?=' + upper + lower + ')|' + upper + '?' + lower + '|' + upper + '+|[0-9]+', 'g');\n})();\n\n/**\n* Convert string to camelCase\n* @function Util.camelCase\n* @param {string} source - the string to convert\n* @return {string} in camelCase format\n */\nexport var camelCase = function(source) {\n  var words = source.match(reWords);\n  words = words.map(word=> word.charAt(0).toLocaleUpperCase() + word.slice(1).toLocaleLowerCase());\n  words[0] = words[0].toLocaleLowerCase();\n\n  return words.join('');\n};\n\n/**\n * Convert string to snake_case\n * @function Util.snakeCase\n * @param {string} source - the string to convert\n * @return {string} in snake_case format\n */\nexport var snakeCase = function(source) {\n  var words = source.match(reWords);\n  words = words.map(word=> word.toLocaleLowerCase());\n  return words.join('_');\n};\n\n/**\n * Creates a new object from source, with the keys transformed using the converter.\n * @param {object} source\n * @param {function|null} converter\n * @returns {object}\n */\nexport var convertKeys = function(source, converter) {\n  var result, value;\n  result = {};\n  for (let key in source) {\n    value = source[key];\n    if(converter) {\n      key = converter(key);\n    }\n    if (!isEmpty(key)) {\n      result[key] = value;\n    }\n  }\n  return result;\n};\n\n/**\n * Create a copy of the source object with all keys in camelCase\n * @function Util.withCamelCaseKeys\n * @param {Object} value - the object to copy\n * @return {Object} a new object\n */\nexport var withCamelCaseKeys = function(source) {\n  return convertKeys(source, camelCase);\n};\n\n/**\n * Create a copy of the source object with all keys in snake_case\n * @function Util.withSnakeCaseKeys\n * @param {Object} value - the object to copy\n * @return {Object} a new object\n */\nexport var withSnakeCaseKeys = function(source) {\n  return convertKeys(source, snakeCase);\n};\n\n// Browser\n// Node.js\nexport var base64Encode = typeof btoa !== 'undefined' && isFunction(btoa) ? btoa : typeof Buffer !== 'undefined' && isFunction(Buffer) ? function(input) {\n  if (!(input instanceof Buffer)) {\n    input = new Buffer.from(String(input), 'binary');\n  }\n  return input.toString('base64');\n} : function(input) {\n  throw new Error(\"No base64 encoding function found\");\n};\n\n/**\n* Returns the Base64-decoded version of url.<br>\n* This method delegates to `btoa` if present. Otherwise it tries `Buffer`.\n* @function Util.base64EncodeURL\n* @param {string} url - the url to encode. the value is URIdecoded and then re-encoded before converting to base64 representation\n* @return {string} the base64 representation of the URL\n */\nexport var base64EncodeURL = function(url) {\n  try {\n    url = decodeURI(url);\n  } finally {\n    url = encodeURI(url);\n  }\n  return base64Encode(url);\n};\n\n/**\n * Create a new object with only URL parameters\n * @param {object} options The source object\n * @return {Object} An object containing only URL parameters\n */\nexport function extractUrlParams(options) {\n  return URL_KEYS.reduce((obj, key) => {\n    if (options[key] != null) {\n      obj[key] = options[key];\n    }\n    return obj;\n  }, {});\n}\n\n\n/**\n * Handle the format parameter for fetch urls\n * @private\n * @param options url and transformation options. This argument may be changed by the function!\n */\nexport function patchFetchFormat(options) {\n  if(options == null) {\n    options = {};\n  }\n  if (options.type === \"fetch\") {\n    if (options.fetch_format == null) {\n      options.fetch_format = optionConsume(options, \"format\");\n    }\n  }\n}\n\n/**\n * Deletes `option_name` from `options` and return the value if present.\n * If `options` doesn't contain `option_name` the default value is returned.\n * @param {Object} options a collection\n * @param {String} option_name the name (key) of the desired value\n * @param {*} [default_value] the value to return is option_name is missing\n */\nexport function optionConsume(options, option_name, default_value) {\n  let result = options[option_name];\n  delete options[option_name];\n  if (result != null) {\n    return result;\n  } else {\n    return default_value;\n  }\n}\n\n/**\n * Returns true if value is empty:\n * <ul>\n *   <li>value is null or undefined</li>\n *   <li>value is an array or string of length 0</li>\n *   <li>value is an object with no keys</li>\n * </ul>\n * @function Util.isEmpty\n * @param value\n * @returns {boolean} true if value is empty\n */\nexport function isEmpty(value) {\n  if(value == null) {\n    return true;\n  }\n  if( typeof value.length == \"number\") {\n    return value.length === 0;\n  }\n  if( typeof value.size == \"number\") {\n    return value.size === 0;\n  }\n  if(typeof value == \"object\") {\n    for(let key in value) {\n      if(value.hasOwnProperty(key)) {\n        return false;\n      }\n    }\n    return true;\n  }\n  return true;\n}\n", "/**\n * Based on video.js implementation:\n * https://github.com/videojs/video.js/blob/4238f5c1d88890547153e7e1de7bd0d1d8e0b236/src/js/utils/browser.js\n */\n\n/**\n* Retrieve from the navigator the user agent property.\n* @returns user agent property.\n*/\nfunction getUserAgent(){\n  return navigator && navigator.userAgent || '';\n}\n\n/**\n * Detect if current browser is any Android\n * @returns true if current browser is Android, false otherwise.\n */\nexport function isAndroid(){\n  const userAgent = getUserAgent();\n  return (/Android/i).test(userAgent);\n}\n\n/**\n * Detect if current browser is any Edge\n * @returns true if current browser is Edge, false otherwise.\n */\nexport function isEdge(){\n  const userAgent = getUserAgent();\n  return (/Edg/i).test(userAgent);\n}\n\n/**\n * Detect if current browser is chrome.\n * @returns true if current browser is Chrome, false otherwise.\n */\nexport function isChrome(){\n  const userAgent = getUserAgent();\n  return !isEdge() && ((/Chrome/i).test(userAgent) || (/CriOS/i).test(userAgent));\n}\n\n/**\n * Detect if current browser is Safari.\n * @returns true if current browser is Safari, false otherwise.\n */\nexport function isSafari(){\n  // User agents for other browsers might include \"Safari\" so we must exclude them.\n  // For example - this is the chrome user agent on windows 10:\n  // Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36\n  const userAgent = getUserAgent();\n  return (/Safari/i).test(userAgent) && !isChrome() && !isAndroid() && !isEdge();\n}\n", "var nodeContains;\n\nimport getSDKAnalyticsSignature from \"../sdkAnalytics/getSDKAnalyticsSignature\";\nimport getAnalyticsOptions from \"../sdkAnalytics/getAnalyticsOptions\";\nexport {getSDKAnalyticsSignature , getAnalyticsOptions};\n\nexport {\n  default as assign\n} from 'lodash/assign';\n\nexport {\n  default as cloneDeep\n} from 'lodash/cloneDeep';\n\nexport {\n  default as compact\n} from 'lodash/compact';\n\nexport {\n  default as difference\n} from 'lodash/difference';\n\nexport {\n  default as functions\n} from 'lodash/functions';\n\nexport {\n  default as identity\n} from 'lodash/identity';\n\nexport {\n  default as includes\n} from 'lodash/includes';\n\nexport {\n  default as isArray\n} from 'lodash/isArray';\n\nexport {\n  default as isPlainObject\n} from 'lodash/isPlainObject';\n\nexport {\n  default as isString\n} from 'lodash/isString';\n\nexport {\n  default as merge\n} from 'lodash/merge';\n\nexport {\n  default as contains\n} from 'lodash/includes';\n\nimport isElement from 'lodash/isElement';\nimport isFunction from 'lodash/isFunction';\nimport trim from 'lodash/trim';\n\nexport * from './lazyLoad';\nexport * from './baseutil';\nexport * from './browser';\nexport {\n  isElement,\n  isFunction,\n  trim\n};\n\n/*\n * Includes utility methods and lodash / jQuery shims\n */\n/**\n * Get data from the DOM element.\n *\n * This method will use jQuery's `data()` method if it is available, otherwise it will get the `data-` attribute\n * @param {Element} element - the element to get the data from\n * @param {string} name - the name of the data item\n * @returns the value associated with the `name`\n * @function Util.getData\n */\nexport var getData = function (element, name) {\n  switch (false) {\n    case !(element == null):\n      return void 0;\n    case !isFunction(element.getAttribute):\n      return element.getAttribute(`data-${name}`);\n    case !isFunction(element.getAttr):\n      return element.getAttr(`data-${name}`);\n    case !isFunction(element.data):\n      return element.data(name);\n    case !(isFunction(typeof jQuery !== \"undefined\" && jQuery.fn && jQuery.fn.data) && isElement(element)):\n      return jQuery(element).data(name);\n  }\n};\n\n/**\n * Set data in the DOM element.\n *\n * This method will use jQuery's `data()` method if it is available, otherwise it will set the `data-` attribute\n * @function Util.setData\n * @param {Element} element - the element to set the data in\n * @param {string} name - the name of the data item\n * @param {*} value - the value to be set\n *\n */\nexport var setData = function (element, name, value) {\n  switch (false) {\n    case !(element == null):\n      return void 0;\n    case !isFunction(element.setAttribute):\n      return element.setAttribute(`data-${name}`, value);\n    case !isFunction(element.setAttr):\n      return element.setAttr(`data-${name}`, value);\n    case !isFunction(element.data):\n      return element.data(name, value);\n    case !(isFunction(typeof jQuery !== \"undefined\" && jQuery.fn && jQuery.fn.data) && isElement(element)):\n      return jQuery(element).data(name, value);\n  }\n};\n\n/**\n * Get attribute from the DOM element.\n *\n * @function Util.getAttribute\n * @param {Element} element - the element to set the attribute for\n * @param {string} name - the name of the attribute\n * @returns {*} the value of the attribute\n *\n */\nexport var getAttribute = function (element, name) {\n  switch (false) {\n    case !(element == null):\n      return void 0;\n    case !isFunction(element.getAttribute):\n      return element.getAttribute(name);\n    case !isFunction(element.attr):\n      return element.attr(name);\n    case !isFunction(element.getAttr):\n      return element.getAttr(name);\n  }\n};\n\n/**\n * Set attribute in the DOM element.\n *\n * @function Util.setAttribute\n * @param {Element} element - the element to set the attribute for\n * @param {string} name - the name of the attribute\n * @param {*} value - the value to be set\n */\nexport var setAttribute = function (element, name, value) {\n  switch (false) {\n    case !(element == null):\n      return void 0;\n    case !isFunction(element.setAttribute):\n      return element.setAttribute(name, value);\n    case !isFunction(element.attr):\n      return element.attr(name, value);\n    case !isFunction(element.setAttr):\n      return element.setAttr(name, value);\n  }\n};\n\n/**\n * Remove an attribute in the DOM element.\n *\n * @function Util.removeAttribute\n * @param {Element} element - the element to set the attribute for\n * @param {string} name - the name of the attribute\n */\nexport var removeAttribute = function (element, name) {\n  switch (false) {\n    case !(element == null):\n      return void 0;\n    case !isFunction(element.removeAttribute):\n      return element.removeAttribute(name);\n    default:\n      return setAttribute(element, void 0);\n  }\n};\n\n/**\n * Set a group of attributes to the element\n * @function Util.setAttributes\n * @param {Element} element - the element to set the attributes for\n * @param {Object} attributes - a hash of attribute names and values\n */\nexport var setAttributes = function (element, attributes) {\n  var name, results, value;\n  results = [];\n  for (name in attributes) {\n    value = attributes[name];\n    if (value != null) {\n      results.push(setAttribute(element, name, value));\n    } else {\n      results.push(removeAttribute(element, name));\n    }\n  }\n  return results;\n};\n\n/**\n * Checks if element has a css class\n * @function Util.hasClass\n * @param {Element} element - the element to check\n * @param {string} name - the class name\n @returns {boolean} true if the element has the class\n */\nexport var hasClass = function (element, name) {\n  if (isElement(element)) {\n    return element.className.match(new RegExp(`\\\\b${name}\\\\b`));\n  }\n};\n\n/**\n * Add class to the element\n * @function Util.addClass\n * @param {Element} element - the element\n * @param {string} name - the class name to add\n */\nexport var addClass = function (element, name) {\n  if (!element.className.match(new RegExp(`\\\\b${name}\\\\b`))) {\n    return element.className = trim(`${element.className} ${name}`);\n  }\n};\n\n// The following code is taken from jQuery\nexport var getStyles = function (elem) {\n  // Support: IE<=11+, Firefox<=30+ (#15098, #14150)\n  // IE throws on elements created in popups\n  // FF meanwhile throws on frame elements through \"defaultView.getComputedStyle\"\n  if (elem.ownerDocument.defaultView.opener) {\n    return elem.ownerDocument.defaultView.getComputedStyle(elem, null);\n  }\n  return window.getComputedStyle(elem, null);\n};\n\nexport var cssExpand = [\"Top\", \"Right\", \"Bottom\", \"Left\"];\n\nnodeContains = function (a, b) {\n  var adown, bup;\n  adown = (a.nodeType === 9 ? a.documentElement : a);\n  bup = b && b.parentNode;\n  return a === bup || !!(bup && bup.nodeType === 1 && adown.contains(bup));\n};\n\n// Truncated version of jQuery.style(elem, name)\nexport var domStyle = function (elem, name) {\n  if (!(!elem || elem.nodeType === 3 || elem.nodeType === 8 || !elem.style)) {\n    return elem.style[name];\n  }\n};\n\nexport var curCSS = function (elem, name, computed) {\n  var maxWidth, minWidth, ret, rmargin, style, width;\n  rmargin = /^margin/;\n  width = void 0;\n  minWidth = void 0;\n  maxWidth = void 0;\n  ret = void 0;\n  style = elem.style;\n  computed = computed || getStyles(elem);\n  if (computed) {\n    // Support: IE9\n    // getPropertyValue is only needed for .css('filter') (#12537)\n    ret = computed.getPropertyValue(name) || computed[name];\n  }\n  if (computed) {\n    if (ret === \"\" && !nodeContains(elem.ownerDocument, elem)) {\n      ret = domStyle(elem, name);\n    }\n    // Support: iOS < 6\n    // A tribute to the \"awesome hack by Dean Edwards\"\n    // iOS < 6 (at least) returns percentage for a larger set of values, but width seems to be reliably pixels\n    // this is against the CSSOM draft spec: http://dev.w3.org/csswg/cssom/#resolved-values\n    if (rnumnonpx.test(ret) && rmargin.test(name)) {\n      // Remember the original values\n      width = style.width;\n      minWidth = style.minWidth;\n      maxWidth = style.maxWidth;\n      // Put in the new values to get a computed value out\n      style.minWidth = style.maxWidth = style.width = ret;\n      ret = computed.width;\n      // Revert the changed values\n      style.width = width;\n      style.minWidth = minWidth;\n      style.maxWidth = maxWidth;\n    }\n  }\n  // Support: IE\n  // IE returns zIndex value as an integer.\n  if (ret !== undefined) {\n    return ret + \"\";\n  } else {\n    return ret;\n  }\n};\n\nexport var cssValue = function (elem, name, convert, styles) {\n  var val;\n  val = curCSS(elem, name, styles);\n  if (convert) {\n    return parseFloat(val);\n  } else {\n    return val;\n  }\n};\n\nexport var augmentWidthOrHeight = function (elem, name, extra, isBorderBox, styles) {\n  var i, len, side, sides, val;\n  // If we already have the right measurement, avoid augmentation\n  // Otherwise initialize for horizontal or vertical properties\n  if (extra === (isBorderBox ? \"border\" : \"content\")) {\n    return 0;\n  } else {\n    sides = name === \"width\" ? [\"Right\", \"Left\"] : [\"Top\", \"Bottom\"];\n    val = 0;\n    for (i = 0, len = sides.length; i < len; i++) {\n      side = sides[i];\n      if (extra === \"margin\") {\n        // Both box models exclude margin, so add it if we want it\n        val += cssValue(elem, extra + side, true, styles);\n      }\n      if (isBorderBox) {\n        if (extra === \"content\") {\n          // border-box includes padding, so remove it if we want content\n          val -= cssValue(elem, `padding${side}`, true, styles);\n        }\n        if (extra !== \"margin\") {\n          // At this point, extra isn't border nor margin, so remove border\n          val -= cssValue(elem, `border${side}Width`, true, styles);\n        }\n      } else {\n        // At this point, extra isn't content, so add padding\n        val += cssValue(elem, `padding${side}`, true, styles);\n        if (extra !== \"padding\") {\n          // At this point, extra isn't content nor padding, so add border\n          val += cssValue(elem, `border${side}Width`, true, styles);\n        }\n      }\n    }\n    return val;\n  }\n};\n\nvar pnum = /[+-]?(?:\\d*\\.|)\\d+(?:[eE][+-]?\\d+|)/.source;\n\nvar rnumnonpx = new RegExp(\"^(\" + pnum + \")(?!px)[a-z%]+$\", \"i\");\n\nexport var getWidthOrHeight = function (elem, name, extra) {\n  var isBorderBox, styles, val, valueIsBorderBox;\n  // Start with offset property, which is equivalent to the border-box value\n  valueIsBorderBox = true;\n  val = (name === \"width\" ? elem.offsetWidth : elem.offsetHeight);\n  styles = getStyles(elem);\n  isBorderBox = cssValue(elem, \"boxSizing\", false, styles) === \"border-box\";\n  // Some non-html elements return undefined for offsetWidth, so check for null/undefined\n  // svg - https://bugzilla.mozilla.org/show_bug.cgi?id=649285\n  // MathML - https://bugzilla.mozilla.org/show_bug.cgi?id=491668\n  if (val <= 0 || (val == null)) {\n    // Fall back to computed then uncomputed css if necessary\n    val = curCSS(elem, name, styles);\n    if (val < 0 || (val == null)) {\n      val = elem.style[name];\n    }\n    if (rnumnonpx.test(val)) {\n      // Computed unit is not pixels. Stop here and return.\n      return val;\n    }\n    // Check for style in case a browser which returns unreliable values\n    // for getComputedStyle silently falls back to the reliable elem.style\n    //    valueIsBorderBox = isBorderBox and (support.boxSizingReliable() or val is elem.style[name])\n    valueIsBorderBox = isBorderBox && (val === elem.style[name]);\n    // Normalize \"\", auto, and prepare for extra\n    val = parseFloat(val) || 0;\n  }\n  // Use the active box-sizing model to add/subtract irrelevant styles\n  return val + augmentWidthOrHeight(elem, name, extra || (isBorderBox ? \"border\" : \"content\"), valueIsBorderBox, styles);\n};\n\nexport var width = function (element) {\n  return getWidthOrHeight(element, \"width\", \"content\");\n};\n\n\n/**\n * @class Util\n */\n/**\n * Returns true if item is a string\n * @function Util.isString\n * @param item\n * @returns {boolean} true if item is a string\n */\n/**\n * Returns true if item is empty:\n * <ul>\n *   <li>item is null or undefined</li>\n *   <li>item is an array or string of length 0</li>\n *   <li>item is an object with no keys</li>\n * </ul>\n * @function Util.isEmpty\n * @param item\n * @returns {boolean} true if item is empty\n */\n/**\n * Assign source properties to destination.\n * If the property is an object it is assigned as a whole, overriding the destination object.\n * @function Util.assign\n * @param {Object} destination - the object to assign to\n */\n/**\n * Recursively assign source properties to destination\n * @function Util.merge\n * @param {Object} destination - the object to assign to\n * @param {...Object} [sources] The source objects.\n */\n/**\n * Create a new copy of the given object, including all internal objects.\n * @function Util.cloneDeep\n * @param {Object} value - the object to clone\n * @return {Object} a new deep copy of the object\n */\n/**\n * Creates a new array from the parameter with \"falsey\" values removed\n * @function Util.compact\n * @param {Array} array - the array to remove values from\n * @return {Array} a new array without falsey values\n */\n/**\n * Check if a given item is included in the given array\n * @function Util.contains\n * @param {Array} array - the array to search in\n * @param {*} item - the item to search for\n * @return {boolean} true if the item is included in the array\n */\n/**\n * Returns values in the given array that are not included in the other array\n * @function Util.difference\n * @param {Array} arr - the array to select from\n * @param {Array} values - values to filter from arr\n * @return {Array} the filtered values\n */\n/**\n * Returns a list of all the function names in obj\n * @function Util.functions\n * @param {Object} object - the object to inspect\n * @return {Array} a list of functions of object\n */\n/**\n * Returns the provided value. This functions is used as a default predicate function.\n * @function Util.identity\n * @param {*} value\n * @return {*} the provided value\n */\n/**\n * Remove leading or trailing spaces from text\n * @function Util.trim\n * @param {string} text\n * @return {string} the `text` without leading or trailing spaces\n */\n", "/**\n * Represents a transformation expression.\n * @param {string} expressionStr - An expression in string format.\n * @class Expression\n * Normally this class is not instantiated directly\n */\nclass Expression {\n  constructor(expressionStr) {\n    /**\n     * @protected\n     * @inner Expression-expressions\n     */\n    this.expressions = [];\n    if (expressionStr != null) {\n      this.expressions.push(Expression.normalize(expressionStr));\n    }\n  }\n\n  /**\n   * Convenience constructor method\n   * @function Expression.new\n   */\n  static new(expressionStr) {\n    return new this(expressionStr);\n  }\n\n  /**\n   * Normalize a string expression\n   * @function Cloudinary#normalize\n   * @param {string} expression a expression, e.g. \"w gt 100\", \"width_gt_100\", \"width > 100\"\n   * @return {string} the normalized form of the value expression, e.g. \"w_gt_100\"\n   */\n  static normalize(expression) {\n    if (expression == null) {\n      return expression;\n    }\n    expression = String(expression);\n    const operators = \"\\\\|\\\\||>=|<=|&&|!=|>|=|<|/|-|\\\\+|\\\\*|\\\\^\";\n\n    // operators\n    const operatorsPattern = \"((\" + operators + \")(?=[ _]))\";\n    const operatorsReplaceRE = new RegExp(operatorsPattern, \"g\");\n    expression = expression.replace(operatorsReplaceRE, match => Expression.OPERATORS[match]);\n\n    // predefined variables\n    // The :${v} part is to prevent normalization of vars with a preceding colon (such as :duration),\n    // It won't be found in PREDEFINED_VARS and so won't be normalized.\n    // It is done like this because ie11 does not support regex lookbehind\n    const predefinedVarsPattern = \"(\" + Object.keys(Expression.PREDEFINED_VARS).map(v=>`:${v}|${v}`).join(\"|\") + \")\";\n    const userVariablePattern = '(\\\\$_*[^_ ]+)';\n\n    const variablesReplaceRE = new RegExp(`${userVariablePattern}|${predefinedVarsPattern}`, \"g\");\n    expression = expression.replace(variablesReplaceRE, (match) => (Expression.PREDEFINED_VARS[match] || match));\n\n    return expression.replace(/[ _]+/g, '_');\n  }\n\n  /**\n   * Serialize the expression\n   * @return {string} the expression as a string\n   */\n  serialize() {\n    return Expression.normalize(this.expressions.join(\"_\"));\n  }\n\n  toString() {\n    return this.serialize();\n  }\n\n  /**\n   * Get the parent transformation of this expression\n   * @return Transformation\n   */\n  getParent() {\n    return this.parent;\n  }\n\n  /**\n   * Set the parent transformation of this expression\n   * @param {Transformation} the parent transformation\n   * @return {Expression} this expression\n   */\n  setParent(parent) {\n    this.parent = parent;\n    return this;\n  }\n\n  /**\n   * Add a expression\n   * @function Expression#predicate\n   * @internal\n   */\n  predicate(name, operator, value) {\n    if (Expression.OPERATORS[operator] != null) {\n      operator = Expression.OPERATORS[operator];\n    }\n    this.expressions.push(`${name}_${operator}_${value}`);\n    return this;\n  }\n\n  /**\n   * @function Expression#and\n   */\n  and() {\n    this.expressions.push(\"and\");\n    return this;\n  }\n\n  /**\n   * @function Expression#or\n   */\n  or() {\n    this.expressions.push(\"or\");\n    return this;\n  }\n\n  /**\n   * Conclude expression\n   * @function Expression#then\n   * @return {Transformation} the transformation this expression is defined for\n   */\n  then() {\n    return this.getParent().if(this.toString());\n  }\n\n  /**\n   * @function Expression#height\n   * @param {string} operator the comparison operator (e.g. \"<\", \"lt\")\n   * @param {string|number} value the right hand side value\n   * @return {Expression} this expression\n   */\n  height(operator, value) {\n    return this.predicate(\"h\", operator, value);\n  }\n\n  /**\n   * @function Expression#width\n   * @param {string} operator the comparison operator (e.g. \"<\", \"lt\")\n   * @param {string|number} value the right hand side value\n   * @return {Expression} this expression\n   */\n  width(operator, value) {\n    return this.predicate(\"w\", operator, value);\n  }\n\n  /**\n   * @function Expression#aspectRatio\n   * @param {string} operator the comparison operator (e.g. \"<\", \"lt\")\n   * @param {string|number} value the right hand side value\n   * @return {Expression} this expression\n   */\n  aspectRatio(operator, value) {\n    return this.predicate(\"ar\", operator, value);\n  }\n\n  /**\n   * @function Expression#pages\n   * @param {string} operator the comparison operator (e.g. \"<\", \"lt\")\n   * @param {string|number} value the right hand side value\n   * @return {Expression} this expression\n   */\n  pageCount(operator, value) {\n    return this.predicate(\"pc\", operator, value);\n  }\n\n  /**\n   * @function Expression#faces\n   * @param {string} operator the comparison operator (e.g. \"<\", \"lt\")\n   * @param {string|number} value the right hand side value\n   * @return {Expression} this expression\n   */\n  faceCount(operator, value) {\n    return this.predicate(\"fc\", operator, value);\n  }\n\n  value(value) {\n    this.expressions.push(value);\n    return this;\n  }\n\n  /**\n   */\n  static variable(name, value) {\n    return new this(name).value(value);\n  }\n\n  /**\n   * @returns Expression a new expression with the predefined variable \"width\"\n   * @function Expression.width\n   */\n  static width() {\n    return new this(\"width\");\n  }\n\n  /**\n   * @returns Expression a new expression with the predefined variable \"height\"\n   * @function Expression.height\n   */\n  static height() {\n    return new this(\"height\");\n  }\n\n  /**\n   * @returns Expression a new expression with the predefined variable \"initialWidth\"\n   * @function Expression.initialWidth\n   */\n  static initialWidth() {\n    return new this(\"initialWidth\");\n  }\n\n  /**\n   * @returns Expression a new expression with the predefined variable \"initialHeight\"\n   * @function Expression.initialHeight\n   */\n  static initialHeight() {\n    return new this(\"initialHeight\");\n  }\n\n  /**\n   * @returns Expression a new expression with the predefined variable \"aspectRatio\"\n   * @function Expression.aspectRatio\n   */\n  static aspectRatio() {\n    return new this(\"aspectRatio\");\n  }\n\n  /**\n   * @returns Expression a new expression with the predefined variable \"initialAspectRatio\"\n   * @function Expression.initialAspectRatio\n   */\n  static initialAspectRatio() {\n    return new this(\"initialAspectRatio\");\n  }\n\n  /**\n   * @returns Expression a new expression with the predefined variable \"pageCount\"\n   * @function Expression.pageCount\n   */\n  static pageCount() {\n    return new this(\"pageCount\");\n  }\n\n  /**\n   * @returns Expression new expression with the predefined variable \"faceCount\"\n   * @function Expression.faceCount\n   */\n  static faceCount() {\n    return new this(\"faceCount\");\n  }\n\n  /**\n   * @returns Expression a new expression with the predefined variable \"currentPage\"\n   * @function Expression.currentPage\n   */\n  static currentPage() {\n    return new this(\"currentPage\");\n  }\n\n  /**\n   * @returns Expression a new expression with the predefined variable \"tags\"\n   * @function Expression.tags\n   */\n  static tags() {\n    return new this(\"tags\");\n  }\n\n  /**\n   * @returns Expression a new expression with the predefined variable \"pageX\"\n   * @function Expression.pageX\n   */\n  static pageX() {\n    return new this(\"pageX\");\n  }\n\n  /**\n   * @returns Expression a new expression with the predefined variable \"pageY\"\n   * @function Expression.pageY\n   */\n  static pageY() {\n    return new this(\"pageY\");\n  }\n\n}\n\n/**\n * @internal\n */\nExpression.OPERATORS = {\n  \"=\": 'eq',\n  \"!=\": 'ne',\n  \"<\": 'lt',\n  \">\": 'gt',\n  \"<=\": 'lte',\n  \">=\": 'gte',\n  \"&&\": 'and',\n  \"||\": 'or',\n  \"*\": \"mul\",\n  \"/\": \"div\",\n  \"+\": \"add\",\n  \"-\": \"sub\",\n  \"^\": \"pow\",\n};\n\n/**\n * @internal\n */\nExpression.PREDEFINED_VARS = {\n  \"aspect_ratio\": \"ar\",\n  \"aspectRatio\": \"ar\",\n  \"current_page\": \"cp\",\n  \"currentPage\": \"cp\",\n  \"duration\": \"du\",\n  \"face_count\": \"fc\",\n  \"faceCount\": \"fc\",\n  \"height\": \"h\",\n  \"initial_aspect_ratio\": \"iar\",\n  \"initial_duration\": \"idu\",\n  \"initial_height\": \"ih\",\n  \"initial_width\": \"iw\",\n  \"initialAspectRatio\": \"iar\",\n  \"initialDuration\": \"idu\",\n  \"initialHeight\": \"ih\",\n  \"initialWidth\": \"iw\",\n  \"page_count\": \"pc\",\n  \"page_x\": \"px\",\n  \"page_y\": \"py\",\n  \"pageCount\": \"pc\",\n  \"pageX\": \"px\",\n  \"pageY\": \"py\",\n  \"tags\": \"tags\",\n  \"width\": \"w\"\n};\n\n/**\n * @internal\n */\nExpression.BOUNDRY = \"[ _]+\";\n\nexport default Expression;\n", "import Expression from './expression';\n\n/**\n * Represents a transformation condition.\n * @param {string} conditionStr - a condition in string format\n * @class Condition\n * @example\n * // normally this class is not instantiated directly\n * var tr = cloudinary.Transformation.new()\n *    .if().width( \">\", 1000).and().aspectRatio(\"<\", \"3:4\").then()\n *      .width(1000)\n *      .crop(\"scale\")\n *    .else()\n *      .width(500)\n *      .crop(\"scale\")\n *\n * var tr = cloudinary.Transformation.new()\n *    .if(\"w > 1000 and aspectRatio < 3:4\")\n *      .width(1000)\n *      .crop(\"scale\")\n *    .else()\n *      .width(500)\n *      .crop(\"scale\")\n *\n */\nclass Condition extends Expression {\n  constructor(conditionStr) {\n    super(conditionStr);\n  }\n\n  /**\n   * @function Condition#height\n   * @param {string} operator the comparison operator (e.g. \"<\", \"lt\")\n   * @param {string|number} value the right hand side value\n   * @return {Condition} this condition\n   */\n  height(operator, value) {\n    return this.predicate(\"h\", operator, value);\n  }\n\n  /**\n   * @function Condition#width\n   * @param {string} operator the comparison operator (e.g. \"<\", \"lt\")\n   * @param {string|number} value the right hand side value\n   * @return {Condition} this condition\n   */\n  width(operator, value) {\n    return this.predicate(\"w\", operator, value);\n  }\n\n  /**\n   * @function Condition#aspectRatio\n   * @param {string} operator the comparison operator (e.g. \"<\", \"lt\")\n   * @param {string|number} value the right hand side value\n   * @return {Condition} this condition\n   */\n  aspectRatio(operator, value) {\n    return this.predicate(\"ar\", operator, value);\n  }\n\n  /**\n   * @function Condition#pages\n   * @param {string} operator the comparison operator (e.g. \"<\", \"lt\")\n   * @param {string|number} value the right hand side value\n   * @return {Condition} this condition\n   */\n  pageCount(operator, value) {\n    return this.predicate(\"pc\", operator, value);\n  }\n\n  /**\n   * @function Condition#faces\n   * @param {string} operator the comparison operator (e.g. \"<\", \"lt\")\n   * @param {string|number} value the right hand side value\n   * @return {Condition} this condition\n   */\n  faceCount(operator, value) {\n    return this.predicate(\"fc\", operator, value);\n  }\n\n  /**\n   * @function Condition#duration\n   * @param {string} operator the comparison operator (e.g. \"<\", \"lt\")\n   * @param {string|number} value the right hand side value\n   * @return {Condition} this condition\n   */\n  duration(operator, value) {\n    return this.predicate(\"du\", operator, value);\n  }\n\n  /**\n   * @function Condition#initialDuration\n   * @param {string} operator the comparison operator (e.g. \"<\", \"lt\")\n   * @param {string|number} value the right hand side value\n   * @return {Condition} this condition\n   */\n  initialDuration(operator, value) {\n    return this.predicate(\"idu\", operator, value);\n  }\n}\n\nexport default Condition;\n", "/**\n * Class for defining account configuration options.\n * Depends on 'utils'\n */\n\nimport {\n  defaults,\n  assign,\n  isString,\n  isPlainObject,\n  cloneDeep\n} from './util';\n\n/**\n * Class for defining account configuration options.\n * @constructor Configuration\n * @param {Object} options - The account configuration parameters to set.\n * @see <a href=\"https://cloudinary.com/documentation/solution_overview#configuration_parameters\"\n *  target=\"_new\">Available configuration options</a>\n */\nclass Configuration {\n  constructor(options) {\n    this.configuration = options == null ? {} : cloneDeep(options);\n    defaults(this.configuration, DEFAULT_CONFIGURATION_PARAMS);\n  }\n\n  /**\n   * Initializes the configuration. This method is a convenience method that invokes both\n   *  {@link Configuration#fromEnvironment|fromEnvironment()} (Node.js environment only)\n   *  and {@link Configuration#fromDocument|fromDocument()}.\n   *  It first tries to retrieve the configuration from the environment variable.\n   *  If not available, it tries from the document meta tags.\n   * @function Configuration#init\n   * @return {Configuration} returns `this` for chaining\n   * @see fromDocument\n   * @see fromEnvironment\n   */\n  init() {\n    this.fromEnvironment();\n    this.fromDocument();\n    return this;\n  }\n\n  /**\n   * Set a new configuration item\n   * @function Configuration#set\n   * @param {string} name - the name of the item to set\n   * @param {*} value - the value to be set\n   * @return {Configuration}\n   *\n   */\n  set(name, value) {\n    this.configuration[name] = value;\n    return this;\n  }\n\n  /**\n   * Get the value of a configuration item\n   * @function Configuration#get\n   * @param {string} name - the name of the item to set\n   * @return {*} the configuration item\n   */\n  get(name) {\n    return this.configuration[name];\n  }\n\n  merge(config) {\n    assign(this.configuration, cloneDeep(config));\n    return this;\n  }\n\n  /**\n   * Initialize Cloudinary from HTML meta tags.\n   * @function Configuration#fromDocument\n   * @return {Configuration}\n   * @example <meta name=\"cloudinary_cloud_name\" content=\"mycloud\">\n   *\n   */\n  fromDocument() {\n    var el, i, len, meta_elements;\n    meta_elements = typeof document !== \"undefined\" && document !== null ? document.querySelectorAll('meta[name^=\"cloudinary_\"]') : void 0;\n    if (meta_elements) {\n      for (i = 0, len = meta_elements.length; i < len; i++) {\n        el = meta_elements[i];\n        this.configuration[el.getAttribute('name').replace('cloudinary_', '')] = el.getAttribute('content');\n      }\n    }\n    return this;\n  }\n\n  /**\n   * Initialize Cloudinary from the `CLOUDINARY_URL` environment variable.\n   *\n   * This function will only run under Node.js environment.\n   * @function Configuration#fromEnvironment\n   * @requires Node.js\n   */\n  fromEnvironment() {\n    var cloudinary_url, query, uri, uriRegex;\n    if(typeof process !== \"undefined\" && process !== null && process.env && process.env.CLOUDINARY_URL ){\n      cloudinary_url = process.env.CLOUDINARY_URL;\n      uriRegex = /cloudinary:\\/\\/(?:(\\w+)(?:\\:([\\w-]+))?@)?([\\w\\.-]+)(?:\\/([^?]*))?(?:\\?(.+))?/;\n      uri = uriRegex.exec(cloudinary_url);\n      if (uri) {\n        if (uri[3] != null) {\n          this.configuration['cloud_name'] = uri[3];\n        }\n        if (uri[1] != null) {\n          this.configuration['api_key'] = uri[1];\n        }\n        if (uri[2] != null) {\n          this.configuration['api_secret'] = uri[2];\n        }\n        if (uri[4] != null) {\n          this.configuration['private_cdn'] = uri[4] != null;\n        }\n        if (uri[4] != null) {\n          this.configuration['secure_distribution'] = uri[4];\n        }\n        query = uri[5];\n        if (query != null) {\n          query.split('&').forEach(value=>{\n            let [k, v] = value.split('=');\n            if (v == null) {\n              v = true;\n            }\n            this.configuration[k] = v;\n          });\n        }\n      }\n    }\n    return this;\n  }\n\n  /**\n   * Create or modify the Cloudinary client configuration\n   *\n   * Warning: `config()` returns the actual internal configuration object. modifying it will change the configuration.\n   *\n   * This is a backward compatibility method. For new code, use get(), merge() etc.\n   * @function Configuration#config\n   * @param {hash|string|boolean} new_config\n   * @param {string} new_value\n   * @returns {*} configuration, or value\n   *\n   * @see {@link fromEnvironment} for initialization using environment variables\n   * @see {@link fromDocument} for initialization using HTML meta tags\n   */\n  config(new_config, new_value) {\n    switch (false) {\n      case new_value === void 0:\n        this.set(new_config, new_value);\n        return this.configuration;\n      case !isString(new_config):\n        return this.get(new_config);\n      case !isPlainObject(new_config):\n        this.merge(new_config);\n        return this.configuration;\n      default:\n        // Backward compatibility - return the internal object\n        return this.configuration;\n    }\n  }\n\n  /**\n   * Returns a copy of the configuration parameters\n   * @function Configuration#toOptions\n   * @returns {Object} a key:value collection of the configuration parameters\n   */\n  toOptions() {\n    return cloneDeep(this.configuration);\n  }\n\n}\n\nconst DEFAULT_CONFIGURATION_PARAMS = {\n  responsive_class: 'cld-responsive',\n  responsive_use_breakpoints: true,\n  round_dpr: true,\n  secure: (typeof window !== \"undefined\" && window !== null ? window.location ? window.location.protocol : void 0 : void 0) === 'https:'\n};\n\nConfiguration.CONFIG_PARAMS = [\n  \"api_key\",\n  \"api_secret\",\n  \"callback\",\n  \"cdn_subdomain\",\n  \"cloud_name\",\n  \"cname\",\n  \"private_cdn\",\n  \"protocol\",\n  \"resource_type\",\n  \"responsive\",\n  \"responsive_class\",\n  \"responsive_use_breakpoints\",\n  \"responsive_width\",\n  \"round_dpr\",\n  \"secure\",\n  \"secure_cdn_subdomain\",\n  \"secure_distribution\",\n  \"shorten\",\n  \"type\",\n  \"upload_preset\",\n  \"url_suffix\",\n  \"use_root_path\",\n  \"version\",\n  \"externalLibraries\",\n  \"max_timeout_ms\"\n];\n\nexport default Configuration;\n", "import {\n  snakeCase,\n  compact\n} from '../util';\n\nclass Layer {\n  /**\n   * Layer\n   * @constructor Layer\n   * @param {Object} options - layer parameters\n   */\n  constructor(options) {\n    this.options = {};\n    if (options != null) {\n      [\"resourceType\", \"type\", \"publicId\", \"format\"].forEach((key) => {\n        var ref;\n        return this.options[key] = (ref = options[key]) != null ? ref : options[snakeCase(key)];\n      });\n    }\n  }\n\n  resourceType(value) {\n    this.options.resourceType = value;\n    return this;\n  }\n\n  type(value) {\n    this.options.type = value;\n    return this;\n  }\n\n  publicId(value) {\n    this.options.publicId = value;\n    return this;\n  }\n\n  /**\n   * Get the public ID, formatted for layer parameter\n   * @function Layer#getPublicId\n   * @return {String} public ID\n   */\n  getPublicId() {\n    var ref;\n    return (ref = this.options.publicId) != null ? ref.replace(/\\//g, \":\") : void 0;\n  }\n\n  /**\n   * Get the public ID, with format if present\n   * @function Layer#getFullPublicId\n   * @return {String} public ID\n   */\n  getFullPublicId() {\n    if (this.options.format != null) {\n      return this.getPublicId() + \".\" + this.options.format;\n    } else {\n      return this.getPublicId();\n    }\n  }\n\n  format(value) {\n    this.options.format = value;\n    return this;\n  }\n\n  /**\n   * generate the string representation of the layer\n   * @function Layer#toString\n   */\n  toString() {\n    var components;\n    components = [];\n    if (this.options.publicId == null) {\n      throw \"Must supply publicId\";\n    }\n    if (!(this.options.resourceType === \"image\")) {\n      components.push(this.options.resourceType);\n    }\n    if (!(this.options.type === \"upload\")) {\n      components.push(this.options.type);\n    }\n    components.push(this.getFullPublicId());\n    return compact(components).join(\":\");\n  }\n\n  clone() {\n    return new this.constructor(this.options);\n  }\n\n}\n\nexport default Layer;\n", "import Layer from './layer';\n\nimport {\n  compact,\n  isEmpty,\n  isNumberLike,\n  smartEscape,\n  snakeCase\n} from '../util';\n\nclass TextLayer extends Layer {\n  /**\n   * @constructor TextLayer\n   * @param {Object} options - layer parameters\n   */\n  constructor(options) {\n    var keys;\n    super(options);\n    keys = [\"resourceType\", \"resourceType\", \"fontFamily\", \"fontSize\", \"fontWeight\", \"fontStyle\", \"textDecoration\", \"textAlign\", \"stroke\", \"letterSpacing\", \"lineSpacing\", \"fontHinting\", \"fontAntialiasing\", \"text\", \"textStyle\"];\n    if (options != null) {\n      keys.forEach((key) => {\n        var ref;\n        return this.options[key] = (ref = options[key]) != null ? ref : options[snakeCase(key)];\n      });\n    }\n    this.options.resourceType = \"text\";\n  }\n\n  resourceType(resourceType) {\n    throw \"Cannot modify resourceType for text layers\";\n  }\n\n  type(type) {\n    throw \"Cannot modify type for text layers\";\n  }\n\n  format(format) {\n    throw \"Cannot modify format for text layers\";\n  }\n\n  fontFamily(fontFamily) {\n    this.options.fontFamily = fontFamily;\n    return this;\n  }\n\n  fontSize(fontSize) {\n    this.options.fontSize = fontSize;\n    return this;\n  }\n\n  fontWeight(fontWeight) {\n    this.options.fontWeight = fontWeight;\n    return this;\n  }\n\n  fontStyle(fontStyle) {\n    this.options.fontStyle = fontStyle;\n    return this;\n  }\n\n  textDecoration(textDecoration) {\n    this.options.textDecoration = textDecoration;\n    return this;\n  }\n\n  textAlign(textAlign) {\n    this.options.textAlign = textAlign;\n    return this;\n  }\n\n  stroke(stroke) {\n    this.options.stroke = stroke;\n    return this;\n  }\n\n  letterSpacing(letterSpacing) {\n    this.options.letterSpacing = letterSpacing;\n    return this;\n  }\n\n  lineSpacing(lineSpacing) {\n    this.options.lineSpacing = lineSpacing;\n    return this;\n  }\n\n  fontHinting (fontHinting){\n    this.options.fontHinting = fontHinting;\n    return this;\n  }\n\n  fontAntialiasing (fontAntialiasing){\n    this.options.fontAntialiasing = fontAntialiasing;\n    return this;\n  }\n\n  text(text) {\n    this.options.text = text;\n    return this;\n  }\n\n  textStyle(textStyle) {\n    this.options.textStyle = textStyle;\n    return this;\n  }\n\n  /**\n   * generate the string representation of the layer\n   * @function TextLayer#toString\n   * @return {String}\n   */\n  toString() {\n    var components, hasPublicId, hasStyle, publicId, re, res, start, style, text, textSource;\n    style = this.textStyleIdentifier();\n    if (this.options.publicId != null) {\n      publicId = this.getFullPublicId();\n    }\n    if (this.options.text != null) {\n      hasPublicId = !isEmpty(publicId);\n      hasStyle = !isEmpty(style);\n      if (hasPublicId && hasStyle || !hasPublicId && !hasStyle) {\n        throw \"Must supply either style parameters or a public_id when providing text parameter in a text overlay/underlay, but not both!\";\n      }\n      re = /\\$\\([a-zA-Z]\\w*\\)/g;\n      start = 0;\n      //        textSource = text.replace(new RegExp(\"[,/]\", 'g'), (c)-> \"%#{c.charCodeAt(0).toString(16).toUpperCase()}\")\n      textSource = smartEscape(this.options.text, /[,\\/]/g);\n      text = \"\";\n      while (res = re.exec(textSource)) {\n        text += smartEscape(textSource.slice(start, res.index));\n        text += res[0];\n        start = res.index + res[0].length;\n      }\n      text += smartEscape(textSource.slice(start));\n    }\n    components = [this.options.resourceType, style, publicId, text];\n    return compact(components).join(\":\");\n  }\n\n  textStyleIdentifier() {\n    // Note: if a text-style argument is provided as a whole, it overrides everything else, no mix and match.\n    if (!isEmpty(this.options.textStyle)) {\n      return this.options.textStyle;\n    }\n    var components;\n    components = [];\n    if (this.options.fontWeight !== \"normal\") {\n      components.push(this.options.fontWeight);\n    }\n    if (this.options.fontStyle !== \"normal\") {\n      components.push(this.options.fontStyle);\n    }\n    if (this.options.textDecoration !== \"none\") {\n      components.push(this.options.textDecoration);\n    }\n    components.push(this.options.textAlign);\n    if (this.options.stroke !== \"none\") {\n      components.push(this.options.stroke);\n    }\n    if (!(isEmpty(this.options.letterSpacing) && !isNumberLike(this.options.letterSpacing))) {\n      components.push(\"letter_spacing_\" + this.options.letterSpacing);\n    }\n    if (!(isEmpty(this.options.lineSpacing) && !isNumberLike(this.options.lineSpacing))) {\n      components.push(\"line_spacing_\" + this.options.lineSpacing);\n    }\n    if (!(isEmpty(this.options.fontAntialiasing))) {\n      components.push(\"antialias_\"+this.options.fontAntialiasing);\n    }\n    if (!(isEmpty(this.options.fontHinting))) {\n      components.push(\"hinting_\"+this.options.fontHinting );\n    }\n    if (!isEmpty(compact(components))) {\n      if (isEmpty(this.options.fontFamily)) {\n        throw `Must supply fontFamily. ${components}`;\n      }\n      if (isEmpty(this.options.fontSize) && !isNumberLike(this.options.fontSize)) {\n        throw \"Must supply fontSize.\";\n      }\n    }\n    components.unshift(this.options.fontFamily, this.options.fontSize);\n    components = compact(components).join(\"_\");\n    return components;\n  }\n\n};\n\nexport default TextLayer;\n", "import TextLayer from './textlayer';\n\nclass SubtitlesLayer extends TextLayer {\n  /**\n   * Represent a subtitles layer\n   * @constructor SubtitlesLayer\n   * @param {Object} options - layer parameters\n   */\n  constructor(options) {\n    super(options);\n    this.options.resourceType = \"subtitles\";\n  }\n\n}\nexport default SubtitlesLayer;\n", "import Layer from './layer';\n\nimport {\n  base64EncodeURL,\n  isString\n} from '../util';\n\nclass FetchLayer extends Layer {\n  /**\n   * @class FetchLayer\n   * @classdesc Creates an image layer using a remote URL.\n   * @param {Object|string} options - layer parameters or a url\n   * @param {string} options.url the url of the image to fetch\n   */\n  constructor(options) {\n    super(options);\n    if (isString(options)) {\n      this.options.url = options;\n    } else if (options != null ? options.url : void 0) {\n      this.options.url = options.url;\n    }\n  }\n\n  url(url) {\n    this.options.url = url;\n    return this;\n  }\n\n  /**\n   * generate the string representation of the layer\n   * @function FetchLayer#toString\n   * @return {String}\n   */\n  toString() {\n    return `fetch:${base64EncodeURL(this.options.url)}`;\n  }\n\n}\n\nexport default FetchLayer;\n", "import Expression from './expression';\nimport Transformation from \"./transformation\";\n\nimport {\n  allStrings,\n  identity,\n  isArray,\n  isEmpty,\n  isFunction,\n  isPlainObject,\n  isString,\n  withCamelCaseKeys\n} from './util';\n\nimport Layer from './layer/layer';\nimport TextLayer from './layer/textlayer';\nimport SubtitlesLayer from './layer/subtitleslayer';\nimport FetchLayer from './layer/fetchlayer';\n\n/**\n * Transformation parameters\n * Depends on 'util', 'transformation'\n */\nclass Param {\n  /**\n   * Represents a single parameter.\n   * @class Param\n   * @param {string} name - The name of the parameter in snake_case\n   * @param {string} shortName - The name of the serialized form of the parameter.\n   *                         If a value is not provided, the parameter will not be serialized.\n   * @param {function} [process=Util.identity ] - Manipulate origValue when value is called\n   * @ignore\n   */\n  constructor(name, shortName, process = identity) {\n    /**\n     * The name of the parameter in snake_case\n     * @member {string} Param#name\n     */\n    this.name = name;\n    /**\n     * The name of the serialized form of the parameter\n     * @member {string} Param#shortName\n     */\n    this.shortName = shortName;\n    /**\n     * Manipulate origValue when value is called\n     * @member {function} Param#process\n     */\n    this.process = process;\n  }\n\n  /**\n   * Set a (unprocessed) value for this parameter\n   * @function Param#set\n   * @param {*} origValue - the value of the parameter\n   * @return {Param} self for chaining\n   */\n  set(origValue) {\n    this.origValue = origValue;\n    return this;\n  }\n\n  /**\n   * Generate the serialized form of the parameter\n   * @function Param#serialize\n   * @return {string} the serialized form of the parameter\n   */\n  serialize() {\n    var val, valid;\n    val = this.value();\n    valid = isArray(val) || isPlainObject(val) || isString(val) ? !isEmpty(val) : val != null;\n    if ((this.shortName != null) && valid) {\n      return `${this.shortName}_${val}`;\n    } else {\n      return '';\n    }\n  }\n\n  /**\n   * Return the processed value of the parameter\n   * @function Param#value\n   */\n  value() {\n    return this.process(this.origValue);\n  }\n\n  static norm_color(value) {\n    return value != null ? value.replace(/^#/, 'rgb:') : void 0;\n  }\n\n  static build_array(arg) {\n    if(arg == null) {\n      return [];\n    } else if (isArray(arg)) {\n      return arg;\n    } else {\n      return [arg];\n    }\n  }\n\n  /**\n  * Covert value to video codec string.\n  *\n  * If the parameter is an object,\n  * @param {(string|Object)} param - the video codec as either a String or a Hash\n  * @return {string} the video codec string in the format codec:profile:level:b_frames\n  * @example\n  * vc_[ :profile : [level : [b_frames]]]\n  * or\n    { codec: 'h264', profile: 'basic', level: '3.1', b_frames: false }\n  * @ignore\n   */\n  static process_video_params(param) {\n    var video;\n    switch (param.constructor) {\n      case Object:\n        video = \"\";\n        if ('codec' in param) {\n          video = param.codec;\n          if ('profile' in param) {\n            video += \":\" + param.profile;\n            if ('level' in param) {\n              video += \":\" + param.level;\n              if ('b_frames' in param && param.b_frames === false) {\n                video += \":bframes_no\";\n              }\n            }\n          }\n        }\n        return video;\n      case String:\n        return param;\n      default:\n        return null;\n    }\n  }\n}\n\nclass ArrayParam extends Param {\n  /**\n   * A parameter that represents an array.\n   * @param {string} name - The name of the parameter in snake_case.\n   * @param {string} shortName - The name of the serialized form of the parameter\n   *                         If a value is not provided, the parameter will not be serialized.\n   * @param {string} [sep='.'] - The separator to use when joining the array elements together\n   * @param {function} [process=Util.identity ] - Manipulate origValue when value is called\n   * @class ArrayParam\n   * @extends Param\n   * @ignore\n   */\n  constructor(name, shortName, sep = '.', process = undefined) {\n    super(name, shortName, process);\n    this.sep = sep;\n  }\n\n  serialize() {\n    if (this.shortName != null) {\n      let arrayValue = this.value();\n      if (isEmpty(arrayValue)) {\n        return '';\n      } else if (isString(arrayValue)) {\n        return `${this.shortName}_${arrayValue}`;\n      } else {\n        let flat = arrayValue.map(t=>isFunction(t.serialize) ? t.serialize() : t).join(this.sep);\n        return `${this.shortName}_${flat}`;\n      }\n    } else {\n      return '';\n    }\n  }\n\n  value() {\n    if (isArray(this.origValue)) {\n      return this.origValue.map(v=>this.process(v));\n    } else {\n      return this.process(this.origValue);\n    }\n  }\n\n  set(origValue) {\n    if ((origValue == null) || isArray(origValue)) {\n      return super.set(origValue);\n    } else {\n      return super.set([origValue]);\n    }\n  }\n}\n\nclass TransformationParam extends Param {\n  /**\n   * A parameter that represents a transformation\n   * @param {string} name - The name of the parameter in snake_case\n   * @param {string} [shortName='t'] - The name of the serialized form of the parameter\n   * @param {string} [sep='.'] - The separator to use when joining the array elements together\n   * @param {function} [process=Util.identity ] - Manipulate origValue when value is called\n   * @class TransformationParam\n   * @extends Param\n   * @ignore\n   */\n  constructor(name, shortName = \"t\", sep = '.', process = undefined) {\n    super(name, shortName, process);\n    this.sep = sep;\n  }\n\n  /**\n   * Generate string representations of the transformation.\n   * @returns {*} Returns either the transformation as a string, or an array of string representations.\n   */\n  serialize() {\n    let result = '';\n    const val = this.value();\n\n    if (isEmpty(val)) {\n      return result;\n    }\n\n    // val is an array of strings so join them\n    if (allStrings(val)) {\n      const joined = val.join(this.sep);  // creates t1.t2.t3 in case multiple named transformations were configured\n      if (!isEmpty(joined)) {\n        // in case options.transformation was not set with an empty string (val != ['']);\n        result = `${this.shortName}_${joined}`;\n      }\n    } else { // Convert val to an array of strings\n      result = val.map(t => {\n        if (isString(t) && !isEmpty(t)) {\n          return `${this.shortName}_${t}`;\n        }\n        if (isFunction(t.serialize)) {\n          return t.serialize();\n        }\n        if (isPlainObject(t) && !isEmpty(t)) {\n          return new Transformation(t).serialize();\n        }\n        return undefined;\n      }).filter(t=>t);\n    }\n    return result;\n  }\n\n  set(origValue1) {\n    this.origValue = origValue1;\n    if (isArray(this.origValue)) {\n      return super.set(this.origValue);\n    } else {\n      return super.set([this.origValue]);\n    }\n  }\n}\n\nconst number_pattern = \"([0-9]*)\\\\.([0-9]+)|([0-9]+)\";\nconst offset_any_pattern = \"(\" + number_pattern + \")([%pP])?\";\n\nclass RangeParam extends Param {\n\n  /**\n   * A parameter that represents a range\n   * @param {string} name - The name of the parameter in snake_case\n   * @param {string} shortName - The name of the serialized form of the parameter\n   *                         If a value is not provided, the parameter will not be serialized.\n   * @param {function} [process=norm_range_value ] - Manipulate origValue when value is called\n   * @class RangeParam\n   * @extends Param\n   * @ignore\n   */\n  constructor(name, shortName, process = RangeParam.norm_range_value) {\n    super(name, shortName, process);\n  }\n  static norm_range_value(value) {\n\n    let offset = String(value).match(new RegExp('^' + offset_any_pattern + '$'));\n    if (offset) {\n      let modifier = offset[5] != null ? 'p' : '';\n      value = (offset[1] || offset[4]) + modifier;\n    }\n    return Expression.normalize(value);\n  }\n}\n\nclass RawParam extends Param {\n  constructor(name, shortName, process = identity) {\n    super(name, shortName, process);\n  }\n\n  serialize() {\n    return this.value();\n  }\n\n}\n\nclass LayerParam extends Param {\n  // Parse layer options\n  // @return [string] layer transformation string\n  // @private\n  value() {\n    if (this.origValue == null) {\n      return '';\n    }\n    let result;\n    if (this.origValue instanceof Layer) {\n      result = this.origValue;\n    } else if (isPlainObject(this.origValue)) {\n      let layerOptions = withCamelCaseKeys(this.origValue);\n      if (layerOptions.resourceType === \"text\" || (layerOptions.text != null)) {\n        result = new TextLayer(layerOptions);\n      } else if (layerOptions.resourceType === \"subtitles\") {\n        result = new SubtitlesLayer(layerOptions);\n      } else if (layerOptions.resourceType === \"fetch\" || (layerOptions.url != null)) {\n        result = new FetchLayer(layerOptions);\n      } else {\n        result = new Layer(layerOptions);\n      }\n    } else if (isString(this.origValue)) {\n      if (/^fetch:.+/.test(this.origValue)) {\n        result = new FetchLayer(this.origValue.substr(6));\n      } else {\n        result = this.origValue;\n      }\n    } else {\n      result = '';\n    }\n    return result.toString();\n  }\n\n  static textStyle(layer) {\n    return (new TextLayer(layer)).textStyleIdentifier();\n  }\n}\n\nclass ExpressionParam extends Param {\n  serialize() {\n    return Expression.normalize(super.serialize());\n  }\n}\n\nexport {\n  Param,\n  ArrayParam,\n  TransformationParam,\n  RangeParam,\n  RawParam,\n  LayerParam,\n  ExpressionParam\n};\n", "import Expression from './expression';\nimport Condition from './condition';\nimport Configuration from './configuration';\nimport {URL_KEYS} from './constants';\n\nimport {\n  assign,\n  camelCase,\n  cloneDeep,\n  compact,\n  contains,\n  difference,\n  identity,\n  isArray,\n  isEmpty,\n  isFunction,\n  isPlainObject,\n  isString,\n  snakeCase\n} from './util';\n\nimport {\n  Param,\n  ArrayParam,\n  LayerParam,\n  RangeParam,\n  RawParam,\n  TransformationParam\n} from \"./parameters\";\n\n/**\n * Assign key, value to target, when value is not null.<br>\n *   This function mutates the target!\n * @param {object} target the object to assign the values to\n * @param {object} sources one or more objects to get values from\n * @returns {object} the target after the assignment\n */\nfunction assignNotNull(target, ...sources) {\n  sources.forEach(source => {\n    Object.keys(source).forEach(key => {\n      if (source[key] != null) {\n        target[key] = source[key];\n      }\n    });\n  });\n  return target;\n}\n\n/**\n * TransformationBase\n * Depends on 'configuration', 'parameters','util'\n * @internal\n */\n\nclass TransformationBase {\n  /**\n   * The base class for transformations.\n   * Members of this class are documented as belonging to the {@link Transformation} class for convenience.\n   * @class TransformationBase\n   */\n  constructor(options) {\n    /** @private */\n    /** @private */\n    var parent, trans;\n    parent = void 0;\n    trans = {};\n    /**\n     * Return an options object that can be used to create an identical Transformation\n     * @function Transformation#toOptions\n     * @return {Object} Returns a plain object representing this transformation\n     */\n    this.toOptions = function (withChain) {\n      let opt = {};\n      if(withChain == null) {\n        withChain = true;\n      }\n      Object.keys(trans).forEach(key => opt[key] = trans[key].origValue);\n      assignNotNull(opt, this.otherOptions);\n      if (withChain && !isEmpty(this.chained)) {\n        let list = this.chained.map(tr => tr.toOptions());\n        list.push(opt);\n        opt = {};\n        assignNotNull(opt, this.otherOptions);\n        opt.transformation = list;\n      }\n      return opt;\n    };\n    /**\n     * Set a parent for this object for chaining purposes.\n     *\n     * @function Transformation#setParent\n     * @param {Object} object - the parent to be assigned to\n     * @returns {Transformation} Returns this instance for chaining purposes.\n     */\n    this.setParent = function (object) {\n      parent = object;\n      if (object != null) {\n        this.fromOptions(typeof object.toOptions === \"function\" ? object.toOptions() : void 0);\n      }\n      return this;\n    };\n    /**\n     * Returns the parent of this object in the chain\n     * @function Transformation#getParent\n     * @protected\n     * @return {Object} Returns the parent of this object if there is any\n     */\n    this.getParent = function () {\n      return parent;\n    };\n\n    // Helper methods to create parameter methods\n    // These methods are defined here because they access `trans` which is\n    // a private member of `TransformationBase`\n\n    /** @protected */\n    this.param = function (value, name, abbr, defaultValue, process) {\n      if (process == null) {\n        if (isFunction(defaultValue)) {\n          process = defaultValue;\n        } else {\n          process = identity;\n        }\n      }\n      trans[name] = new Param(name, abbr, process).set(value);\n      return this;\n    };\n    /** @protected */\n    this.rawParam = function (value, name, abbr, defaultValue, process) {\n      process = lastArgCallback(arguments);\n      trans[name] = new RawParam(name, abbr, process).set(value);\n      return this;\n    };\n    /** @protected */\n    this.rangeParam = function (value, name, abbr, defaultValue, process) {\n      process = lastArgCallback(arguments);\n      trans[name] = new RangeParam(name, abbr, process).set(value);\n      return this;\n    };\n    /** @protected */\n    this.arrayParam = function (value, name, abbr, sep = \":\", defaultValue = [], process = undefined) {\n      process = lastArgCallback(arguments);\n      trans[name] = new ArrayParam(name, abbr, sep, process).set(value);\n      return this;\n    };\n    /** @protected */\n    this.transformationParam = function (value, name, abbr, sep = \".\", defaultValue = undefined, process = undefined) {\n      process = lastArgCallback(arguments);\n      trans[name] = new TransformationParam(name, abbr, sep, process).set(value);\n      return this;\n    };\n    this.layerParam = function (value, name, abbr) {\n      trans[name] = new LayerParam(name, abbr).set(value);\n      return this;\n    };\n\n    // End Helper methods\n\n    /**\n     * Get the value associated with the given name.\n     * @function Transformation#getValue\n     * @param {string} name - the name of the parameter\n     * @return {*} the processed value associated with the given name\n     * @description Use {@link get}.origValue for the value originally provided for the parameter\n     */\n    this.getValue = function (name) {\n      let value = trans[name] && trans[name].value();\n      return value != null ? value : this.otherOptions[name];\n    };\n    /**\n     * Get the parameter object for the given parameter name\n     * @function Transformation#get\n     * @param {string} name the name of the transformation parameter\n     * @returns {Param} the param object for the given name, or undefined\n     */\n    this.get = function (name) {\n      return trans[name];\n    };\n    /**\n     * Remove a transformation option from the transformation.\n     * @function Transformation#remove\n     * @param {string} name - the name of the option to remove\n     * @return {*} Returns the option that was removed or null if no option by that name was found. The type of the\n     *              returned value depends on the value.\n     */\n    this.remove = function (name) {\n      var temp;\n      switch (false) {\n        case trans[name] == null:\n          temp = trans[name];\n          delete trans[name];\n          return temp.origValue;\n        case this.otherOptions[name] == null:\n          temp = this.otherOptions[name];\n          delete this.otherOptions[name];\n          return temp;\n        default:\n          return null;\n      }\n    };\n    /**\n     * Return an array of all the keys (option names) in the transformation.\n     * @return {Array<string>} the keys in snakeCase format\n     */\n    this.keys = function () {\n      var key;\n      return ((function () {\n        var results;\n        results = [];\n        for (key in trans) {\n          if (key != null) {\n            results.push(key.match(VAR_NAME_RE) ? key : snakeCase(key));\n          }\n        }\n        return results;\n      })()).sort();\n    };\n    /**\n     * Returns a plain object representation of the transformation. Values are processed.\n     * @function Transformation#toPlainObject\n     * @return {Object} the transformation options as plain object\n     */\n    this.toPlainObject = function () {\n      var hash, key, list;\n      hash = {};\n      for (key in trans) {\n        hash[key] = trans[key].value();\n        if (isPlainObject(hash[key])) {\n          hash[key] = cloneDeep(hash[key]);\n        }\n      }\n      if (!isEmpty(this.chained)) {\n        list = this.chained.map(tr => tr.toPlainObject());\n        list.push(hash);\n        hash = {\n          transformation: list\n        };\n      }\n      return hash;\n    };\n    /**\n     * Complete the current transformation and chain to a new one.\n     * In the URL, transformations are chained together by slashes.\n     * @function Transformation#chain\n     * @return {Transformation} Returns this transformation for chaining\n     * @example\n     * var tr = cloudinary.Transformation.new();\n     * tr.width(10).crop('fit').chain().angle(15).serialize()\n     * // produces \"c_fit,w_10/a_15\"\n     */\n    this.chain = function () {\n      var names, tr;\n      names = Object.getOwnPropertyNames(trans);\n      if (names.length !== 0) {\n        tr = new this.constructor(this.toOptions(false));\n        this.resetTransformations();\n        this.chained.push(tr);\n      }\n      return this;\n    };\n    this.resetTransformations = function () {\n      trans = {};\n      return this;\n    };\n    this.otherOptions = {};\n    this.chained = [];\n    this.fromOptions(options);\n  }\n\n  /**\n   * Merge the provided options with own's options\n   * @param {Object} [options={}] key-value list of options\n   * @returns {Transformation} Returns this instance for chaining\n   */\n  fromOptions(options = {}) {\n    if (options instanceof TransformationBase) {\n      this.fromTransformation(options);\n    } else {\n      if (isString(options) || isArray(options)) {\n        options = {\n          transformation: options\n        };\n      }\n      options = cloneDeep(options, function (value) {\n        if (value instanceof TransformationBase || value instanceof Layer) {\n          return new value.clone();\n        }\n      });\n      // Handling of \"if\" statements precedes other options as it creates a chained transformation\n      if (options[\"if\"]) {\n        this.set(\"if\", options[\"if\"]);\n        delete options[\"if\"];\n      }\n      for (let key in options) {\n        let opt = options[key];\n        if(opt != null) {\n          if (key.match(VAR_NAME_RE)) {\n            if (key !== '$attr') {\n              this.set('variable', key, opt);\n            }\n          } else {\n            this.set(key, opt);\n          }\n        }\n      }\n    }\n    return this;\n  }\n\n  fromTransformation(other) {\n    if (other instanceof TransformationBase) {\n      other.keys().forEach(key =>\n        this.set(key, other.get(key).origValue)\n      );\n    }\n    return this;\n  }\n\n  /**\n   * Set a parameter.\n   * The parameter name `key` is converted to\n   * @param {string} key - the name of the parameter\n   * @param {*} values - the value of the parameter\n   * @returns {Transformation} Returns this instance for chaining\n   */\n  set(key, ...values) {\n    var camelKey;\n    camelKey = camelCase(key);\n    if (contains(Transformation.methods, camelKey)) {\n      this[camelKey].apply(this, values);\n    } else {\n      this.otherOptions[key] = values[0];\n    }\n    return this;\n  }\n\n  hasLayer() {\n    return this.getValue(\"overlay\") || this.getValue(\"underlay\");\n  }\n\n  /**\n   * Generate a string representation of the transformation.\n   * @function Transformation#serialize\n   * @return {string} Returns the transformation as a string\n   */\n  serialize() {\n    var ifParam, j, len, paramList, ref, ref1, ref2, ref3, ref4, resultArray, t, transformationList,\n      transformationString, transformations, value, variables, vars;\n    resultArray = this.chained.map(tr => tr.serialize());\n    paramList = this.keys();\n    transformations = (ref = this.get(\"transformation\")) != null ? ref.serialize() : void 0;\n    ifParam = (ref1 = this.get(\"if\")) != null ? ref1.serialize() : void 0;\n    variables = processVar((ref2 = this.get(\"variables\")) != null ? ref2.value() : void 0);\n    paramList = difference(paramList, [\"transformation\", \"if\", \"variables\"]);\n    vars = [];\n    transformationList = [];\n    for (j = 0, len = paramList.length; j < len; j++) {\n      t = paramList[j];\n      if (t.match(VAR_NAME_RE)) {\n        vars.push(t + \"_\" + Expression.normalize((ref3 = this.get(t)) != null ? ref3.value() : void 0));\n      } else {\n        transformationList.push((ref4 = this.get(t)) != null ? ref4.serialize() : void 0);\n      }\n    }\n    switch (false) {\n      case !isString(transformations):\n        transformationList.push(transformations);\n        break;\n      case !isArray(transformations):\n        resultArray = resultArray.concat(transformations);\n    }\n    transformationList = (function () {\n      var k, len1, results;\n      results = [];\n      for (k = 0, len1 = transformationList.length; k < len1; k++) {\n        value = transformationList[k];\n        if (isArray(value) && !isEmpty(value) || !isArray(value) && value) {\n          results.push(value);\n        }\n      }\n      return results;\n    })();\n    transformationList = vars.sort().concat(variables).concat(transformationList.sort());\n    if (ifParam === \"if_end\") {\n      transformationList.push(ifParam);\n    } else if (!isEmpty(ifParam)) {\n      transformationList.unshift(ifParam);\n    }\n    transformationString = compact(transformationList).join(this.param_separator);\n    if (!isEmpty(transformationString)) {\n      resultArray.push(transformationString);\n    }\n    return compact(resultArray).join(this.trans_separator);\n  }\n\n  /**\n   * Provide a list of all the valid transformation option names\n   * @function Transformation#listNames\n   * @private\n   * @return {Array<string>} a array of all the valid option names\n   */\n  static listNames() {\n    return Transformation.methods;\n  }\n\n  /**\n   * Returns the attributes for an HTML tag.\n   * @function Cloudinary.toHtmlAttributes\n   * @return PlainObject\n   */\n  toHtmlAttributes() {\n    let attrName, height, options, ref2, ref3, value, width;\n    options = {};\n    let snakeCaseKey;\n    Object.keys(this.otherOptions).forEach(key=>{\n      value = this.otherOptions[key];\n      snakeCaseKey = snakeCase(key);\n      if (!contains(Transformation.PARAM_NAMES, snakeCaseKey) && !contains(URL_KEYS, snakeCaseKey)) {\n        attrName = /^html_/.test(key) ? key.slice(5) : key;\n        options[attrName] = value;\n      }\n    });\n    // convert all \"html_key\" to \"key\" with the same value\n    this.keys().forEach(key => {\n      if (/^html_/.test(key)) {\n        options[camelCase(key.slice(5))] = this.getValue(key);\n      }\n    });\n    if (!(this.hasLayer() || this.getValue(\"angle\") || contains([\"fit\", \"limit\", \"lfill\"], this.getValue(\"crop\")))) {\n      width = (ref2 = this.get(\"width\")) != null ? ref2.origValue : void 0;\n      height = (ref3 = this.get(\"height\")) != null ? ref3.origValue : void 0;\n      if (parseFloat(width) >= 1.0) {\n        if (options.width == null) {\n          options.width = width;\n        }\n      }\n      if (parseFloat(height) >= 1.0) {\n        if (options.height == null) {\n          options.height = height;\n        }\n      }\n    }\n    return options;\n  }\n\n  static isValidParamName(name) {\n    return Transformation.methods.indexOf(camelCase(name)) >= 0;\n  }\n\n  /**\n   * Delegate to the parent (up the call chain) to produce HTML\n   * @function Transformation#toHtml\n   * @return {string} HTML representation of the parent if possible.\n   * @example\n   * tag = cloudinary.ImageTag.new(\"sample\", {cloud_name: \"demo\"})\n   * // ImageTag {name: \"img\", publicId: \"sample\"}\n   * tag.toHtml()\n   * // <img src=\"http://res.cloudinary.com/demo/image/upload/sample\">\n   * tag.transformation().crop(\"fit\").width(300).toHtml()\n   * // <img src=\"http://res.cloudinary.com/demo/image/upload/c_fit,w_300/sample\">\n   */\n  toHtml() {\n    var ref;\n    return (ref = this.getParent()) != null ? typeof ref.toHtml === \"function\" ? ref.toHtml() : void 0 : void 0;\n  }\n\n  toString() {\n    return this.serialize();\n  }\n\n  clone() {\n    return new this.constructor(this.toOptions(true));\n  }\n}\n\nconst VAR_NAME_RE = /^\\$[a-zA-Z0-9]+$/;\n\nTransformationBase.prototype.trans_separator = '/';\n\nTransformationBase.prototype.param_separator = ',';\n\n\nfunction lastArgCallback(args) {\n  var callback;\n  callback = args != null ? args[args.length - 1] : void 0;\n  if (isFunction(callback)) {\n    return callback;\n  } else {\n    return void 0;\n  }\n}\n\nfunction processVar(varArray) {\n  var j, len, name, results, v;\n  if (isArray(varArray)) {\n    results = [];\n    for (j = 0, len = varArray.length; j < len; j++) {\n      [name, v] = varArray[j];\n      results.push(`${name}_${Expression.normalize(v)}`);\n    }\n    return results;\n  } else {\n    return varArray;\n  }\n}\n\nfunction processCustomFunction({function_type, source}) {\n  if (function_type === 'remote') {\n    return [function_type, btoa(source)].join(\":\");\n  } else if (function_type === 'wasm') {\n    return [function_type, source].join(\":\");\n  }\n}\n\n/**\n * Transformation Class methods.\n * This is a list of the parameters defined in Transformation.\n * Values are camelCased.\n * @const Transformation.methods\n * @private\n * @ignore\n * @type {Array<string>}\n */\n/**\n * Parameters that are filtered out before passing the options to an HTML tag.\n *\n * The list of parameters is a combination of `Transformation::methods` and `Configuration::CONFIG_PARAMS`\n * @const {Array<string>} Transformation.PARAM_NAMES\n * @private\n * @ignore\n * @see toHtmlAttributes\n */\nclass Transformation extends TransformationBase {\n  /**\n   * Represents a single transformation.\n   * @class Transformation\n   * @example\n   * t = new cloudinary.Transformation();\n   * t.angle(20).crop(\"scale\").width(\"auto\");\n   *\n   * // or\n   *\n   * t = new cloudinary.Transformation( {angle: 20, crop: \"scale\", width: \"auto\"});\n   * @see <a href=\"https://cloudinary.com/documentation/image_transformation_reference\"\n   *  target=\"_blank\">Available image transformations</a>\n   * @see <a href=\"https://cloudinary.com/documentation/video_transformation_reference\"\n   *  target=\"_blank\">Available video transformations</a>\n   */\n  constructor(options) {\n    super(options);\n  }\n\n  /**\n   * Convenience constructor\n   * @param {Object} options\n   * @return {Transformation}\n   * @example cl = cloudinary.Transformation.new( {angle: 20, crop: \"scale\", width: \"auto\"})\n   */\n  static new(options) {\n    return new Transformation(options);\n  }\n\n  /*\n    Transformation Parameters\n  */\n  angle(value) {\n    return this.arrayParam(value, \"angle\", \"a\", \".\", Expression.normalize);\n  }\n\n  audioCodec(value) {\n    return this.param(value, \"audio_codec\", \"ac\");\n  }\n\n  audioFrequency(value) {\n    return this.param(value, \"audio_frequency\", \"af\");\n  }\n\n  aspectRatio(value) {\n    return this.param(value, \"aspect_ratio\", \"ar\", Expression.normalize);\n  }\n\n  background(value) {\n    return this.param(value, \"background\", \"b\", Param.norm_color);\n  }\n\n  bitRate(value) {\n    return this.param(value, \"bit_rate\", \"br\");\n  }\n\n  border(value) {\n    return this.param(value, \"border\", \"bo\", function (border) {\n      if (isPlainObject(border)) {\n        border = assign({}, {\n          color: \"black\",\n          width: 2\n        }, border);\n        return `${border.width}px_solid_${Param.norm_color(border.color)}`;\n      } else {\n        return border;\n      }\n    });\n  }\n\n  color(value) {\n    return this.param(value, \"color\", \"co\", Param.norm_color);\n  }\n\n  colorSpace(value) {\n    return this.param(value, \"color_space\", \"cs\");\n  }\n\n  crop(value) {\n    return this.param(value, \"crop\", \"c\");\n  }\n\n  customFunction(value) {\n    return this.param(value, \"custom_function\", \"fn\", () => {\n      return processCustomFunction(value);\n    });\n  }\n\n  customPreFunction(value) {\n    if (this.get('custom_function')) {\n      return;\n    }\n    return this.rawParam(value, \"custom_function\", \"\", () => {\n      value = processCustomFunction(value);\n      return value ? `fn_pre:${value}` : value;\n    });\n  }\n\n  defaultImage(value) {\n    return this.param(value, \"default_image\", \"d\");\n  }\n\n  delay(value) {\n    return this.param(value, \"delay\", \"dl\");\n  }\n\n  density(value) {\n    return this.param(value, \"density\", \"dn\");\n  }\n\n  duration(value) {\n    return this.rangeParam(value, \"duration\", \"du\");\n  }\n\n  dpr(value) {\n    return this.param(value, \"dpr\", \"dpr\", (dpr) => {\n      dpr = dpr.toString();\n      if (dpr != null ? dpr.match(/^\\d+$/) : void 0) {\n        return dpr + \".0\";\n      } else {\n        return Expression.normalize(dpr);\n      }\n    });\n  }\n\n  effect(value) {\n    return this.arrayParam(value, \"effect\", \"e\", \":\", Expression.normalize);\n  }\n\n  else() {\n    return this.if('else');\n  }\n\n  endIf() {\n    return this.if('end');\n  }\n\n  endOffset(value) {\n    return this.rangeParam(value, \"end_offset\", \"eo\");\n  }\n\n  fallbackContent(value) {\n    return this.param(value, \"fallback_content\");\n  }\n\n  fetchFormat(value) {\n    return this.param(value, \"fetch_format\", \"f\");\n  }\n\n  format(value) {\n    return this.param(value, \"format\");\n  }\n\n  flags(value) {\n    return this.arrayParam(value, \"flags\", \"fl\", \".\");\n  }\n\n  gravity(value) {\n    return this.param(value, \"gravity\", \"g\");\n  }\n\n  fps(value) {\n    return this.param(value, \"fps\", \"fps\", (fps) => {\n      if (isString(fps)) {\n        return fps;\n      } else if (isArray(fps)) {\n        return fps.join(\"-\");\n      } else {\n        return fps;\n      }\n    });\n  }\n\n  height(value) {\n    return this.param(value, \"height\", \"h\", () => {\n      if (this.getValue(\"crop\") || this.getValue(\"overlay\") || this.getValue(\"underlay\")) {\n        return Expression.normalize(value);\n      } else {\n        return null;\n      }\n    });\n  }\n\n  htmlHeight(value) {\n    return this.param(value, \"html_height\");\n  }\n\n  htmlWidth(value) {\n    return this.param(value, \"html_width\");\n  }\n\n  if(value = \"\") {\n    var i, ifVal, j, ref, trIf, trRest;\n    switch (value) {\n      case \"else\":\n        this.chain();\n        return this.param(value, \"if\", \"if\");\n      case \"end\":\n        this.chain();\n        for (i = j = ref = this.chained.length - 1; j >= 0; i = j += -1) {\n          ifVal = this.chained[i].getValue(\"if\");\n          if (ifVal === \"end\") {\n            break;\n          } else if (ifVal != null) {\n            trIf = Transformation.new().if(ifVal);\n            this.chained[i].remove(\"if\");\n            trRest = this.chained[i];\n            this.chained[i] = Transformation.new().transformation([trIf, trRest]);\n            if (ifVal !== \"else\") {\n              break;\n            }\n          }\n        }\n        return this.param(value, \"if\", \"if\");\n      case \"\":\n        return Condition.new().setParent(this);\n      default:\n        return this.param(value, \"if\", \"if\", function (value) {\n          return Condition.new(value).toString();\n        });\n    }\n  }\n\n  keyframeInterval(value) {\n    return this.param(value, \"keyframe_interval\", \"ki\");\n  }\n\n  ocr(value) {\n    return this.param(value, \"ocr\", \"ocr\");\n  }\n\n  offset(value) {\n    var end_o, start_o;\n    [start_o, end_o] = (isFunction(value != null ? value.split : void 0)) ? value.split('..') : isArray(value) ? value : [null, null];\n    if (start_o != null) {\n      this.startOffset(start_o);\n    }\n    if (end_o != null) {\n      return this.endOffset(end_o);\n    }\n  }\n\n  opacity(value) {\n    return this.param(value, \"opacity\", \"o\", Expression.normalize);\n  }\n\n  overlay(value) {\n    return this.layerParam(value, \"overlay\", \"l\");\n  }\n\n  page(value) {\n    return this.param(value, \"page\", \"pg\");\n  }\n\n  poster(value) {\n    return this.param(value, \"poster\");\n  }\n\n  prefix(value) {\n    return this.param(value, \"prefix\", \"p\");\n  }\n\n  quality(value) {\n    return this.param(value, \"quality\", \"q\", Expression.normalize);\n  }\n\n  radius(value) {\n    return this.arrayParam(value, \"radius\", \"r\", \":\", Expression.normalize);\n  }\n\n  rawTransformation(value) {\n    return this.rawParam(value, \"raw_transformation\");\n  }\n\n  size(value) {\n    var height, width;\n    if (isFunction(value != null ? value.split : void 0)) {\n      [width, height] = value.split('x');\n      this.width(width);\n      return this.height(height);\n    }\n  }\n\n  sourceTypes(value) {\n    return this.param(value, \"source_types\");\n  }\n\n  sourceTransformation(value) {\n    return this.param(value, \"source_transformation\");\n  }\n\n  startOffset(value) {\n    return this.rangeParam(value, \"start_offset\", \"so\");\n  }\n\n  streamingProfile(value) {\n    return this.param(value, \"streaming_profile\", \"sp\");\n  }\n\n  transformation(value) {\n    return this.transformationParam(value, \"transformation\", \"t\");\n  }\n\n  underlay(value) {\n    return this.layerParam(value, \"underlay\", \"u\");\n  }\n\n  variable(name, value) {\n    return this.param(value, name, name);\n  }\n\n  variables(values) {\n    return this.arrayParam(values, \"variables\");\n  }\n\n  videoCodec(value) {\n    return this.param(value, \"video_codec\", \"vc\", Param.process_video_params);\n  }\n\n  videoSampling(value) {\n    return this.param(value, \"video_sampling\", \"vs\");\n  }\n\n  width(value) {\n    return this.param(value, \"width\", \"w\", () => {\n      if (this.getValue(\"crop\") || this.getValue(\"overlay\") || this.getValue(\"underlay\")) {\n        return Expression.normalize(value);\n      } else {\n        return null;\n      }\n    });\n  }\n\n  x(value) {\n    return this.param(value, \"x\", \"x\", Expression.normalize);\n  }\n\n  y(value) {\n    return this.param(value, \"y\", \"y\", Expression.normalize);\n  }\n\n  zoom(value) {\n    return this.param(value, \"zoom\", \"z\", Expression.normalize);\n  }\n\n}\n\n/**\n * Transformation Class methods.\n * This is a list of the parameters defined in Transformation.\n * Values are camelCased.\n */\nTransformation.methods = [\n  \"angle\",\n  \"audioCodec\",\n  \"audioFrequency\",\n  \"aspectRatio\",\n  \"background\",\n  \"bitRate\",\n  \"border\",\n  \"color\",\n  \"colorSpace\",\n  \"crop\",\n  \"customFunction\",\n  \"customPreFunction\",\n  \"defaultImage\",\n  \"delay\",\n  \"density\",\n  \"duration\",\n  \"dpr\",\n  \"effect\",\n  \"else\",\n  \"endIf\",\n  \"endOffset\",\n  \"fallbackContent\",\n  \"fetchFormat\",\n  \"format\",\n  \"flags\",\n  \"gravity\",\n  \"fps\",\n  \"height\",\n  \"htmlHeight\",\n  \"htmlWidth\",\n  \"if\",\n  \"keyframeInterval\",\n  \"ocr\",\n  \"offset\",\n  \"opacity\",\n  \"overlay\",\n  \"page\",\n  \"poster\",\n  \"prefix\",\n  \"quality\",\n  \"radius\",\n  \"rawTransformation\",\n  \"size\",\n  \"sourceTypes\",\n  \"sourceTransformation\",\n  \"startOffset\",\n  \"streamingProfile\",\n  \"transformation\",\n  \"underlay\",\n  \"variable\",\n  \"variables\",\n  \"videoCodec\",\n  \"videoSampling\",\n  \"width\",\n  \"x\",\n  \"y\",\n  \"zoom\"\n];\n\n/**\n * Parameters that are filtered out before passing the options to an HTML tag.\n *\n * The list of parameters is a combination of `Transformation::methods` and `Configuration::CONFIG_PARAMS`\n */\nTransformation.PARAM_NAMES = Transformation.methods.map(snakeCase).concat(Configuration.CONFIG_PARAMS);\n\nexport default Transformation;\n", "/**\n * Generic HTML tag\n * Depends on 'transformation', 'util'\n */\n\nimport {\n  isPlainObject,\n  isFunction,\n  getData,\n  hasClass,\n  merge,\n  isString\n} from '../util';\n\nimport Transformation from '../transformation';\n\n/**\n * Represents an HTML (DOM) tag\n * @constructor HtmlTag\n * @param {string} name - the name of the tag\n * @param {string} [publicId]\n * @param {Object} options\n * @example tag = new HtmlTag( 'div', { 'width': 10})\n */\nclass HtmlTag {\n  constructor(name, publicId, options) {\n    var transformation;\n    this.name = name;\n    this.publicId = publicId;\n    if (options == null) {\n      if (isPlainObject(publicId)) {\n        options = publicId;\n        this.publicId = void 0;\n      } else {\n        options = {};\n      }\n    }\n    transformation = new Transformation(options);\n    transformation.setParent(this);\n    this.transformation = function () {\n      return transformation;\n    };\n  }\n\n  /**\n   * Convenience constructor\n   * Creates a new instance of an HTML (DOM) tag\n   * @function HtmlTag.new\n   * @param {string} name - the name of the tag\n   * @param {string} [publicId]\n   * @param {Object} options\n   * @return {HtmlTag}\n   * @example tag = HtmlTag.new( 'div', { 'width': 10})\n   */\n  static new(name, publicId, options) {\n    return new this(name, publicId, options);\n  }\n\n  /**\n   * combine key and value from the `attr` to generate an HTML tag attributes string.\n   * `Transformation::toHtmlTagOptions` is used to filter out transformation and configuration keys.\n   * @protected\n   * @param {Object} attrs\n   * @return {string} the attributes in the format `'key1=\"value1\" key2=\"value2\"'`\n   * @ignore\n   */\n  htmlAttrs(attrs) {\n    var key, pairs, value;\n    return pairs = ((function () {\n      var results;\n      results = [];\n      for (key in attrs) {\n        value = escapeQuotes(attrs[key]);\n        if (value) {\n          results.push(toAttribute(key, value));\n        }\n      }\n      return results;\n    })()).sort().join(' ');\n  }\n\n  /**\n   * Get all options related to this tag.\n   * @function HtmlTag#getOptions\n   * @returns {Object} the options\n   *\n   */\n  getOptions() {\n    return this.transformation().toOptions();\n  }\n\n  /**\n   * Get the value of option `name`\n   * @function HtmlTag#getOption\n   * @param {string} name - the name of the option\n   * @returns {*} Returns the value of the option\n   *\n   */\n  getOption(name) {\n    return this.transformation().getValue(name);\n  }\n\n  /**\n   * Get the attributes of the tag.\n   * @function HtmlTag#attributes\n   * @returns {Object} attributes\n   */\n  attributes() {\n    // The attributes are be computed from the options every time this method is invoked.\n    let htmlAttributes = this.transformation().toHtmlAttributes();\n    Object.keys(htmlAttributes ).forEach(key => {\n      if(isPlainObject(htmlAttributes[key])){\n        delete htmlAttributes[key];\n      }\n    });\n    if( htmlAttributes.attributes) {\n      // Currently HTML attributes are defined both at the top level and under 'attributes'\n      merge(htmlAttributes, htmlAttributes.attributes);\n      delete htmlAttributes.attributes;\n    }\n\n    return htmlAttributes;\n  }\n\n  /**\n   * Set a tag attribute named `name` to `value`\n   * @function HtmlTag#setAttr\n   * @param {string} name - the name of the attribute\n   * @param {string} value - the value of the attribute\n   */\n  setAttr(name, value) {\n    this.transformation().set(`html_${name}`, value);\n    return this;\n  }\n\n  /**\n   * Get the value of the tag attribute `name`\n   * @function HtmlTag#getAttr\n   * @param {string} name - the name of the attribute\n   * @returns {*}\n   */\n  getAttr(name) {\n    return this.attributes()[`html_${name}`] || this.attributes()[name];\n  }\n\n  /**\n   * Remove the tag attributed named `name`\n   * @function HtmlTag#removeAttr\n   * @param {string} name - the name of the attribute\n   * @returns {*}\n   */\n  removeAttr(name) {\n    var ref;\n    return (ref = this.transformation().remove(`html_${name}`)) != null ? ref : this.transformation().remove(name);\n  }\n\n  /**\n   * @function HtmlTag#content\n   * @protected\n   * @ignore\n   */\n  content() {\n    return \"\";\n  }\n\n  /**\n   * @function HtmlTag#openTag\n   * @protected\n   * @ignore\n   */\n  openTag() {\n    let tag = \"<\" + this.name;\n    let htmlAttrs = this.htmlAttrs(this.attributes());\n    if(htmlAttrs && htmlAttrs.length > 0) {\n      tag += \" \" + htmlAttrs\n    }\n    return tag + \">\";\n  }\n\n  /**\n   * @function HtmlTag#closeTag\n   * @protected\n   * @ignore\n   */\n  closeTag() {\n    return `</${this.name}>`;\n  }\n\n  /**\n   * Generates an HTML representation of the tag.\n   * @function HtmlTag#toHtml\n   * @returns {string} Returns HTML in string format\n   */\n  toHtml() {\n    return this.openTag() + this.content() + this.closeTag();\n  }\n\n  /**\n   * Creates a DOM object representing the tag.\n   * @function HtmlTag#toDOM\n   * @returns {Element}\n   */\n  toDOM() {\n    var element, name, ref, value;\n    if (!isFunction(typeof document !== \"undefined\" && document !== null ? document.createElement : void 0)) {\n      throw \"Can't create DOM if document is not present!\";\n    }\n    element = document.createElement(this.name);\n    ref = this.attributes();\n    for (name in ref) {\n      value = ref[name];\n      element.setAttribute(name, value);\n    }\n    return element;\n  }\n\n  static isResponsive(tag, responsiveClass) {\n    var dataSrc;\n    dataSrc = getData(tag, 'src-cache') || getData(tag, 'src');\n    return hasClass(tag, responsiveClass) && /\\bw_auto\\b/.exec(dataSrc);\n  }\n\n};\n\n/**\n * Represent the given key and value as an HTML attribute.\n * @function toAttribute\n * @protected\n * @param {string} key - attribute name\n * @param {*|boolean} value - the value of the attribute. If the value is boolean `true`, return the key only.\n * @returns {string} the attribute\n *\n */\nfunction toAttribute(key, value) {\n  if (!value) {\n    return void 0;\n  } else if (value === true) {\n    return key;\n  } else {\n    return `${key}=\"${value}\"`;\n  }\n}\n\n/**\n * If given value is a string, replaces quotes with character entities (&#34;, &#39;)\n * @param value - value to change\n * @returns {*} changed value\n */\nfunction escapeQuotes(value) {\n  return isString(value) ? value.replace('\"', '&#34;').replace(\"'\", '&#39;') : value;\n}\n\nexport default HtmlTag;\n", "import Transformation from './transformation';\n\nimport {\n  ACCESSIBILITY_MODES,\n  DEFAULT_IMAGE_PARAMS,\n  OLD_AKAMAI_SHARED_CDN,\n  PLACEHOLDER_IMAGE_MODES,\n  SHARED_CDN,\n  SEO_TYPES\n} from './constants';\n\nimport {\n  defaults,\n  compact,\n  isPlainObject\n} from './util';\n\nimport crc32 from './crc32';\nimport getSDKAnalyticsSignature from \"./sdkAnalytics/getSDKAnalyticsSignature\";\nimport getAnalyticsOptions from \"./sdkAnalytics/getAnalyticsOptions\";\n\n\n/**\n * Adds protocol, host, pathname prefixes to given string\n * @param str\n * @returns {string}\n */\nfunction makeUrl(str) {\n    let prefix = document.location.protocol + '//' + document.location.host;\n    if (str[0] === '?') {\n      prefix += document.location.pathname;\n    } else if (str[0] !== '/') {\n      prefix += document.location.pathname.replace(/\\/[^\\/]*$/, '/');\n    }\n    return prefix + str;\n}\n\n/**\n * Check is given string is a url\n * @param str\n * @returns {boolean}\n */\nfunction isUrl(str){\n  return str ? !!str.match(/^https?:\\//) : false;\n}\n\n// Produce a number between 1 and 5 to be used for cdn sub domains designation\nfunction cdnSubdomainNumber(publicId) {\n  return crc32(publicId) % 5 + 1;\n}\n\n/**\n * Removes signature from options and returns the signature\n * Makes sure signature is empty or of this format: s--signature--\n * @param {object} options\n * @returns {string} the formatted signature\n */\nfunction handleSignature(options) {\n  const {signature} = options;\n  const isFormatted = !signature || (signature.indexOf('s--') === 0 && signature.substr(-2) === '--');\n  delete options.signature;\n\n  return isFormatted ? signature : `s--${signature}--`;\n}\n\n/**\n * Create the URL prefix for Cloudinary resources.\n * @param {string} publicId the resource public ID\n * @param {object} options additional options\n * @param {string} options.cloud_name - the cloud name.\n * @param {boolean} [options.cdn_subdomain=false] - Whether to automatically build URLs with\n *  multiple CDN sub-domains.\n * @param {string} [options.private_cdn] - Boolean (default: false). Should be set to true for Advanced plan's users\n *  that have a private CDN distribution.\n * @param {string} [options.protocol=\"http://\"] - the URI protocol to use. If options.secure is true,\n *  the value is overridden to \"https://\"\n * @param {string} [options.secure_distribution] - The domain name of the CDN distribution to use for building HTTPS URLs.\n *  Relevant only for Advanced plan's users that have a private CDN distribution.\n * @param {string} [options.cname] - Custom domain name to use for building HTTP URLs.\n *  Relevant only for Advanced plan's users that have a private CDN distribution and a custom CNAME.\n * @param {boolean} [options.secure_cdn_subdomain=true] - When options.secure is true and this parameter is false,\n *  the subdomain is set to \"res\".\n * @param {boolean} [options.secure=false] - Force HTTPS URLs of images even if embedded in non-secure HTTP pages.\n *  When this value is true, options.secure_distribution will be used as host if provided, and options.protocol is set\n *  to \"https://\".\n * @returns {string} the URL prefix for the resource.\n * @private\n */\nfunction handlePrefix(publicId, options) {\n  if (options.cloud_name && options.cloud_name[0] === '/') {\n    return '/res' + options.cloud_name;\n  }\n  // defaults\n  let protocol = \"http://\";\n  let cdnPart = \"\";\n  let subdomain = \"res\";\n  let host = \".cloudinary.com\";\n  let path = \"/\" + options.cloud_name;\n  // modifications\n  if (options.protocol) {\n    protocol = options.protocol + '//';\n  }\n  if (options.private_cdn) {\n    cdnPart = options.cloud_name + \"-\";\n    path = \"\";\n  }\n  if (options.cdn_subdomain) {\n    subdomain = \"res-\" + cdnSubdomainNumber(publicId);\n  }\n  if (options.secure) {\n    protocol = \"https://\";\n    if (options.secure_cdn_subdomain === false) {\n      subdomain = \"res\";\n    }\n    if ((options.secure_distribution != null) && options.secure_distribution !== OLD_AKAMAI_SHARED_CDN && options.secure_distribution !== SHARED_CDN) {\n      cdnPart = \"\";\n      subdomain = \"\";\n      host = options.secure_distribution;\n    }\n  } else if (options.cname) {\n    protocol = \"http://\";\n    cdnPart = \"\";\n    subdomain = options.cdn_subdomain ? 'a' + ((crc32(publicId) % 5) + 1) + '.' : '';\n    host = options.cname;\n  }\n  return [protocol, cdnPart, subdomain, host, path].join(\"\");\n}\n\n/**\n * Return the resource type and action type based on the given configuration\n * @function Cloudinary#handleResourceType\n * @param {Object|string} resource_type\n * @param {string} [type='upload']\n * @param {string} [url_suffix]\n * @param {boolean} [use_root_path]\n * @param {boolean} [shorten]\n * @returns {string} resource_type/type\n * @ignore\n */\nfunction handleResourceType({resource_type = \"image\", type = \"upload\", url_suffix, use_root_path, shorten}) {\n  let options, resourceType = resource_type;\n\n  if (isPlainObject(resourceType)) {\n    options = resourceType;\n    resourceType = options.resource_type;\n    type = options.type;\n    shorten = options.shorten;\n  }\n  if (type == null) {\n    type = 'upload';\n  }\n  if (url_suffix != null) {\n    resourceType = SEO_TYPES[`${resourceType}/${type}`];\n    type = null;\n    if (resourceType == null) {\n      throw new Error(`URL Suffix only supported for ${Object.keys(SEO_TYPES).join(', ')}`);\n    }\n  }\n  if (use_root_path) {\n    if (resourceType === 'image' && type === 'upload' || resourceType === \"images\") {\n      resourceType = null;\n      type = null;\n    } else {\n      throw new Error(\"Root path only supported for image/upload\");\n    }\n  }\n  if (shorten && resourceType === 'image' && type === 'upload') {\n    resourceType = 'iu';\n    type = null;\n  }\n  return [resourceType, type].join(\"/\");\n}\n\n/**\n * Encode publicId\n * @param publicId\n * @returns {string} encoded publicId\n */\nfunction encodePublicId(publicId) {\n  return encodeURIComponent(publicId).replace(/%3A/g, ':').replace(/%2F/g, '/');\n}\n\n/**\n * Encode and format publicId\n * @param publicId\n * @param options\n * @returns {string} publicId\n */\nfunction formatPublicId(publicId, options) {\n  if (isUrl(publicId)){\n    publicId = encodePublicId(publicId);\n  } else {\n    try {\n      // Make sure publicId is URI encoded.\n      publicId = decodeURIComponent(publicId);\n    } catch (error) {}\n\n    publicId = encodePublicId(publicId);\n\n    if (options.url_suffix) {\n      publicId = publicId + '/' + options.url_suffix;\n    }\n    if (options.format) {\n      if (!options.trust_public_id) {\n        publicId = publicId.replace(/\\.(jpg|png|gif|webp)$/, '');\n      }\n      publicId = publicId + '.' + options.format;\n    }\n  }\n  return publicId;\n}\n\n/**\n * Get any error with url options\n * @param options\n * @returns {string} if error, otherwise return undefined\n */\nfunction validate(options) {\n  const {cloud_name, url_suffix} = options;\n\n  if (!cloud_name) {\n    return 'Unknown cloud_name';\n  }\n\n  if (url_suffix && url_suffix.match(/[\\.\\/]/)) {\n    return 'url_suffix should not include . or /';\n  }\n}\n\n/**\n * Get version part of the url\n * @param publicId\n * @param options\n * @returns {string}\n */\nfunction handleVersion(publicId, options) {\n  // force_version param means to make sure there is a version in the url (Default is true)\n  const isForceVersion = (options.force_version || typeof options.force_version === 'undefined');\n\n  // Is version included in publicId or in options, or publicId is a url (doesn't need version)\n  const isVersionExist = (publicId.indexOf('/') < 0 || publicId.match(/^v[0-9]+/) || isUrl(publicId)) || options.version;\n\n  if (isForceVersion && !isVersionExist) {\n    options.version = 1;\n  }\n\n  return options.version ? `v${options.version}` : '';\n}\n\n/**\n * Get final transformation component for url string\n * @param options\n * @returns {string}\n */\nfunction handleTransformation(options) {\n  let {placeholder, accessibility, ...otherOptions} = options || {};\n  const result = new Transformation(otherOptions);\n\n  // Append accessibility transformations\n  if (accessibility && ACCESSIBILITY_MODES[accessibility]) {\n    result.chain().effect(ACCESSIBILITY_MODES[accessibility]);\n  }\n\n  // Append placeholder transformations\n  if (placeholder) {\n    if (placeholder === \"predominant-color\" && result.getValue('width') && result.getValue('height')) {\n      placeholder += '-pixel';\n    }\n    const placeholderTransformations = PLACEHOLDER_IMAGE_MODES[placeholder] || PLACEHOLDER_IMAGE_MODES.blur;\n    placeholderTransformations.forEach(t => result.chain().transformation(t));\n  }\n\n  return result.serialize();\n}\n\n/**\n * If type is 'fetch', update publicId to be a url\n * @param publicId\n * @param type\n * @returns {string}\n */\nfunction preparePublicId(publicId, {type}){\n  return (!isUrl(publicId) && type === 'fetch') ? makeUrl(publicId) : publicId;\n}\n\n/**\n * Generate url string\n * @param publicId\n * @param options\n * @returns {string} final url\n */\nfunction urlString(publicId, options) {\n  if (isUrl(publicId) && (options.type === 'upload' || options.type === 'asset')) {\n    return publicId;\n  }\n\n  const version = handleVersion(publicId, options);\n  const transformationString = handleTransformation(options);\n  const prefix = handlePrefix(publicId, options);\n  const signature = handleSignature(options);\n  const resourceType = handleResourceType(options);\n\n  publicId = formatPublicId(publicId, options);\n\n  return compact([prefix, resourceType, signature, transformationString, version, publicId])\n    .join('/')\n    .replace(/([^:])\\/+/g, '$1/') // replace '///' with '//'\n    .replace(' ', '%20');\n}\n\n/**\n * Merge options and config with defaults\n * update options fetch_format according to 'type' param\n * @param options\n * @param config\n * @returns {*} updated options\n */\nfunction prepareOptions(options, config) {\n  if (options instanceof Transformation) {\n    options = options.toOptions();\n  }\n\n  options = defaults({}, options, config, DEFAULT_IMAGE_PARAMS);\n\n  if (options.type === 'fetch') {\n    options.fetch_format = options.fetch_format || options.format;\n  }\n\n  return options;\n}\n\n/**\n * Generates a URL for any asset in your Media library.\n * @function url\n * @ignore\n * @param {string} publicId - The public ID of the media asset.\n * @param {Object} [options={}] - The {@link Transformation} parameters to include in the URL.\n * @param {object} [config={}] - URL configuration parameters\n * @param {type} [options.type='upload'] - The asset's storage type.\n *  For details on all fetch types, see\n * <a href=\"https://cloudinary.com/documentation/image_transformations#fetching_images_from_remote_locations\"\n *  target=\"_blank\">Fetch types</a>.\n * @param {Object} [options.resource_type='image'] - The type of asset. <p>Possible values:<br/>\n *  - `image`<br/>\n *  - `video`<br/>\n *  - `raw`\n * @param {signature} [options.signature='s--12345678--'] - The signature component of a\n *  signed delivery URL of the format: /s--SIGNATURE--/.\n *  For details on signatures, see\n * <a href=\"https://cloudinary.com/documentation/signatures\" target=\"_blank\">Signatures</a>.\n * @return {string} The media asset URL.\n * @see <a href=\"https://cloudinary.com/documentation/image_transformation_reference\" target=\"_blank\">\n *  Available image transformations</a>\n * @see <a href=\"https://cloudinary.com/documentation/video_transformation_reference\" target=\"_blank\">\n *  Available video transformations</a>\n */\nexport default function url(publicId, options = {}, config = {}) {\n  if (!publicId) {\n    return publicId;\n  }\n  options = prepareOptions(options, config);\n  publicId = preparePublicId(publicId, options);\n\n  const error = validate(options);\n\n  if (error) {\n    throw error;\n  }\n  let resultUrl = urlString(publicId, options);\n  if(options.urlAnalytics) {\n    let analyticsOptions = getAnalyticsOptions(options);\n    let sdkAnalyticsSignature = getSDKAnalyticsSignature(analyticsOptions);\n    // url might already have a '?' query param\n    let appender = '?';\n    if (resultUrl.indexOf('?') >= 0) {\n      appender = '&';\n    }\n    resultUrl = `${resultUrl}${appender}_a=${sdkAnalyticsSignature}`;\n  }\n  if (options.auth_token) {\n    let appender = resultUrl.indexOf('?') >= 0 ? '&' : '?';\n    resultUrl = `${resultUrl}${appender}__cld_token__=${options.auth_token}`;\n  }\n  return resultUrl;\n};\n", "\n/**\n * Helper function. Gets or populates srcset breakpoints using provided parameters\n * Either the breakpoints or min_width, max_width, max_images must be provided.\n *\n * @private\n * @param {srcset} srcset Options with either `breakpoints` or `min_width`, `max_width`, and `max_images`\n *\n * @return {number[]} Array of breakpoints\n *\n */\nexport default function generateBreakpoints(srcset) {\n  let breakpoints = srcset.breakpoints || [];\n  if (breakpoints.length) {\n    return breakpoints;\n  }\n  let [min_width, max_width, max_images] = [srcset.min_width, srcset.max_width, srcset.max_images].map(Number);\n  if ([min_width, max_width, max_images].some(isNaN)) {\n    throw 'Either (min_width, max_width, max_images) ' +\n    'or breakpoints must be provided to the image srcset attribute';\n  }\n\n  if (min_width > max_width) {\n    throw 'min_width must be less than max_width'\n  }\n\n  if (max_images <= 0) {\n    throw 'max_images must be a positive integer';\n  } else if (max_images === 1) {\n    min_width = max_width;\n  }\n\n  let stepSize = Math.ceil((max_width - min_width) / Math.max(max_images - 1, 1));\n  for (let current = min_width; current < max_width; current += stepSize) {\n    breakpoints.push(current);\n  }\n  breakpoints.push(max_width);\n  return breakpoints;\n}\n", "import * as utils from '../util';\n\nconst isEmpty = utils.isEmpty;\nimport generateBreakpoints from './generateBreakpoints';\nimport Transformation from '../transformation';\nimport url from '../url';\n\n/**\n * Options used to generate the srcset attribute.\n * @typedef {object} srcset\n * @property {(number[]|string[])}   [breakpoints] An array of breakpoints.\n * @property {number}                [min_width]   Minimal width of the srcset images.\n * @property {number}                [max_width]   Maximal width of the srcset images.\n * @property {number}                [max_images]  Number of srcset images to generate.\n * @property {object|string}         [transformation] The transformation to use in the srcset urls.\n * @property {boolean}               [sizes] Whether to calculate and add the sizes attribute.\n */\n\n/**\n * Helper function. Generates a single srcset item url\n *\n * @private\n * @param {string} public_id  Public ID of the resource.\n * @param {number} width      Width in pixels of the srcset item.\n * @param {object|string} transformation\n * @param {object} options    Additional options.\n *\n * @return {string} Resulting URL of the item\n */\nexport function scaledUrl(public_id, width, transformation, options = {}) {\n  let configParams = utils.extractUrlParams(options);\n  transformation = transformation || options;\n  configParams.raw_transformation = new Transformation([utils.merge({}, transformation), {\n    crop: 'scale',\n    width: width\n  }]).toString();\n\n  return url(public_id, configParams);\n}\n\n/**\n * If cache is enabled, get the breakpoints from the cache. If the values were not found in the cache,\n * or cache is not enabled, generate the values.\n * @param {srcset} srcset The srcset configuration parameters\n * @param {string} public_id\n * @param {object} options\n * @return {*|Array}\n */\nexport function getOrGenerateBreakpoints(public_id, srcset = {}, options = {}) {\n  return generateBreakpoints(srcset);\n}\n\n/**\n * Helper function. Generates srcset attribute value of the HTML img tag\n * @private\n *\n * @param {string} public_id  Public ID of the resource\n * @param {number[]} breakpoints An array of breakpoints (in pixels)\n * @param {object} transformation The transformation\n * @param {object} options Includes html tag options, transformation options\n * @return {string} Resulting srcset attribute value\n */\nexport function generateSrcsetAttribute(public_id, breakpoints, transformation, options) {\n  options = utils.cloneDeep(options);\n  utils.patchFetchFormat(options);\n  return breakpoints.map(width => `${scaledUrl(public_id, width, transformation, options)} ${width}w`).join(', ');\n}\n\n/**\n * Helper function. Generates sizes attribute value of the HTML img tag\n * @private\n * @param {number[]} breakpoints An array of breakpoints.\n * @return {string} Resulting sizes attribute value\n */\nexport function generateSizesAttribute(breakpoints) {\n  if (breakpoints == null) {\n    return '';\n  }\n  return breakpoints.map(width => `(max-width: ${width}px) ${width}px`).join(', ');\n}\n\n/**\n * Helper function. Generates srcset and sizes attributes of the image tag\n *\n * Generated attributes are added to attributes argument\n *\n * @private\n * @param {string}    publicId  The public ID of the resource\n * @param {object}    attributes Existing HTML attributes.\n * @param {srcset}    srcsetData\n * @param {object}    options    Additional options.\n *\n * @return array The responsive attributes\n */\nexport function generateImageResponsiveAttributes(publicId, attributes = {}, srcsetData = {}, options = {}) {\n  // Create both srcset and sizes here to avoid fetching breakpoints twice\n\n  let responsiveAttributes = {};\n  if (isEmpty(srcsetData)) {\n    return responsiveAttributes;\n  }\n\n  const generateSizes = (!attributes.sizes && srcsetData.sizes === true);\n\n  const generateSrcset = !attributes.srcset;\n  if (generateSrcset || generateSizes) {\n    let breakpoints = getOrGenerateBreakpoints(publicId, srcsetData, options);\n\n    if (generateSrcset) {\n      let transformation = srcsetData.transformation;\n      let srcsetAttr = generateSrcsetAttribute(publicId, breakpoints, transformation, options);\n      if (!isEmpty(srcsetAttr)) {\n        responsiveAttributes.srcset = srcsetAttr;\n      }\n    }\n\n    if (generateSizes) {\n      let sizesAttr = generateSizesAttribute(breakpoints);\n      if (!isEmpty(sizesAttr)) {\n        responsiveAttributes.sizes = sizesAttr;\n      }\n    }\n  }\n  return responsiveAttributes;\n}\n\n/**\n * Generate a media query\n *\n * @private\n * @param {object} options configuration options\n * @param {number|string} options.min_width\n * @param {number|string} options.max_width\n * @return {string} a media query string\n */\nexport function generateMediaAttr(options) {\n  let mediaQuery = [];\n  if (options != null) {\n    if (options.min_width != null) {\n      mediaQuery.push(`(min-width: ${options.min_width}px)`);\n    }\n    if (options.max_width != null) {\n      mediaQuery.push(`(max-width: ${options.max_width}px)`);\n    }\n  }\n  return mediaQuery.join(' and ');\n}\n\nexport const srcsetUrl = scaledUrl;\n", "/**\n * Image Tag\n * Depends on 'tags/htmltag', 'cloudinary'\n */\n\nimport HtmlTag from './htmltag';\n\nimport url from '../url';\nimport {isEmpty, isString, merge} from \"../util\";\nimport {generateImageResponsiveAttributes} from \"../util/srcsetUtils\";\n\n/**\n * Creates an HTML (DOM) Image tag using Cloudinary as the source.\n * @constructor ImageTag\n * @extends HtmlTag\n * @param {string} [publicId]\n * @param {Object} [options]\n */\nclass ImageTag extends HtmlTag {\n  constructor(publicId, options = {}) {\n    super(\"img\", publicId, options);\n  }\n\n  /** @override */\n  closeTag() {\n    return \"\";\n  }\n\n  /** @override */\n  attributes() {\n    var attr, options, srcAttribute;\n    attr = super.attributes() || {};\n    options = this.getOptions();\n    let attributes = this.getOption('attributes') || {};\n    let srcsetParam = this.getOption('srcset')|| attributes.srcset;\n\n    let responsiveAttributes = {};\n    if (isString(srcsetParam)) {\n      responsiveAttributes.srcset = srcsetParam\n    } else {\n      responsiveAttributes = generateImageResponsiveAttributes(this.publicId, attributes, srcsetParam, options);\n    }\n    if(!isEmpty(responsiveAttributes)) {\n      delete attr.width;\n      delete attr.height;\n    }\n\n    merge(attr, responsiveAttributes);\n    srcAttribute = options.responsive && !options.client_hints ? 'data-src' : 'src';\n    if (attr[srcAttribute] == null) {\n      attr[srcAttribute] = url(this.publicId, this.getOptions());\n    }\n    return attr;\n  }\n\n};\n\nexport default ImageTag;\n", "/**\n * Image Tag\n * Depends on 'tags/htmltag', 'cloudinary'\n */\nimport {generateImageResponsiveAttributes, generateMediaAttr} from \"../util/srcsetUtils\";\nimport {merge} from '../util';\nimport url from '../url';\nimport HtmlTag from './htmltag';\n\n/**\n * Creates an HTML (DOM) Image tag using Cloudinary as the source.\n * @constructor SourceTag\n * @extends HtmlTag\n * @param {string} [publicId]\n * @param {Object} [options]\n */\nclass SourceTag extends HtmlTag {\n  constructor(publicId, options = {}) {\n    super(\"source\", publicId, options);\n  }\n\n  /** @override */\n  closeTag() {\n    return \"\";\n  }\n\n  /** @override */\n  attributes() {\n    let srcsetParam = this.getOption('srcset');\n    let attr = super.attributes() || {};\n    let options = this.getOptions();\n    merge(attr, generateImageResponsiveAttributes(this.publicId, attr, srcsetParam, options));\n    if(!attr.srcset){\n      attr.srcset = url(this.publicId, options);\n    }\n    if(!attr.media && options.media){\n      attr.media = generateMediaAttr(options.media);\n    }\n\n    return attr;\n  }\n\n};\n\nexport default SourceTag;\n", "import HtmlTag from './htmltag';\nimport ImageTag from './imagetag';\nimport Transformation from '../transformation';\nimport SourceTag from './sourcetag';\nimport {extractUrlParams} from \"../util\";\n\nclass PictureTag extends HtmlTag {\n  constructor(publicId, options = {}, sources = []) {\n    super('picture', publicId, options);\n    this.widthList = sources;\n  }\n\n  /** @override */\n  content() {\n    return this.widthList.map(({min_width, max_width, transformation}) => {\n      let options = this.getOptions();\n      let sourceTransformation = new Transformation(options);\n      sourceTransformation.chain().fromOptions(typeof transformation === 'string' ? {\n        raw_transformation: transformation\n      } : transformation);\n      options = extractUrlParams(options);\n      options.media = {min_width, max_width};\n      options.transformation = sourceTransformation;\n      return new SourceTag(this.publicId, options).toHtml();\n    }).join('') +\n      new ImageTag(this.publicId, this.getOptions()).toHtml();\n  }\n\n  /** @override */\n  attributes() {\n\n    let attr = super.attributes();\n    delete attr.width;\n    delete attr.height;\n    return attr;\n  }\n\n  /** @override */\n  closeTag() {\n    return \"</\" + this.name + \">\";\n  }\n\n};\n\nexport default PictureTag;\n", "/**\n * Video Tag\n * Depends on 'tags/htmltag', 'util', 'cloudinary'\n */\n\nimport {\n  DEFAULT_VIDEO_PARAMS,\n  DEFAULT_IMAGE_PARAMS\n} from '../constants';\n\nimport url from '../url';\n\nimport {\n  defaults,\n  isPlainObject,\n  isArray,\n  isEmpty,\n  omit\n} from '../util';\n\nimport HtmlTag from './htmltag';\n\n\nconst VIDEO_TAG_PARAMS = ['source_types', 'source_transformation', 'fallback_content', 'poster', 'sources'];\n\nconst DEFAULT_VIDEO_SOURCE_TYPES = ['webm', 'mp4', 'ogv'];\n\nconst DEFAULT_POSTER_OPTIONS = {\n  format: 'jpg',\n  resource_type: 'video'\n};\n\n/**\n * Creates an HTML (DOM) Video tag using Cloudinary as the source.\n * @constructor VideoTag\n * @extends HtmlTag\n * @param {string} [publicId]\n * @param {Object} [options]\n */\nclass VideoTag extends HtmlTag {\n  constructor(publicId, options = {}) {\n    options = defaults({}, options, DEFAULT_VIDEO_PARAMS);\n    super(\"video\", publicId.replace(/\\.(mp4|ogv|webm)$/, ''), options);\n  }\n\n  /**\n   * Set the transformation to apply on each source\n   * @function VideoTag#setSourceTransformation\n   * @param {Object} an object with pairs of source type and source transformation\n   * @returns {VideoTag} Returns this instance for chaining purposes.\n   */\n  setSourceTransformation(value) {\n    this.transformation().sourceTransformation(value);\n    return this;\n  }\n\n  /**\n   * Set the source types to include in the video tag\n   * @function VideoTag#setSourceTypes\n   * @param {Array<string>} an array of source types\n   * @returns {VideoTag} Returns this instance for chaining purposes.\n   */\n  setSourceTypes(value) {\n    this.transformation().sourceTypes(value);\n    return this;\n  }\n\n  /**\n   * Set the poster to be used in the video tag\n   * @function VideoTag#setPoster\n   * @param {string|Object} value\n   * - string: a URL to use for the poster\n   * - Object: transformation parameters to apply to the poster. May optionally include a public_id to use instead of the video public_id.\n   * @returns {VideoTag} Returns this instance for chaining purposes.\n   */\n  setPoster(value) {\n    this.transformation().poster(value);\n    return this;\n  }\n\n  /**\n   * Set the content to use as fallback in the video tag\n   * @function VideoTag#setFallbackContent\n   * @param {string} value - the content to use, in HTML format\n   * @returns {VideoTag} Returns this instance for chaining purposes.\n   */\n  setFallbackContent(value) {\n    this.transformation().fallbackContent(value);\n    return this;\n  }\n\n  content() {\n    let sourceTypes = this.transformation().getValue('source_types');\n    const sourceTransformation = this.transformation().getValue('source_transformation');\n    const fallback = this.transformation().getValue('fallback_content');\n    let sources = this.getOption('sources');\n    let innerTags = [];\n    if (isArray(sources) && !isEmpty(sources)) {\n      innerTags = sources.map(source => {\n        let src = url(this.publicId, defaults(\n            {},\n            source.transformations || {},\n            {resource_type: 'video', format: source.type}\n            ), this.getOptions());\n        return  this.createSourceTag(src, source.type, source.codecs);\n      });\n    } else {\n      if (isEmpty(sourceTypes)) {\n        sourceTypes = DEFAULT_VIDEO_SOURCE_TYPES;\n      }\n      if (isArray(sourceTypes)) {\n        innerTags = sourceTypes.map(srcType => {\n          let src = url(this.publicId, defaults(\n              {},\n              sourceTransformation[srcType] || {},\n              {resource_type: 'video', format: srcType}\n          ), this.getOptions());\n          return  this.createSourceTag(src, srcType);\n        });\n      }\n    }\n    return innerTags.join('') + fallback;\n  }\n\n  attributes() {\n    let sourceTypes = this.getOption('source_types');\n    let poster = this.getOption('poster');\n    if (poster === undefined) {\n      poster = {};\n    }\n    if (isPlainObject(poster)) {\n      let defaultOptions = poster.public_id != null ? DEFAULT_IMAGE_PARAMS : DEFAULT_POSTER_OPTIONS;\n      poster = url(poster.public_id || this.publicId, defaults({}, poster, defaultOptions, this.getOptions()));\n    }\n    let attr = super.attributes() || {};\n    attr = omit(attr, VIDEO_TAG_PARAMS);\n    const sources = this.getOption('sources');\n    // In case of empty sourceTypes - fallback to default source types is used.\n    const hasSourceTags = !isEmpty(sources) || isEmpty(sourceTypes) || isArray(sourceTypes);\n    if (!hasSourceTags) {\n      attr[\"src\"] = url(this.publicId, this.getOptions(), {\n        resource_type: 'video',\n        format: sourceTypes\n      });\n    }\n    if (poster != null) {\n      attr[\"poster\"] = poster;\n    }\n    return attr;\n  }\n\n  createSourceTag(src, sourceType, codecs = null) {\n    let mimeType = null;\n    if (!isEmpty(sourceType)) {\n      let videoType = sourceType === 'ogv' ? 'ogg' : sourceType;\n      mimeType = 'video/' + videoType;\n      if (!isEmpty(codecs)) {\n        let codecsStr = isArray(codecs) ? codecs.join(', ') : codecs;\n        mimeType += '; codecs=' + codecsStr;\n      }\n    }\n    return \"<source \" + (this.htmlAttrs({\n      src: src,\n      type: mimeType\n    })) + \">\";\n  }\n\n\n}\n\nexport default VideoTag;\n", "/**\n * Image Tag\n * Depends on 'tags/htmltag', 'cloudinary'\n */\n\nimport HtmlTag from './htmltag';\n\nimport {\n  assign\n} from '../util';\n\n/**\n * Creates an HTML (DOM) Meta tag that enables Client-Hints for the HTML page. <br/>\n *  See\n *  <a href=\"https://cloudinary.com/documentation/responsive_images#automating_responsive_images_with_client_hints\"\n *  target=\"_new\">Automating responsive images with Client Hints</a> for more details.\n * @constructor ClientHintsMetaTag\n * @extends HtmlTag\n * @param {object} options\n * @example\n * tag = new ClientHintsMetaTag()\n * //returns: <meta http-equiv=\"Accept-CH\" content=\"DPR, Viewport-Width, Width\">\n */\nclass ClientHintsMetaTag extends HtmlTag {\n  constructor(options) {\n    super('meta', void 0, assign({\n      \"http-equiv\": \"Accept-CH\",\n      content: \"DPR, Viewport-Width, Width\"\n    }, options));\n  }\n\n  /** @override */\n  closeTag() {\n    return \"\";\n  }\n\n};\n\nexport default ClientHintsMetaTag;\n", "import {isArray, isString} from \"./util\";\n\n\n/**\n * @desc normalize elements, support a single element, array or nodelist, always outputs array\n * @param elements<HTMLElement[]>\n * @returns {[]}\n */\nexport function normalizeToArray(elements) {\n  if (isArray(elements)) {\n    return elements;\n  } else if (elements.constructor.name === \"NodeList\") {\n    return [...elements]; // ensure an array is always returned, even if nodelist\n  } else if (isString(elements)) {\n    return Array.prototype.slice.call(document.querySelectorAll(elements), 0);\n  } else {\n    return [elements];\n  }\n}\n\n", "/**\n * @param {HTMLElement} htmlElContainer\n * @param {object} clInstance cloudinary instance\n * @param {string} publicId\n * @param {object} options - TransformationOptions\n * @returns Promise<HTMLElement>\n */\nfunction mountCloudinaryVideoTag(htmlElContainer, clInstance, publicId, options) {\n  return new Promise((resolve, reject) => {\n    htmlElContainer.innerHTML = clInstance.videoTag(publicId, options).toHtml();\n\n    // All videos under the html container must have a width of 100%, or they might overflow from the container\n    let cloudinaryVideoElement = htmlElContainer.querySelector('.cld-transparent-video');\n    cloudinaryVideoElement.style.width = '100%';\n    resolve(htmlElContainer);\n  });\n}\n\nexport default mountCloudinaryVideoTag;\n", "/**\n * @description - Function will push a flag to incoming options\n * @param {{transformation} | {...transformation}} options - These options are the same options provided to all our SDK methods\n *                           We expect options to either be the transformation itself, or an object containing\n *                           an array of transformations\n *\n * @param {string} flag\n * @returns the mutated options object\n */\n\nfunction addFlagToOptions(options, flag) {\n  // Do we have transformation\n  if (options.transformation) {\n    options.transformation.push({\n      flags: [flag]\n    });\n  } else {\n    // no transformation\n    // ensure the flags are extended\n    if (!options.flags) {\n      options.flags = [];\n    }\n\n    if (typeof options.flags === 'string') {\n      options.flags = [options.flags];\n    }\n\n    options.flags.push(flag);\n  }\n}\n\nexport default addFlagToOptions;\n", "import addFlagToOptions from \"../../transformations/addFlag\";\nimport {DEFAULT_EXTERNAL_LIBRARIES, DEFAULT_TIMEOUT_MS} from \"../../../constants\";\n\n/**\n * @description - Enforce option structure, sets defaults and ensures alpha flag exists\n * @param options {TransformationOptions}\n */\nfunction enforceOptionsForTransparentVideo(options) {\n  options.autoplay = true;\n  options.muted = true;\n  options.controls = false;\n  options.max_timeout_ms = options.max_timeout_ms || DEFAULT_TIMEOUT_MS;\n  options.class = options.class || '';\n  options.class += ' cld-transparent-video';\n  options.externalLibraries = options.externalLibraries || {};\n\n  if (!options.externalLibraries.seeThru) {\n    options.externalLibraries.seeThru = DEFAULT_EXTERNAL_LIBRARIES.seeThru;\n  }\n\n  // ensure there's an alpha transformation present\n  // this is a non documented internal flag\n  addFlagToOptions(options, 'alpha');\n}\n\nexport default enforceOptionsForTransparentVideo;\n", "/**\n * @description - Given a string URL, this function will load the script and resolve the promise.\n *                The function doesn't resolve any value,\n *                this is not a UMD loader where you can get your library name back.\n * @param scriptURL {string}\n * @param {number} max_timeout_ms - Time to elapse before promise is rejected\n * @param isAlreadyLoaded {boolean} if true, the loadScript resolves immediately\n *                                  this is used for multiple invocations - prevents the script from being loaded multiple times\n * @return {Promise<any | {status:string, message:string}>}\n */\nfunction loadScript(scriptURL, max_timeout_ms, isAlreadyLoaded) {\n  return new Promise((resolve, reject) => {\n    if (isAlreadyLoaded) {\n      resolve();\n    } else {\n      let scriptTag = document.createElement('script');\n      scriptTag.src = scriptURL;\n\n      let timerID = setTimeout(() => {\n        reject({\n          status: 'error',\n          message: `Timeout loading script ${scriptURL}`\n        });\n      }, max_timeout_ms); // 10 seconds for timeout\n\n      scriptTag.onerror = () => {\n        clearTimeout(timerID); // clear timeout reject error\n        reject({\n          status: 'error',\n          message: `Error loading ${scriptURL}`\n        });\n      };\n\n      scriptTag.onload = () => {\n        clearTimeout(timerID); // clear timeout reject error\n        resolve();\n      };\n      document.head.appendChild(scriptTag);\n    }\n  });\n}\n\nexport default loadScript;\n", "/**\n * Reject on timeout\n * @param maxTimeoutMS\n * @param reject\n * @returns {number} timerID\n */\nfunction rejectOnTimeout(maxTimeoutMS, reject) {\n  return setTimeout(() => {\n    reject({\n      status: 'error',\n      message: 'Timeout loading Blob URL'\n    });\n  }, maxTimeoutMS);\n}\n\n/**\n * @description Converts a URL to a BLOB URL\n * @param {string} urlToLoad\n * @param {number} max_timeout_ms - Time to elapse before promise is rejected\n * @return {Promise<{\n *   status: 'success' | 'error'\n *   message?: string,\n *    payload: {\n *      url: string\n *    }\n * }>}\n */\nfunction getBlobFromURL(urlToLoad, maxTimeoutMS) {\n  return new Promise((resolve, reject) => {\n    const timerID = rejectOnTimeout(maxTimeoutMS, reject);\n\n    // If fetch exists, use it to fetch blob, otherwise use XHR.\n    // XHR causes issues on safari 14.1 so we prefer fetch\n    const fetchBlob = (typeof fetch !== 'undefined' && fetch) ? loadUrlUsingFetch : loadUrlUsingXhr;\n\n    fetchBlob(urlToLoad).then((blob) => {\n      resolve({\n        status: 'success',\n        payload: {\n          blobURL: URL.createObjectURL(blob)\n        }\n      });\n    }).catch(() => {\n      reject({\n        status: 'error',\n        message: 'Error loading Blob URL'\n      });\n    }).finally(() => {\n      // Clear the timeout timer on fail or success.\n      clearTimeout(timerID);\n    });\n  });\n}\n\n/**\n * Use fetch function to fetch file\n * @param urlToLoad\n * @returns {Promise<unknown>}\n */\nfunction loadUrlUsingFetch(urlToLoad) {\n  return new Promise((resolve, reject) => {\n    fetch(urlToLoad).then((response) => {\n      response.blob().then((blob) => {\n        resolve(blob);\n      });\n    }).catch(() => {\n      reject('error');\n    });\n  });\n}\n\n/**\n * Use XHR to fetch file\n * @param urlToLoad\n * @returns {Promise<unknown>}\n */\nfunction loadUrlUsingXhr(urlToLoad) {\n  return new Promise((resolve, reject) => {\n    const xhr = new XMLHttpRequest();\n    xhr.responseType = 'blob';\n    xhr.onload = function (response) {\n      resolve(xhr.response);\n    };\n\n    xhr.onerror = function () {\n      reject('error');\n    };\n\n    xhr.open('GET', urlToLoad, true);\n    xhr.send();\n  });\n}\n\nexport default getBlobFromURL;\n", "/**\n * @description Creates a hidden HTMLVideoElement with the specified videoOptions\n * @param {{autoplay, playsinline, loop, muted, poster, blobURL, videoURL }} videoOptions\n * @param {boolean} videoOptions.autoplay - autoplays the video if true\n * @param {string} videoOptions.blobURL - the blobURL to set as video.src\n * @param {string} videoOptions.videoURL - the original videoURL the user created (with transformations)\n * @return {HTMLVideoElement}\n */\nfunction createHiddenVideoTag(videoOptions) {\n  let { autoplay, playsinline, loop, muted, poster, blobURL, videoURL} = videoOptions;\n\n  let el = document.createElement('video');\n  el.style.visibility = 'hidden';\n  el.position = 'absolute';\n  el.x = 0;\n  el.y = 0;\n  el.src = blobURL;\n  el.setAttribute('data-video-url', videoURL); // for debugging/testing\n\n  autoplay && el.setAttribute('autoplay', autoplay);\n  playsinline && el.setAttribute('playsinline', playsinline);\n  loop && el.setAttribute('loop', loop);\n  muted && el.setAttribute('muted', muted);\n  muted && (el.muted = muted); // this is also needed for autoplay, on top of setAttribute\n  poster && el.setAttribute('poster', poster);\n\n  // Free memory at the end of the file loading.\n  el.onload = () => {\n    URL.revokeObjectURL(blobURL);\n  };\n\n  return el;\n}\n\nexport default createHiddenVideoTag;\n", "/**\n * @description This function creates a new instanc eof seeThru (seeThru.create()) and returns a promise of the seeThru instance\n * @param {HTMLVideoElement} videoElement\n * @param {number} max_timeout_ms - Time to elapse before promise is rejected\n * @param {string} customClass - A classname to be added to the canvas element created by seeThru\n * @param {boolean} autoPlay\n * @return {Promise<any>} SeeThru instance or rejection error\n */\nfunction instantiateSeeThru(videoElement, max_timeout_ms, customClass, autoPlay) {\n  let {seeThru, setTimeout, clearTimeout} = window;\n\n  return new Promise((resolve, reject) => {\n    let timerID = setTimeout(() => {\n      reject({status: 'error', message: 'Timeout instantiating seeThru instance'});\n    }, max_timeout_ms);\n\n    if (seeThru) {\n      let seeThruInstance = seeThru.create(videoElement).ready(() => {\n        // clear timeout reject error\n        clearTimeout(timerID);\n\n        // force container width, else the canvas can overflow out\n        let canvasElement = seeThruInstance.getCanvas();\n        canvasElement.style.width = '100%';\n        canvasElement.className += ' ' + customClass;\n\n        // start the video if autoplay is set\n        if (autoPlay) {\n          seeThruInstance.play();\n        }\n\n        resolve(seeThruInstance);\n      });\n    } else {\n      reject({status: 'error', message: 'Error instantiating seeThru instance'});\n    }\n  });\n}\n\nexport default instantiateSeeThru;\n", "import loadScript from \"../../xhr/loadScript\";\nimport getBlobFromURL from \"../../xhr/getBlobFromURL\";\nimport createHiddenVideoTag from \"./createHiddenVideoTag\";\nimport instantiateSeeThru from \"./instantiateSeeThru\";\n\n/**\n *\n * @param {HTMLElement} htmlElContainer\n * @param {string} videoURL\n * @param {TransformationOptions} options\n * @return {Promise<any>}\n */\nfunction mountSeeThruCanvasTag(htmlElContainer, videoURL, options) {\n  let {poster, autoplay, playsinline, loop, muted} = options;\n  videoURL = videoURL + '.mp4'; // seeThru always uses mp4\n  return new Promise((resolve, reject) => {\n    loadScript(options.externalLibraries.seeThru, options.max_timeout_ms, window.seeThru).then(() => {\n      getBlobFromURL(videoURL, options.max_timeout_ms).then(({payload}) => {\n        let videoElement = createHiddenVideoTag({\n          blobURL: payload.blobURL,\n          videoURL: videoURL, // for debugging/testing\n          poster,\n          autoplay,\n          playsinline,\n          loop,\n          muted\n        });\n\n        htmlElContainer.appendChild(videoElement);\n\n        instantiateSeeThru(videoElement, options.max_timeout_ms, options.class, options.autoplay)\n          .then(() => {\n            resolve(htmlElContainer);\n          })\n          .catch((err) => {\n            reject(err);\n          });\n\n        // catch for getBlobFromURL()\n      }).catch(({status, message}) => {\n        reject({status, message});\n      });\n      // catch for loadScript()\n    }).catch(({status, message}) => {\n      reject({status, message});\n    });\n  });\n}\n\n\nexport default mountSeeThruCanvasTag;\n", "/**\n * @return {Promise<boolean>} - Whether the browser supports transparent videos or not\n */\nimport {isSafari} from \"./util\";\n\nfunction checkSupportForTransparency() {\n  return new Promise((resolve, reject) => {\n    // Resolve early for safari.\n    // Currently (29 December 2021) Safari can play webm/vp9,\n    // but it does not support transparent video in the format we're outputting\n    if (isSafari()){\n      resolve(false);\n    }\n\n    const video = document.createElement('video');\n    const canPlay = video.canPlayType && video.canPlayType('video/webm; codecs=\"vp9\"');\n    resolve(canPlay === 'maybe' || canPlay === 'probably');\n  });\n}\n\nexport default checkSupportForTransparency;\n", "import {normalizeToArray} from \"./util/parse/normalizeToArray\";\n\nvar applyBreakpoints, closestAbove, defaultBreakpoints, findContainerWidth, maxWidth, updateDpr;\n\nimport Configuration from './configuration';\nimport HtmlTag from './tags/htmltag';\nimport ImageTag from './tags/imagetag';\nimport PictureTag from './tags/picturetag';\nimport SourceTag from './tags/sourcetag';\nimport Transformation from './transformation';\nimport url from './url';\nimport VideoTag from './tags/videotag';\nimport * as constants from './constants';\n\nimport {\n  addClass,\n  assign,\n  defaults,\n  getData,\n  isEmpty,\n  isFunction,\n  isString,\n  merge,\n  removeAttribute,\n  setAttribute,\n  setData,\n  width\n} from './util';\n//\n\nimport mountCloudinaryVideoTag from \"./util/features/transparentVideo/mountCloudinaryVideoTag\";\nimport enforceOptionsForTransparentVideo from \"./util/features/transparentVideo/enforceOptionsForTransparentVideo\";\nimport mountSeeThruCanvasTag from \"./util/features/transparentVideo/mountSeeThruCanvasTag\";\nimport checkSupportForTransparency from \"./util/features/transparentVideo/checkSupportForTransparency\";\n\ndefaultBreakpoints = function(width, steps = 100) {\n  return steps * Math.ceil(width / steps);\n};\n\nclosestAbove = function(list, value) {\n  var i;\n  i = list.length - 2;\n  while (i >= 0 && list[i] >= value) {\n    i--;\n  }\n  return list[i + 1];\n};\n\napplyBreakpoints = function(tag, width, steps, options) {\n  var ref, ref1, ref2, responsive_use_breakpoints;\n  responsive_use_breakpoints = (ref = (ref1 = (ref2 = options['responsive_use_breakpoints']) != null ? ref2 : options['responsive_use_stoppoints']) != null ? ref1 : this.config('responsive_use_breakpoints')) != null ? ref : this.config('responsive_use_stoppoints');\n  if ((!responsive_use_breakpoints) || (responsive_use_breakpoints === 'resize' && !options.resizing)) {\n    return width;\n  } else {\n    return this.calc_breakpoint(tag, width, steps);\n  }\n};\n\nfindContainerWidth = function(element) {\n  var containerWidth, style;\n  containerWidth = 0;\n  while (((element = element != null ? element.parentNode : void 0) instanceof Element) && !containerWidth) {\n    style = window.getComputedStyle(element);\n    if (!/^inline/.test(style.display)) {\n      containerWidth = width(element);\n    }\n  }\n  return containerWidth;\n};\n\nupdateDpr = function(dataSrc, roundDpr) {\n  return dataSrc.replace(/\\bdpr_(1\\.0|auto)\\b/g, 'dpr_' + this.device_pixel_ratio(roundDpr));\n};\n\nmaxWidth = function(requiredWidth, tag) {\n  var imageWidth;\n  imageWidth = getData(tag, 'width') || 0;\n  if (requiredWidth > imageWidth) {\n    imageWidth = requiredWidth;\n    setData(tag, 'width', requiredWidth);\n  }\n  return imageWidth;\n};\n\nclass Cloudinary {\n  /**\n   * Creates a new Cloudinary instance.\n   * @class Cloudinary\n   * @classdesc Main class for accessing Cloudinary functionality.\n   * @param {Object} options - A {@link Configuration} object for globally configuring Cloudinary account settings.\n   * @example<br/>\n   *  var cl = new cloudinary.Cloudinary( { cloud_name: \"mycloud\"});<br/>\n   *  var imgTag = cl.image(\"myPicID\");\n   * @see <a href=\"https://cloudinary.com/documentation/solution_overview#configuration_parameters\" target=\"_blank\">\n   *  Available configuration options</a>\n   */\n  constructor(options) {\n    var configuration;\n    this.devicePixelRatioCache = {};\n    this.responsiveConfig = {};\n    this.responsiveResizeInitialized = false;\n    configuration = new Configuration(options);\n    // Provided for backward compatibility\n    this.config = function(newConfig, newValue) {\n      return configuration.config(newConfig, newValue);\n    };\n    /**\n     * Use \\<meta\\> tags in the document to configure this `cloudinary` instance.\n     * @return This {Cloudinary} instance for chaining.\n     */\n    this.fromDocument = function() {\n      configuration.fromDocument();\n      return this;\n    };\n    /**\n     * Use environment variables to configure this `cloudinary` instance.\n     * @return This {Cloudinary} instance for chaining.\n     */\n    this.fromEnvironment = function() {\n      configuration.fromEnvironment();\n      return this;\n    };\n    /**\n     * Initializes the configuration of this `cloudinary` instance.\n     *  This is a convenience method that invokes both {@link Configuration#fromEnvironment|fromEnvironment()}\n     *  (Node.js environment only) and {@link Configuration#fromDocument|fromDocument()}.\n     *  It first tries to retrieve the configuration from the environment variable.\n     *  If not available, it tries from the document meta tags.\n     * @function Cloudinary#init\n     * @see Configuration#init\n     * @return This {Cloudinary} instance for chaining.\n     */\n    this.init = function() {\n      configuration.init();\n      return this;\n    };\n  }\n\n  /**\n   * Convenience constructor\n   * @param {Object} options\n   * @return {Cloudinary}\n   * @example cl = cloudinary.Cloudinary.new( { cloud_name: \"mycloud\"})\n   */\n  static new(options) {\n    return new this(options);\n  }\n\n  /**\n   * Generates a URL for any asset in your Media library.\n   * @function Cloudinary#url\n   * @param {string} publicId - The public ID of the media asset.\n   * @param {Object} [options] - The {@link Transformation} parameters to include in the URL.\n   * @param {type} [options.type='upload'] - The asset's storage type.\n   *  For details on all fetch types, see\n   * <a href=\"https://cloudinary.com/documentation/image_transformations#fetching_images_from_remote_locations\"\n   *  target=\"_blank\">Fetch types</a>.\n   * @param {resourceType} [options.resource_type='image'] - The type of asset. Possible values:<br/>\n   *  - `image`<br/>\n   *  - `video`<br/>\n   *  - `raw`\n   * @return {string} The media asset URL.\n   * @see <a href=\"https://cloudinary.com/documentation/image_transformation_reference\" target=\"_blank\">\n   *  Available image transformations</a>\n   * @see <a href=\"https://cloudinary.com/documentation/video_transformation_reference\" target=\"_blank\">\n   *  Available video transformations</a>\n   */\n  url(publicId, options = {}) {\n    return url(publicId, options, this.config());\n  }\n\n  /**\n   * Generates a video asset URL.\n   * @function Cloudinary#video_url\n   * @param {string} publicId - The public ID of the video.\n   * @param {Object} [options] - The {@link Transformation} parameters to include in the URL.\n   * @param {type} [options.type='upload'] - The asset's storage type.\n   *  For details on all fetch types, see\n   *  <a href=\"https://cloudinary.com/documentation/image_transformations#fetching_images_from_remote_locations\"\n   *  target=\"_blank\">Fetch types</a>.\n   * @return {string} The video URL.\n   * @see <a href=\"https://cloudinary.com/documentation/video_transformation_reference\"\n   *  target=\"_blank\">Available video transformations</a>\n   */\n  video_url(publicId, options) {\n    options = assign({\n      resource_type: 'video'\n    }, options);\n    return this.url(publicId, options);\n  }\n\n  /**\n   * Generates a URL for an image intended to be used as a thumbnail for the specified video.\n   *  Identical to {@link Cloudinary#url|url}, except that the `resource_type` is `video`\n   *  and the default `format` is `jpg`.\n   * @function Cloudinary#video_thumbnail_url\n   * @param {string} publicId -  The unique identifier of the video from which you want to generate a thumbnail image.\n   * @param {Object} [options] - The image {@link Transformation} parameters to apply to the thumbnail.\n   * In addition to standard image transformations, you can also use the `start_offset` transformation parameter\n   * to instruct Cloudinary to generate the thumbnail from a frame other than the middle frame of the video.\n   * For details, see\n   * <a href=\"https://cloudinary.com/documentation/video_manipulation_and_delivery#generating_video_thumbnails\"\n   * target=\"_blank\">Generating video thumbnails</a> in the Cloudinary documentation.\n   * @param {type} [options.type='upload'] - The asset's storage type.\n   * @return {string} The URL of the video thumbnail image.\n   * @see <a href=\"https://cloudinary.com/documentation/image_transformation_reference\" target=\"_blank\">\n   *  Available image transformations</a>\n   */\n  video_thumbnail_url(publicId, options) {\n    options = assign({}, constants.DEFAULT_POSTER_OPTIONS, options);\n    return this.url(publicId, options);\n  }\n\n  /**\n   * Generates a string representation of the specified transformation options.\n   * @function Cloudinary#transformation_string\n   * @param {Object} options - The {@link Transformation} options.\n   * @returns {string} The transformation string.\n   * @see <a href=\"https://cloudinary.com/documentation/image_transformation_reference\" target=\"_blank\">\n   *  Available image transformations</a>\n   * @see <a href=\"https://cloudinary.com/documentation/video_transformation_reference\" target=\"_blank\">\n   *  Available video transformations</a>\n   */\n  transformation_string(options) {\n    return new Transformation(options).serialize();\n  }\n\n  /**\n   * Generates an image tag.\n   * @function Cloudinary#image\n   * @param {string} publicId - The public ID of the image.\n   * @param {Object} options - The {@link Transformation} parameters, {@link Configuration} parameters,\n   *  and standard HTML &lt;img&gt; tag attributes to apply to the image tag.\n   * @return {HTMLImageElement} An image tag DOM element.\n   * @see <a href=\"https://cloudinary.com/documentation/image_transformation_reference\" target=\"_blank\">\n   *  Available image transformations</a>\n   * @see <a href=\"https://cloudinary.com/documentation/solution_overview#configuration_parameters\"\n   *  target=\"_blank\">Available configuration options</a>\n   */\n  image(publicId, options = {}) {\n    var client_hints, img, ref;\n    img = this.imageTag(publicId, options);\n    client_hints = (ref = options.client_hints != null ? options.client_hints : this.config('client_hints')) != null ? ref : false;\n    if (options.src == null && !client_hints) {\n      // src must be removed before creating the DOM element to avoid loading the image\n      img.setAttr(\"src\", '');\n    }\n    img = img.toDOM();\n    if (!client_hints) {\n      // cache the image src\n      setData(img, 'src-cache', this.url(publicId, options));\n      // set image src taking responsiveness in account\n      this.cloudinary_update(img, options);\n    }\n    return img;\n  }\n\n  /**\n   * Creates a new ImageTag instance using the configuration defined for this `cloudinary` instance.\n   * @function Cloudinary#imageTag\n   * @param {string} publicId - The public ID of the image.\n   * @param {Object} [options] - The {@link Transformation} parameters, {@link Configuration} parameters,\n   *  and standard HTML &lt;img&gt; tag attributes to apply to the image tag.\n   * @return {ImageTag} An ImageTag instance that is attached (chained) to this Cloudinary instance.\n   * @see <a href=\"https://cloudinary.com/documentation/image_transformation_reference\" target=\"_blank\">\n   *  Available image transformations</a>\n   * @see <a href=\"https://cloudinary.com/documentation/solution_overview#configuration_parameters\"\n   *  target=\"_blank\">Available configuration options</a>\n   */\n  imageTag(publicId, options) {\n    var tag;\n    tag = new ImageTag(publicId, this.config());\n    tag.transformation().fromOptions(options);\n    return tag;\n  }\n\n  /**\n   * Creates a new PictureTag instance, configured using this `cloudinary` instance.\n   * @function Cloudinary#PictureTag\n   * @param {string} publicId - the public ID of the resource\n   * @param {Object} options - additional options to pass to the new ImageTag instance\n   * @param {Array<Object>} sources - the sources definitions\n   * @return {PictureTag} A PictureTag that is attached (chained) to this Cloudinary instance\n   */\n  pictureTag(publicId, options, sources) {\n    var tag;\n    tag = new PictureTag(publicId, this.config(), sources);\n    tag.transformation().fromOptions(options);\n    return tag;\n  }\n\n  /**\n   * Creates a new SourceTag instance, configured using this `cloudinary` instance.\n   * @function Cloudinary#SourceTag\n   * @param {string} publicId - the public ID of the resource.\n   * @param {Object} options - additional options to pass to the new instance.\n   * @return {SourceTag} A SourceTag that is attached (chained) to this Cloudinary instance\n   */\n  sourceTag(publicId, options) {\n    var tag;\n    tag = new SourceTag(publicId, this.config());\n    tag.transformation().fromOptions(options);\n    return tag;\n  }\n\n  /**\n   * Generates a video thumbnail URL from the specified remote video and includes it in an image tag.\n   * @function Cloudinary#video_thumbnail\n   * @param {string} publicId - The unique identifier of the video from the relevant video site.\n   *  Additionally, either append the image extension type to the identifier value or set\n   *  the image delivery format in the 'options' parameter using the 'format' transformation option.\n   *  For example, a YouTube video might have the identifier: 'o-urnlaJpOA.jpg'.\n   * @param {Object} [options] - The {@link Transformation} parameters to apply.\n   * @return {HTMLImageElement} An HTML image tag element\n   * @see <a href=\"https://cloudinary.com/documentation/video_transformation_reference\" target=\"_blank\">\n   *  Available video transformations</a>\n   * @see <a href=\"https://cloudinary.com/documentation/solution_overview#configuration_parameters\"\n   *  target=\"_blank\">Available configuration options</a>\n   */\n  video_thumbnail(publicId, options) {\n    return this.image(publicId, merge({}, constants.DEFAULT_POSTER_OPTIONS, options));\n  }\n\n  /**\n   * Fetches a facebook profile image and delivers it in an image tag element.\n   * @function Cloudinary#facebook_profile_image\n   * @param {string} publicId - The Facebook numeric ID. Additionally, either append the image extension type\n   *  to the ID or set the image delivery format in the 'options' parameter using the 'format' transformation option.\n   * @param {Object} [options] - The {@link Transformation} parameters, {@link Configuration} parameters,\n   *  and standard HTML &lt;img&gt; tag attributes to apply to the image tag.\n   * @return {HTMLImageElement} An image tag element.\n   * @see <a href=\"https://cloudinary.com/documentation/image_transformation_reference\" target=\"_blank\">\n   *  Available image transformations</a>\n   * @see <a href=\"https://cloudinary.com/documentation/solution_overview#configuration_parameters\"\n   *  target=\"_blank\">Available configuration options</a>\n   */\n  facebook_profile_image(publicId, options) {\n    return this.image(publicId, assign({\n      type: 'facebook'\n    }, options));\n  }\n\n  /**\n   * Fetches a Twitter profile image by ID and delivers it in an image tag element.\n   * @function Cloudinary#twitter_profile_image\n   * @param {string} publicId - The Twitter numeric ID. Additionally, either append the image extension type\n   *  to the ID or set the image delivery format in the 'options' parameter using the 'format' transformation option.\n   * @param {Object} [options] - The {@link Transformation} parameters, {@link Configuration} parameters,\n   *  and standard HTML &lt;img&gt; tag attributes to apply to the image tag.\n   * @return {HTMLImageElement} An image tag element.\n   * @see <a href=\"https://cloudinary.com/documentation/image_transformation_reference\" target=\"_blank\">\n   *  Available image transformations</a>\n   * @see <a href=\"https://cloudinary.com/documentation/solution_overview#configuration_parameters\"\n   *  target=\"_blank\">Available configuration options</a>\n   */\n  twitter_profile_image(publicId, options) {\n    return this.image(publicId, assign({\n      type: 'twitter'\n    }, options));\n  }\n\n  /**\n   * Fetches a Twitter profile image by name and delivers it in an image tag element.\n   * @function Cloudinary#twitter_name_profile_image\n   * @param {string} publicId - The Twitter screen name. Additionally, either append the image extension type\n   *  to the screen name or set the image delivery format in the 'options' parameter using the 'format' transformation option.\n   * @param {Object} [options] - The {@link Transformation} parameters, {@link Configuration} parameters,\n   *  and standard HTML &lt;img&gt; tag attributes to apply to the image tag.\n   * @return {HTMLImageElement} An image tag element.\n   * @see <a href=\"https://cloudinary.com/documentation/image_transformation_reference\" target=\"_blank\">\n   *  Available image transformations</a>\n   * @see <a href=\"https://cloudinary.com/documentation/solution_overview#configuration_parameters\"\n   *  target=\"_blank\">Available configuration options</a>\n   */\n  twitter_name_profile_image(publicId, options) {\n    return this.image(publicId, assign({\n      type: 'twitter_name'\n    }, options));\n  }\n\n  /**\n   * Fetches a Gravatar profile image and delivers it in an image tag element.\n   * @function Cloudinary#gravatar_image\n   * @param {string} publicId - The calculated hash for the Gravatar email address.\n   *  Additionally, either append the image extension type to the screen name or set the image delivery format\n   *  in the 'options' parameter using the 'format' transformation option.\n   * @param {Object} [options] - The {@link Transformation} parameters, {@link Configuration} parameters,\n   *  and standard HTML &lt;img&gt; tag attributes to apply to the image tag.\n   * @return {HTMLImageElement} An image tag element.\n   * @see <a href=\"https://cloudinary.com/documentation/image_transformation_reference\" target=\"_blank\">\n   *  Available image transformations</a>\n   * @see <a href=\"https://cloudinary.com/documentation/solution_overview#configuration_parameters\"\n   *  target=\"_blank\">Available configuration options</a>\n   */\n  gravatar_image(publicId, options) {\n    return this.image(publicId, assign({\n      type: 'gravatar'\n    }, options));\n  }\n\n  /**\n   * Fetches an image from a remote URL and delivers it in an image tag element.\n   * @function Cloudinary#fetch_image\n   * @param {string} publicId - The full URL of the image to fetch, including the extension.\n   * @param {Object} [options] - The {@link Transformation} parameters, {@link Configuration} parameters,\n   *  and standard HTML &lt;img&gt; tag attributes to apply to the image tag.\n   * @return {HTMLImageElement} An image tag element.\n   * @see <a href=\"https://cloudinary.com/documentation/image_transformation_reference\" target=\"_blank\">\n   *  Available image transformations</a>\n   * @see <a href=\"https://cloudinary.com/documentation/solution_overview#configuration_parameters\"\n   *  target=\"_blank\">Available configuration options</a>\n   */\n  fetch_image(publicId, options) {\n    return this.image(publicId, assign({\n      type: 'fetch'\n    }, options));\n  }\n\n  /**\n   * Generates a video tag.\n   * @function Cloudinary#video\n   * @param {string} publicId - The public ID of the video.\n   * @param {Object} [options] - The {@link Transformation} parameters, {@link Configuration} parameters,\n   *  and standard HTML &lt;img&gt; tag attributes to apply to the image tag.\n   * @return {HTMLVideoElement} A video tag DOM element.\n   * @see <a href=\"https://cloudinary.com/documentation/video_transformation_reference\" target=\"_blank\">\n   *  Available video transformations</a>\n   * @see <a href=\"https://cloudinary.com/documentation/solution_overview#configuration_parameters\"\n   *  target=\"_blank\">Available configuration options</a>\n   */\n  video(publicId, options = {}) {\n    return this.videoTag(publicId, options).toHtml();\n  }\n\n  /**\n   * Creates a new VideoTag instance using the configuration defined for this `cloudinary` instance.\n   * @function Cloudinary#videoTag\n   * @param {string} publicId - The public ID of the video.\n   * @param {Object} options - The {@link Transformation} parameters, {@link Configuration} parameters,\n   *  and standard HTML &lt;img&gt; tag attributes to apply to the image tag.\n   * @return {VideoTag} A VideoTag that is attached (chained) to this `cloudinary` instance.\n   * @see <a href=\"https://cloudinary.com/documentation/video_transformation_reference\" target=\"_blank\">\n   *  Available video transformations</a>\n   * @see <a href=\"https://cloudinary.com/documentation/solution_overview#configuration_parameters\"\n   *  target=\"_blank\">Available configuration options</a>\n   */\n  videoTag(publicId, options) {\n    options = defaults({}, options, this.config());\n    return new VideoTag(publicId, options);\n  }\n\n  /**\n   * Generates a sprite PNG image that contains all images with the specified tag and the corresponding css file.\n   * @function Cloudinary#sprite_css\n   * @param {string} publicId - The tag on which to base the sprite image.\n   * @param {Object} [options] - The {@link Transformation} parameters to include in the URL.\n   * @return {string} The URL of the generated CSS file. The sprite image has the same URL, but with a PNG extension.\n   * @see <a href=\"https://cloudinary.com/documentation/sprite_generation\" target=\"_blank\">\n   *  Sprite generation</a>\n   * @see <a href=\"https://cloudinary.com/documentation/image_transformation_reference\" target=\"_blank\">\n   *  Available image transformations</a>\n   */\n  sprite_css(publicId, options) {\n    options = assign({\n      type: 'sprite'\n    }, options);\n    if (!publicId.match(/.css$/)) {\n      options.format = 'css';\n    }\n    return this.url(publicId, options);\n  }\n\n  /**\n   * Initializes responsive image behavior for all image tags with the 'cld-responsive'\n   *  (or other defined {@link Cloudinary#responsive|responsive} class).<br/>\n   *  This method should be invoked after the page has loaded.<br/>\n   *  <b>Note</b>: Calls {@link Cloudinary#cloudinary_update|cloudinary_update} to modify image tags.\n   * @function Cloudinary#responsive\n   * @param {Object} options\n   * @param {String} [options.responsive_class='cld-responsive'] - An alternative class\n   *  to locate the relevant &lt;img&gt; tags.\n   * @param {number} [options.responsive_debounce=100] - The debounce interval in milliseconds.\n   * @param {boolean} [bootstrap=true] If true, processes the &lt;img&gt; tags by calling\n   *  {@link Cloudinary#cloudinary_update|cloudinary_update}. When false, the tags are processed\n   *  only after a resize event.\n   * @see {@link Cloudinary#cloudinary_update|cloudinary_update} for additional configuration parameters\n   * @see <a href=\"https://cloudinary.com/documentation/responsive_images#automating_responsive_images_with_javascript\"\n   *  target=\"_blank\">Automating responsive images with JavaScript</a>\n   * @return {function} that when called, removes the resize EventListener added by this function\n   */\n  responsive(options, bootstrap = true) {\n    var ref, ref1, ref2, responsiveClass, responsiveResize, timeout;\n    this.responsiveConfig = merge(this.responsiveConfig || {}, options);\n    responsiveClass = (ref = this.responsiveConfig.responsive_class) != null ? ref : this.config('responsive_class');\n    if (bootstrap) {\n      this.cloudinary_update(`img.${responsiveClass}, img.cld-hidpi`, this.responsiveConfig);\n    }\n    responsiveResize = (ref1 = (ref2 = this.responsiveConfig.responsive_resize) != null ? ref2 : this.config('responsive_resize')) != null ? ref1 : true;\n    if (responsiveResize && !this.responsiveResizeInitialized) {\n      this.responsiveConfig.resizing = this.responsiveResizeInitialized = true;\n      timeout = null;\n      const makeResponsive = () => {\n        var debounce, ref3, ref4, reset, run, wait, waitFunc;\n        debounce = (ref3 = (ref4 = this.responsiveConfig.responsive_debounce) != null ? ref4 : this.config('responsive_debounce')) != null ? ref3 : 100;\n        reset = function() {\n          if (timeout) {\n            clearTimeout(timeout);\n            timeout = null;\n          }\n        };\n        run = () => {\n          return this.cloudinary_update(`img.${responsiveClass}`, this.responsiveConfig);\n        };\n        waitFunc = function() {\n          reset();\n          return run();\n        };\n        wait = function() {\n          reset();\n          timeout = setTimeout(waitFunc, debounce);\n        };\n        if (debounce) {\n          return wait();\n        } else {\n          return run();\n        }\n      };\n      window.addEventListener('resize', makeResponsive);\n      return ()=>window.removeEventListener('resize', makeResponsive);\n    }\n  }\n\n  /**\n   * @function Cloudinary#calc_breakpoint\n   * @private\n   * @ignore\n   */\n  calc_breakpoint(element, width, steps) {\n    let breakpoints = getData(element, 'breakpoints') || getData(element, 'stoppoints') || this.config('breakpoints') || this.config('stoppoints') || defaultBreakpoints;\n    if (isFunction(breakpoints)) {\n      return breakpoints(width, steps);\n    } else {\n      if (isString(breakpoints)) {\n        breakpoints = breakpoints.split(',').map(point=>parseInt(point)).sort((a, b) => a - b);\n      }\n      return closestAbove(breakpoints, width);\n    }\n  }\n\n  /**\n   * @function Cloudinary#calc_stoppoint\n   * @deprecated Use {@link calc_breakpoint} instead.\n   * @private\n   * @ignore\n   */\n  calc_stoppoint(element, width, steps) {\n    return this.calc_breakpoint(element, width, steps);\n  }\n\n  /**\n   * @function Cloudinary#device_pixel_ratio\n   * @private\n   */\n  device_pixel_ratio(roundDpr) {\n    roundDpr = roundDpr == null ? true : roundDpr;\n    let dpr = (typeof window !== \"undefined\" && window !== null ? window.devicePixelRatio : void 0) || 1;\n    if (roundDpr) {\n      dpr = Math.ceil(dpr);\n    }\n    if (dpr <= 0 || dpr === (0/0)) {\n      dpr = 1;\n    }\n    let dprString = dpr.toString();\n    if (dprString.match(/^\\d+$/)) {\n      dprString += '.0';\n    }\n    return dprString;\n  }\n\n  /**\n  * Applies responsiveness to all <code>&lt;img&gt;</code> tags under each relevant node\n  *  (regardless of whether the tag contains the {@link Cloudinary#responsive|responsive} class).\n  * @param {Element[]} nodes The parent nodes where you want to search for &lt;img&gt; tags.\n  * @param {Object} [options] The {@link Cloudinary#cloudinary_update|cloudinary_update} options to apply.\n  * @see <a href=\"https://cloudinary.com/documentation/image_transformation_reference\"\n  *  target=\"_blank\">Available image transformations</a>\n  * @function Cloudinary#processImageTags\n  */\n  processImageTags(nodes, options) {\n    if (isEmpty(nodes)) {\n      // similar to `$.fn.cloudinary`\n      return this;\n    }\n\n    options = defaults({}, options || {}, this.config());\n    let images = nodes\n      .filter(node=>/^img$/i.test(node.tagName))\n      .map(function(node){\n          let imgOptions = assign({\n            width: node.getAttribute('width'),\n            height: node.getAttribute('height'),\n            src: node.getAttribute('src')\n          }, options);\n          let publicId = imgOptions['source'] || imgOptions['src'];\n          delete imgOptions['source'];\n          delete imgOptions['src'];\n          let attr = new Transformation(imgOptions).toHtmlAttributes();\n          setData(node, 'src-cache', url(publicId, imgOptions));\n          node.setAttribute('width', attr.width);\n          node.setAttribute('height', attr.height);\n          return node;\n      });\n    this.cloudinary_update(images, options);\n    return this;\n  }\n\n  /**\n  * Updates the dpr (for `dpr_auto`) and responsive (for `w_auto`) fields according to\n  *  the current container size and the device pixel ratio.<br/>\n  *  <b>Note</b>:`w_auto` is updated only for images marked with the `cld-responsive`\n  *  (or other defined {@link Cloudinary#responsive|responsive}) class.\n  * @function Cloudinary#cloudinary_update\n  * @param {(Array|string|NodeList)} elements - The HTML image elements to modify.\n  * @param {Object} options\n  * @param {boolean|string} [options.responsive_use_breakpoints=true]\n  * Possible values:<br/>\n  *  - `true`: Always use breakpoints for width.<br/>\n  *  - `resize`: Use exact width on first render and breakpoints on resize.<br/>\n  *  - `false`: Always use exact width.\n  * @param {boolean} [options.responsive] - If `true`, enable responsive on all specified elements.\n  *  Alternatively, you can define specific HTML elements to modify by adding the `cld-responsive`\n  *  (or other custom-defined {@link Cloudinary#responsive|responsive_class}) class to those elements.\n  * @param {boolean} [options.responsive_preserve_height] - If `true`, original css height is preserved.\n  *  Should be used only if the transformation supports different aspect ratios.\n  */\n  cloudinary_update(elements, options) {\n    var containerWidth, dataSrc, match, ref4, requiredWidth;\n    if (elements === null) {\n      return this;\n    }\n    if(options == null) {\n      options = {};\n    }\n    const responsive = options.responsive != null ? options.responsive : this.config('responsive');\n\n    elements = normalizeToArray(elements);\n\n    let responsiveClass;\n    if (this.responsiveConfig && this.responsiveConfig.responsive_class != null) {\n      responsiveClass = this.responsiveConfig.responsive_class;\n    } else if (options.responsive_class != null) {\n      responsiveClass = options.responsive_class;\n    } else {\n      responsiveClass = this.config('responsive_class');\n    }\n\n    let roundDpr = options.round_dpr != null ? options.round_dpr : this.config('round_dpr');\n    elements.forEach(tag => {\n      if (/img/i.test(tag.tagName)) {\n        let setUrl = true;\n        if (responsive) {\n          addClass(tag, responsiveClass);\n        }\n        dataSrc = getData(tag, 'src-cache') || getData(tag, 'src');\n        if (!isEmpty(dataSrc)) {\n          // Update dpr according to the device's devicePixelRatio\n          dataSrc = updateDpr.call(this, dataSrc, roundDpr);\n          if (HtmlTag.isResponsive(tag, responsiveClass)) {\n            containerWidth = findContainerWidth(tag);\n            if (containerWidth !== 0) {\n              if (/w_auto:breakpoints/.test(dataSrc)) {\n                requiredWidth = maxWidth(containerWidth, tag);\n                if (requiredWidth) {\n                  dataSrc = dataSrc.replace(/w_auto:breakpoints([_0-9]*)(:[0-9]+)?/, `w_auto:breakpoints$1:${requiredWidth}`);\n                } else {\n                  setUrl = false;\n                }\n              } else {\n                match = /w_auto(:(\\d+))?/.exec(dataSrc);\n                if (match) {\n                  requiredWidth = applyBreakpoints.call(this, tag, containerWidth, match[2], options);\n                  requiredWidth = maxWidth(requiredWidth, tag)\n                  if (requiredWidth) {\n                    dataSrc = dataSrc.replace(/w_auto[^,\\/]*/g, `w_${requiredWidth}`);\n                  } else {\n                    setUrl = false;\n                  }\n                }\n              }\n              removeAttribute(tag, 'width');\n              if (!options.responsive_preserve_height) {\n                removeAttribute(tag, 'height');\n              }\n            } else {\n              // Container doesn't know the size yet - usually because the image is hidden or outside the DOM.\n              setUrl = false;\n            }\n          }\n          const isLazyLoading = (options.loading === 'lazy' && !this.isNativeLazyLoadSupported() && this.isLazyLoadSupported() && !elements[0].getAttribute('src'));\n          if (setUrl || isLazyLoading){\n            // If data-width exists, set width to be data-width\n            this.setAttributeIfExists(elements[0], 'width', 'data-width');\n          }\n\n          if (setUrl && !isLazyLoading) {\n            setAttribute(tag, 'src', dataSrc);\n          }\n        }\n      }\n    });\n    return this;\n  }\n\n  /**\n   * Sets element[toAttribute] = element[fromAttribute] if element[fromAttribute] is set\n   * @param element\n   * @param toAttribute\n   * @param fromAttribute\n   */\n  setAttributeIfExists(element, toAttribute, fromAttribute){\n    const attributeValue = element.getAttribute(fromAttribute);\n    if (attributeValue != null) {\n      setAttribute(element, toAttribute, attributeValue);\n    }\n  }\n\n  /**\n   * Returns true if Intersection Observer API is supported\n   * @returns {boolean}\n   */\n  isLazyLoadSupported() {\n    return window && 'IntersectionObserver' in window;\n  }\n\n  /**\n   * Returns true if using Chrome\n   * @returns {boolean}\n   */\n  isNativeLazyLoadSupported() {\n    return 'loading' in HTMLImageElement.prototype;\n  }\n\n  /**\n   * Returns a {@link Transformation} object, initialized with the specified options, for chaining purposes.\n   * @function Cloudinary#transformation\n   * @param {Object} options The {@link Transformation} options to apply.\n   * @return {Transformation}\n   * @see Transformation\n   * @see <a href=\"https://cloudinary.com/documentation/image_transformation_reference\" target=\"_blank\">\n   *  Available image transformations</a>\n   * @see <a href=\"https://cloudinary.com/documentation/video_transformation_reference\" target=\"_blank\">\n   *  Available video transformations</a>\n   */\n  transformation(options) {\n    return Transformation.new(this.config()).fromOptions(options).setParent(this);\n  }\n\n\n  /**\n   * @description This function will append a TransparentVideo element to the htmlElContainer passed to it.\n   *              TransparentVideo can either be an HTML Video tag, or an HTML Canvas Tag.\n   * @param {HTMLElement} htmlElContainer\n   * @param {string} publicId\n   * @param {object} options The {@link TransparentVideoOptions} options to apply - Extends TransformationOptions\n   *                 options.playsinline    - HTML Video Tag's native playsinline - passed to video element.\n   *                 options.poster         - HTML Video Tag's native poster - passed to video element.\n   *                 options.loop           - HTML Video Tag's native loop - passed to video element.\n   *                 options?.externalLibraries = { [key: string]: string} - map of external libraries to be loaded\n   * @return {Promise<HTMLElement | {status:string, message:string}>}\n   */\n  injectTransparentVideoElement(htmlElContainer, publicId, options = {}) {\n    return new Promise((resolve, reject) => {\n      if (!htmlElContainer) {\n        reject({status: 'error', message: 'Expecting htmlElContainer to be HTMLElement'});\n      }\n\n      enforceOptionsForTransparentVideo(options);\n\n      let videoURL = this.video_url(publicId, options);\n\n      checkSupportForTransparency().then((isNativelyTransparent) => {\n        let mountPromise;\n\n        if (isNativelyTransparent) {\n          mountPromise = mountCloudinaryVideoTag(htmlElContainer, this, publicId, options);\n          resolve(htmlElContainer);\n        } else {\n          mountPromise = mountSeeThruCanvasTag(htmlElContainer, videoURL, options);\n        }\n\n        mountPromise\n          .then(() => {\n          resolve(htmlElContainer);\n        }).catch(({status, message}) => { reject({status, message});});\n\n        // catch for checkSupportForTransparency()\n      }).catch(({status, message}) => { reject({status, message});});\n    });\n  }\n}\n\nassign(Cloudinary, constants);\nexport default Cloudinary;\n", "/**\n * Creates the namespace for Cloudinary\n */\nimport utf8_encode from '../utf8_encode';\nimport crc32 from '../crc32';\nimport * as Util from '../util';\nimport Transformation from '../transformation';\nimport Condition from '../condition';\nimport Configuration from '../configuration';\nimport Expression from \"../expression\";\nimport HtmlTag from '../tags/htmltag';\nimport ImageTag from '../tags/imagetag';\nimport PictureTag from '../tags/picturetag';\nimport VideoTag from '../tags/videotag';\nimport ClientHintsMetaTag from '../tags/clienthintsmetatag';\nimport Layer from '../layer/layer';\nimport FetchLayer from '../layer/fetchlayer';\nimport TextLayer from '../layer/textlayer';\nimport SubtitlesLayer from '../layer/subtitleslayer';\nimport Cloudinary from '../cloudinary';\n\nexport default {\n  ClientHintsMetaTag,\n  Cloudinary,\n  Condition,\n  Configuration,\n  crc32,\n  Expression,\n  FetchLayer,\n  HtmlTag,\n  ImageTag,\n  Layer,\n  PictureTag,\n  SubtitlesLayer,\n  TextLayer,\n  Transformation,\n  utf8_encode,\n  Util,\n  VideoTag\n};\n\nexport {\n  ClientHintsMetaTag,\n  Cloudinary,\n  Condition,\n  Configuration,\n  crc32,\n  Expression,\n  FetchLayer,\n  HtmlTag,\n  ImageTag,\n  Layer,\n  PictureTag,\n  SubtitlesLayer,\n  TextLayer,\n  Transformation,\n  utf8_encode,\n  Util,\n  VideoTag\n};\n", "module.exports = __WEBPACK_EXTERNAL_MODULE_lodash_assign__;", "module.exports = __WEBPACK_EXTERNAL_MODULE_lodash_cloneDeep__;", "module.exports = __WEBPACK_EXTERNAL_MODULE_lodash_compact__;", "module.exports = __WEBPACK_EXTERNAL_MODULE_lodash_difference__;", "module.exports = __WEBPACK_EXTERNAL_MODULE_lodash_functions__;", "module.exports = __WEBPACK_EXTERNAL_MODULE_lodash_identity__;", "module.exports = __WEBPACK_EXTERNAL_MODULE_lodash_includes__;", "module.exports = __WEBPACK_EXTERNAL_MODULE_lodash_isArray__;", "module.exports = __WEBPACK_EXTERNAL_MODULE_lodash_isElement__;", "module.exports = __WEBPACK_EXTERNAL_MODULE_lodash_isFunction__;", "module.exports = __WEBPACK_EXTERNAL_MODULE_lodash_isPlainObject__;", "module.exports = __WEBPACK_EXTERNAL_MODULE_lodash_isString__;", "module.exports = __WEBPACK_EXTERNAL_MODULE_lodash_merge__;", "module.exports = __WEBPACK_EXTERNAL_MODULE_lodash_trim__;"], "sourceRoot": ""}
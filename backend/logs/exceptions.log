{"date":"Thu Jun 05 2025 16:02:31 GMT+0530 (India Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: nodemailer.createTransporter is not a function\nTypeError: nodemailer.createTransporter is not a function\n    at new EmailService (/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js:6:35)\n    at Object.<anonymous> (/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js:187:18)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)","os":{"loadavg":[2.12890625,2.61328125,3.09423828125],"uptime":18608},"process":{"argv":["/usr/local/bin/node","/Users/<USER>/Fashion E-Commerce/backend/server.js"],"cwd":"/Users/<USER>/Fashion E-Commerce/backend","execPath":"/usr/local/bin/node","gid":20,"memoryUsage":{"arrayBuffers":18264303,"external":20541489,"heapTotal":59031552,"heapUsed":29036496,"rss":92160000},"pid":8857,"uid":501,"version":"v22.15.0"},"service":"fashion-ecommerce-api","stack":"TypeError: nodemailer.createTransporter is not a function\n    at new EmailService (/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js:6:35)\n    at Object.<anonymous> (/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js:187:18)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)","timestamp":"2025-06-05 16:02:31","trace":[{"column":35,"file":"/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js","function":"new EmailService","line":6,"method":null,"native":false},{"column":18,"file":"/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js","function":null,"line":187,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1730,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1895,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":235,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1487,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":135,"method":null,"native":false}]}
{"date":"Thu Jun 05 2025 16:05:13 GMT+0530 (India Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: nodemailer.createTransporter is not a function\nTypeError: nodemailer.createTransporter is not a function\n    at new EmailService (/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js:6:35)\n    at Object.<anonymous> (/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js:187:18)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)","os":{"loadavg":[2.88330078125,2.5986328125,2.98876953125],"uptime":18770},"process":{"argv":["/usr/local/bin/node","/Users/<USER>/Fashion E-Commerce/backend/server.js"],"cwd":"/Users/<USER>/Fashion E-Commerce/backend","execPath":"/usr/local/bin/node","gid":20,"memoryUsage":{"arrayBuffers":18264304,"external":20541489,"heapTotal":57982976,"heapUsed":30709424,"rss":91734016},"pid":8934,"uid":501,"version":"v22.15.0"},"service":"fashion-ecommerce-api","stack":"TypeError: nodemailer.createTransporter is not a function\n    at new EmailService (/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js:6:35)\n    at Object.<anonymous> (/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js:187:18)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)","timestamp":"2025-06-05 16:05:13","trace":[{"column":35,"file":"/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js","function":"new EmailService","line":6,"method":null,"native":false},{"column":18,"file":"/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js","function":null,"line":187,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1730,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1895,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":235,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1487,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":135,"method":null,"native":false}]}
{"date":"Thu Jun 05 2025 16:07:35 GMT+0530 (India Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: nodemailer.createTransporter is not a function\nTypeError: nodemailer.createTransporter is not a function\n    at new EmailService (/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js:6:35)\n    at Object.<anonymous> (/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js:187:18)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)","os":{"loadavg":[1.91943359375,2.2919921875,2.7998046875],"uptime":18912},"process":{"argv":["/usr/local/bin/node","/Users/<USER>/Fashion E-Commerce/backend/server.js"],"cwd":"/Users/<USER>/Fashion E-Commerce/backend","execPath":"/usr/local/bin/node","gid":20,"memoryUsage":{"arrayBuffers":18264303,"external":20541489,"heapTotal":59031552,"heapUsed":29088408,"rss":92585984},"pid":8959,"uid":501,"version":"v22.15.0"},"service":"fashion-ecommerce-api","stack":"TypeError: nodemailer.createTransporter is not a function\n    at new EmailService (/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js:6:35)\n    at Object.<anonymous> (/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js:187:18)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)","timestamp":"2025-06-05 16:07:35","trace":[{"column":35,"file":"/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js","function":"new EmailService","line":6,"method":null,"native":false},{"column":18,"file":"/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js","function":null,"line":187,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1730,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1895,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":235,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1487,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":135,"method":null,"native":false}]}
{"date":"Thu Jun 05 2025 16:13:43 GMT+0530 (India Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: nodemailer.createTransporter is not a function\nTypeError: nodemailer.createTransporter is not a function\n    at new EmailService (/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js:6:35)\n    at Object.<anonymous> (/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js:187:18)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)","os":{"loadavg":[2.19189453125,2.3671875,2.66455078125],"uptime":19280},"process":{"argv":["/usr/local/bin/node","/Users/<USER>/Fashion E-Commerce/backend/server.js"],"cwd":"/Users/<USER>/Fashion E-Commerce/backend","execPath":"/usr/local/bin/node","gid":20,"memoryUsage":{"arrayBuffers":18264303,"external":20541489,"heapTotal":59031552,"heapUsed":29456688,"rss":92225536},"pid":9085,"uid":501,"version":"v22.15.0"},"service":"fashion-ecommerce-api","stack":"TypeError: nodemailer.createTransporter is not a function\n    at new EmailService (/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js:6:35)\n    at Object.<anonymous> (/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js:187:18)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)","timestamp":"2025-06-05 16:13:43","trace":[{"column":35,"file":"/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js","function":"new EmailService","line":6,"method":null,"native":false},{"column":18,"file":"/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js","function":null,"line":187,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1730,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1895,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":235,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1487,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":135,"method":null,"native":false}]}
{"date":"Thu Jun 05 2025 16:26:22 GMT+0530 (India Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: nodemailer.createTransporter is not a function\nTypeError: nodemailer.createTransporter is not a function\n    at new EmailService (/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js:6:35)\n    at Object.<anonymous> (/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js:187:18)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)","os":{"loadavg":[3.3515625,3.7734375,3.14501953125],"uptime":20039},"process":{"argv":["/usr/local/bin/node","/Users/<USER>/Fashion E-Commerce/backend/server.js"],"cwd":"/Users/<USER>/Fashion E-Commerce","execPath":"/usr/local/bin/node","gid":20,"memoryUsage":{"arrayBuffers":18264303,"external":20541489,"heapTotal":59293696,"heapUsed":29402360,"rss":92127232},"pid":9648,"uid":501,"version":"v22.15.0"},"service":"fashion-ecommerce-api","stack":"TypeError: nodemailer.createTransporter is not a function\n    at new EmailService (/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js:6:35)\n    at Object.<anonymous> (/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js:187:18)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)","timestamp":"2025-06-05 16:26:22","trace":[{"column":35,"file":"/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js","function":"new EmailService","line":6,"method":null,"native":false},{"column":18,"file":"/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js","function":null,"line":187,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1730,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1895,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":235,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1487,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":135,"method":null,"native":false}]}
{"date":"Thu Jun 05 2025 16:33:35 GMT+0530 (India Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: nodemailer.createTransporter is not a function\nTypeError: nodemailer.createTransporter is not a function\n    at new EmailService (/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js:6:35)\n    at Object.<anonymous> (/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js:187:18)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)","os":{"loadavg":[2.8134765625,2.81005859375,2.84765625],"uptime":20472},"process":{"argv":["/usr/local/bin/node","/Users/<USER>/Fashion E-Commerce/backend/server.js"],"cwd":"/Users/<USER>/Fashion E-Commerce/backend","execPath":"/usr/local/bin/node","gid":20,"memoryUsage":{"arrayBuffers":18264303,"external":20541489,"heapTotal":39108608,"heapUsed":26458464,"rss":90079232},"pid":9833,"uid":501,"version":"v22.15.0"},"service":"fashion-ecommerce-api","stack":"TypeError: nodemailer.createTransporter is not a function\n    at new EmailService (/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js:6:35)\n    at Object.<anonymous> (/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js:187:18)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1487:12)\n    at require (node:internal/modules/helpers:135:16)","timestamp":"2025-06-05 16:33:35","trace":[{"column":35,"file":"/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js","function":"new EmailService","line":6,"method":null,"native":false},{"column":18,"file":"/Users/<USER>/Fashion E-Commerce/backend/services/emailService.js","function":null,"line":187,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1730,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1895,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":235,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1487,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":135,"method":null,"native":false}]}
